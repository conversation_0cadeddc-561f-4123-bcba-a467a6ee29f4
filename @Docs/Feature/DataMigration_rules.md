# 数据迁移功能设计文档

## 功能概述

本功能实现了支持131万条记录的大规模数据迁移，具备分批处理和断点续传能力，确保在网络中断或系统故障时能够从上次处理位置继续执行。

## 设计思路

### 1. 核心设计原则

- **可恢复性**: 每批处理完成后保存进度，支持从任意断点恢复
- **幂等性**: 支持重复执行而不产生副作用
- **监控性**: 提供详细的进度和性能指标
- **容错性**: 单条记录失败不影响整批处理
- **内存安全**: 实时监控内存使用，防止OOM

### 2. 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Layer     │    │  Application    │    │   Domain        │
│                 │    │     Layer       │    │    Layer        │
│ MigrationCtrl   │───▶│ DataMigration   │───▶│ BatchMigration  │
│                 │    │    AppSvc       │    │     DmSvc       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Infrastructure  │    │   Repository    │
                       │     Layer       │    │    Interface    │
                       │                 │    │                 │
                       │ MigrationRepo   │◀───│ MigrationData   │
                       │    Impl         │    │   Repository    │
                       └─────────────────┘    └─────────────────┘
```

### 3. 分批处理策略

#### 内存计算
- **131万条长度50字符串内存估算**:
  - 每个String: ~50 * 2 bytes (UTF-16) + 对象头(24 bytes) ≈ 124 bytes
  - 131万条: 131万 × 124 bytes ≈ 162.44 MB
  - 建议预留: **300-400 MB** 内存

#### 批次配置
- **默认配置**: 公司页大小10，用户页大小1000
- **高性能配置**: 公司页大小20，用户页大小2000
- **保守配置**: 公司页大小5，用户页大小200

## 实现细节

### 1. 核心实体类

#### OptimizedBitmapProgress
- 优化的位图进度管理器，支持序列化到文件
- 包含状态管理、计数统计、位置跟踪、位图优化
- 支持原子操作、线程安全、断点恢复、失败跟踪

#### MigrationBatch
- 数据批次实体，表示一批需要迁移的记录
- 支持批次构建器模式
- 包含批次验证和统计功能

#### MigrationRecord
- 单条迁移记录实体
- 支持状态流转和重试机制
- 包含数据完整性校验

### 2. 服务层设计

#### DataMigrationAppSvc
- 应用服务层，提供迁移任务的生命周期管理
- 支持启动、暂停、恢复、状态查询
- 异步执行迁移任务

#### BatchMigrationDmSvc
- 领域服务层，实现具体的分批处理逻辑
- 内存监控和阈值控制
- 断点续传和错误处理

### 3. 仓储接口

#### MigrationProgressRepository
- 迁移进度持久化接口
- 支持文件系统备份和恢复
- 提供统计和查询功能

#### MigrationDataRepository
- 迁移数据访问接口
- 分页查询和批量处理
- 数据完整性验证

## 断点续传机制

### 1. 进度保存策略
- **自动保存**: 每处理N条记录自动保存进度
- **定时保存**: 每隔一定时间保存进度
- **状态变更保存**: 状态变更时立即保存

### 2. 恢复机制
- **位置恢复**: 从上次处理的公司和用户页码继续
- **状态验证**: 验证数据一致性和完整性
- **重试处理**: 对失败记录进行重试

### 3. 数据一致性
- **幂等性保证**: 重复执行不会产生重复数据
- **事务控制**: 批次级别的事务管理
- **校验机制**: 数据校验和完整性检查

## 性能优化

### 1. 内存优化
- **分批加载**: 避免一次性加载大量数据
- **内存监控**: 实时监控JVM内存使用
- **垃圾回收**: 及时释放不需要的对象引用

### 2. 并发优化
- **异步执行**: 使用专用线程池执行迁移任务
- **批量处理**: 批量数据库操作减少网络开销
- **连接池**: 合理配置数据库连接池

### 3. 网络优化
- **批次间隔**: 控制批次处理间隔避免过载
- **重试机制**: 网络异常时的重试策略
- **超时控制**: 合理设置各种超时时间

## 监控和告警

### 1. 进度监控
- **实时进度**: 处理进度百分比
- **速度监控**: 平均处理速度（记录/秒）
- **剩余时间**: 预估完成时间

### 2. 性能监控
- **内存使用**: JVM内存使用情况
- **CPU使用**: 处理器使用率
- **数据库性能**: 查询和更新性能

### 3. 异常监控
- **失败率**: 处理失败率统计
- **错误类型**: 错误分类和统计
- **重试情况**: 重试次数和成功率

## API接口

### 1. 迁移管理
- `POST /api/migration/start` - 启动迁移任务
- `POST /api/migration/resume/{sessionId}` - 恢复迁移任务
- `POST /api/migration/pause/{sessionId}` - 暂停迁移任务

### 2. 状态查询
- `GET /api/migration/status/{sessionId}` - 获取迁移状态
- `GET /api/migration/progress/{sessionId}` - 获取详细进度
- `GET /api/migration/active` - 获取活跃任务列表

### 3. 模板和工具
- `GET /api/migration/template/default` - 默认配置模板
- `GET /api/migration/template/high-performance` - 高性能配置
- `GET /api/migration/template/conservative` - 保守配置
- `DELETE /api/migration/cleanup` - 清理已完成记录

## 配置参数

### 1. 批次配置
```java
// 公司分页大小
private int companyPageSize = 10;

// 用户分页大小  
private int userPageSize = 1000;

// 最大重试次数
private int maxRetryCount = 3;

// 批次处理间隔（毫秒）
private long batchIntervalMs = 50L;
```

### 2. 内存配置
```java
// 内存阈值（MB）
private int memoryThresholdMb = 300;

// 是否启用内存监控
private boolean enableMemoryMonitoring = true;

// 自动保存间隔
private int autoSaveInterval = 500;
```

### 3. 线程池配置
```java
// 迁移任务执行器
@Bean("migrationTaskExecutor")
ThreadPoolTaskExecutor migrationTaskExecutor() {
    executor.setCorePoolSize(2);
    executor.setMaxPoolSize(4);
    executor.setQueueCapacity(10);
}
```

## 使用示例

### 1. 启动迁移任务
```java
MigrationRequest request = MigrationRequest.createDefault("FINISHED", "admin");
String sessionId = dataMigrationAppSvc.startMigration(request);
```

### 2. 监控进度
```java
MigrationStatusResponse status = dataMigrationAppSvc.getMigrationStatus(sessionId);
System.out.println("进度: " + status.getProgressPercentage() + "%");
```

### 3. 恢复中断任务
```java
boolean resumed = dataMigrationAppSvc.resumeMigration(sessionId);
```

## 注意事项

### 1. 部署要求
- JVM内存建议至少1GB
- 数据库连接池配置合理
- 磁盘空间充足（用于进度文件）

### 2. 运维建议
- 定期清理已完成的迁移记录
- 监控系统资源使用情况
- 备份重要的进度文件

### 3. 故障处理
- 网络中断：自动重试和恢复机制
- 内存不足：降低批次大小
- 数据库异常：检查连接和权限

## 扩展性

### 1. 支持多种数据源
- 可扩展支持不同类型的数据迁移
- 插件化的数据处理器

### 2. 分布式支持
- 可扩展为分布式迁移
- 支持多节点并行处理

### 3. 自定义处理逻辑
- 支持自定义数据转换逻辑
- 可配置的验证规则
