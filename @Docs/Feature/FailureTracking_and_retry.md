# 失败记录跟踪和重试功能使用指南

## 概述

基于优化迁移方案，我们新增了完整的失败记录跟踪和精准重试功能，让您能够：

- 🎯 **快速定位失败记录**：根据用户ID、公司ID、错误类型快速查找
- 🔄 **精准重试**：仅对失败的记录进行重试，不重复处理成功的记录
- 📊 **详细错误分析**：提供错误类型分类和统计信息
- 🔍 **实时监控**：实时跟踪重试进度和结果

## 核心功能

### 1. 失败记录自动跟踪

在迁移过程中，系统会自动记录每个失败的用户记录：

```java
// 失败记录包含的信息
public class FailureRecord {
    private String userId;          // 用户ID
    private String companyId;       // 公司ID
    private long globalIndex;       // 全局索引位置
    private String errorMessage;    // 详细错误信息
    private String errorType;       // 错误类型分类
    private LocalDateTime failureTime;    // 失败时间
    private int retryCount;         // 重试次数
    private LocalDateTime lastRetryTime;  // 最后重试时间
}
```

### 2. 错误类型自动分类

系统会自动将错误分类为以下类型：

- **DATABASE_ERROR**: 数据库相关错误
- **TIMEOUT_ERROR**: 超时错误
- **NULL_POINTER_ERROR**: 空指针异常
- **INVALID_ARGUMENT_ERROR**: 参数错误
- **BUSINESS_LOGIC_ERROR**: 业务逻辑错误
- **UNKNOWN_ERROR**: 未知错误

## API使用指南

### 1. 获取失败记录列表

```bash
# 获取所有失败记录
curl "http://localhost:8080/perf/task/getFailureRecords?sessionId={sessionId}"

# 按公司ID过滤
curl "http://localhost:8080/perf/task/getFailureRecords?sessionId={sessionId}&companyId=COMPANY_123"

# 按错误类型过滤
curl "http://localhost:8080/perf/task/getFailureRecords?sessionId={sessionId}&errorType=DATABASE_ERROR"

# 组合过滤
curl "http://localhost:8080/perf/task/getFailureRecords?sessionId={sessionId}&companyId=COMPANY_123&errorType=DATABASE_ERROR"
```

**响应示例**：
```json
{
  "success": true,
  "data": [
    {
      "userId": "USER_12345",
      "companyId": "COMPANY_123",
      "globalIndex": 567890,
      "errorMessage": "Connection timeout",
      "errorType": "TIMEOUT_ERROR",
      "failureTime": "2025-06-29T14:30:15",
      "retryCount": 0,
      "lastRetryTime": null
    }
  ]
}
```

### 2. 快速定位特定用户的失败记录

```bash
# 根据用户ID快速定位
curl "http://localhost:8080/perf/task/getFailureRecordByUserId?sessionId={sessionId}&userId=USER_12345"
```

### 3. 获取失败记录统计信息

```bash
# 获取详细统计
curl "http://localhost:8080/perf/task/getFailureStatistics?sessionId={sessionId}"
```

**响应示例**：
```
失败记录统计:
总失败数: 156
按错误类型分组: {DATABASE_ERROR=89, TIMEOUT_ERROR=45, BUSINESS_LOGIC_ERROR=22}
按公司分组: {COMPANY_123=67, COMPANY_456=89}
```

### 4. 启动失败记录重试

#### 4.1 重试所有失败记录

```bash
# 重试所有失败记录
curl "http://localhost:8080/perf/task/startFailureRetry?sessionId={sessionId}"
```

#### 4.2 重试指定用户

```bash
# 重试单个用户
curl "http://localhost:8080/perf/task/retrySpecificUser?sessionId={sessionId}&userId=USER_12345"

# 重试多个用户（用逗号分隔）
curl "http://localhost:8080/perf/task/startFailureRetry?sessionId={sessionId}&userIds=USER_12345,USER_67890,USER_11111"
```

**响应示例**：
```json
{
  "success": true,
  "data": "RETRY_OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4_1735462800000",
  "errCode": null,
  "errMessage": null
}
```

### 5. 按条件查询失败记录

#### 5.1 按公司ID查询

```bash
curl "http://localhost:8080/perf/task/getFailureRecordsByCompany?sessionId={sessionId}&companyId=COMPANY_123"
```

#### 5.2 按错误类型查询

```bash
curl "http://localhost:8080/perf/task/getFailureRecordsByErrorType?sessionId={sessionId}&errorType=DATABASE_ERROR"
```

## 使用场景示例

### 场景1：快速定位和修复特定用户问题

```bash
# 1. 用户反馈USER_12345迁移失败，快速定位
curl "http://localhost:8080/perf/task/getFailureRecordByUserId?sessionId=SESSION_123&userId=USER_12345"

# 2. 查看错误信息，发现是数据库连接超时
# 3. 修复数据库连接问题后，重试该用户
curl "http://localhost:8080/perf/task/retrySpecificUser?sessionId=SESSION_123&userId=USER_12345"

# 4. 监控重试进度
curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId=RETRY_SESSION_456"
```

### 场景2：批量处理特定类型的错误

```bash
# 1. 查看失败统计，发现大量数据库错误
curl "http://localhost:8080/perf/task/getFailureStatistics?sessionId=SESSION_123"

# 2. 获取所有数据库错误的记录
curl "http://localhost:8080/perf/task/getFailureRecordsByErrorType?sessionId=SESSION_123&errorType=DATABASE_ERROR"

# 3. 修复数据库问题后，重试所有数据库错误的用户
# 从响应中提取所有userId，然后批量重试
curl "http://localhost:8080/perf/task/startFailureRetry?sessionId=SESSION_123&userIds=USER_001,USER_002,USER_003"
```

### 场景3：公司级别的问题处理

```bash
# 1. 某个公司的数据有问题，查看该公司的所有失败记录
curl "http://localhost:8080/perf/task/getFailureRecordsByCompany?sessionId=SESSION_123&companyId=COMPANY_456"

# 2. 修复该公司的数据问题后，重试该公司的所有失败记录
# 从响应中提取该公司的所有userId，然后批量重试
curl "http://localhost:8080/perf/task/startFailureRetry?sessionId=SESSION_123&userIds=USER_A,USER_B,USER_C"
```

## 代码集成示例

### 1. 在业务代码中使用

```java
@Service
public class MigrationManagementService {
    
    @Autowired
    private ScorerDataMingrationAppSvc migrationAppSvc;
    
    /**
     * 处理迁移失败的用户
     */
    public void handleFailedUsers(String sessionId) {
        // 1. 获取所有失败记录
        List<OptimizedBitmapProgress.FailureRecord> failureRecords = 
                migrationAppSvc.getFailureRecords(sessionId, null, null);
        
        // 2. 按错误类型分组处理
        Map<String, List<OptimizedBitmapProgress.FailureRecord>> groupedByErrorType = 
                failureRecords.stream()
                        .collect(Collectors.groupingBy(OptimizedBitmapProgress.FailureRecord::getErrorType));
        
        // 3. 针对不同错误类型采取不同处理策略
        for (Map.Entry<String, List<OptimizedBitmapProgress.FailureRecord>> entry : groupedByErrorType.entrySet()) {
            String errorType = entry.getKey();
            List<OptimizedBitmapProgress.FailureRecord> records = entry.getValue();
            
            switch (errorType) {
                case "DATABASE_ERROR":
                    handleDatabaseErrors(sessionId, records);
                    break;
                case "TIMEOUT_ERROR":
                    handleTimeoutErrors(sessionId, records);
                    break;
                default:
                    handleOtherErrors(sessionId, records);
                    break;
            }
        }
    }
    
    private void handleDatabaseErrors(String sessionId, List<OptimizedBitmapProgress.FailureRecord> records) {
        // 处理数据库错误：可能需要重新配置连接池、检查数据库状态等
        log.info("处理数据库错误: {} 条记录", records.size());
        
        // 修复问题后重试
        List<String> userIds = records.stream()
                .map(OptimizedBitmapProgress.FailureRecord::getUserId)
                .collect(Collectors.toList());
        
        String retrySessionId = migrationAppSvc.startFailureRetry(sessionId, userIds);
        log.info("数据库错误重试启动: {}", retrySessionId);
    }
}
```

### 2. 定时任务自动重试

```java
@Component
public class AutoRetryScheduler {
    
    @Autowired
    private ScorerDataMingrationAppSvc migrationAppSvc;
    
    /**
     * 每小时自动重试超时错误
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void autoRetryTimeoutErrors() {
        // 获取所有活跃的迁移会话
        List<String> activeSessions = getActiveMigrationSessions();
        
        for (String sessionId : activeSessions) {
            try {
                // 获取超时错误的记录
                List<OptimizedBitmapProgress.FailureRecord> timeoutRecords = 
                        migrationAppSvc.getFailureRecords(sessionId, null, "TIMEOUT_ERROR");
                
                if (!timeoutRecords.isEmpty()) {
                    // 过滤出重试次数少于3次的记录
                    List<String> retryUserIds = timeoutRecords.stream()
                            .filter(record -> record.getRetryCount() < 3)
                            .map(OptimizedBitmapProgress.FailureRecord::getUserId)
                            .collect(Collectors.toList());
                    
                    if (!retryUserIds.isEmpty()) {
                        String retrySessionId = migrationAppSvc.startFailureRetry(sessionId, retryUserIds);
                        log.info("自动重试超时错误: sessionId={}, retrySessionId={}, count={}", 
                                sessionId, retrySessionId, retryUserIds.size());
                    }
                }
                
            } catch (Exception e) {
                log.error("自动重试失败: sessionId={}", sessionId, e);
            }
        }
    }
}
```

## 监控和告警

### 1. 失败率监控

```java
/**
 * 监控迁移失败率
 */
public void monitorFailureRate(String sessionId) {
    OptimizedBitmapProgress progress = migrationAppSvc.getOptimizedMigrationStatus(sessionId);
    
    if (progress != null) {
        long totalProcessed = progress.getProcessedCount().get();
        long failureCount = progress.getFailureCount().get();
        
        if (totalProcessed > 0) {
            double failureRate = (double) failureCount / totalProcessed * 100;
            
            if (failureRate > 5.0) { // 失败率超过5%
                // 发送告警
                sendFailureRateAlert(sessionId, failureRate);
            }
        }
    }
}
```

### 2. 错误类型分析

```java
/**
 * 分析错误类型分布
 */
public void analyzeErrorTypes(String sessionId) {
    String statistics = migrationAppSvc.getFailureStatistics(sessionId);
    
    // 解析统计信息，识别主要错误类型
    // 根据错误类型采取相应的处理措施
}
```

## 最佳实践

### 1. 错误处理策略

- **数据库错误**: 检查连接池配置，考虑增加重试间隔
- **超时错误**: 检查网络状况，考虑增加超时时间
- **业务逻辑错误**: 检查数据完整性，可能需要数据修复
- **空指针错误**: 检查数据验证逻辑

### 2. 重试策略

- **立即重试**: 适用于网络抖动等临时性问题
- **延迟重试**: 适用于系统负载过高的情况
- **人工介入**: 适用于数据问题需要修复的情况

### 3. 监控建议

- 设置失败率阈值告警（建议5%）
- 监控特定错误类型的趋势
- 定期清理已解决的失败记录

## 注意事项

1. **重试会话管理**: 重试会创建新的会话ID，注意区分原始会话和重试会话
2. **并发控制**: 避免同时对同一批用户启动多个重试任务
3. **资源管理**: 大量重试可能影响系统性能，建议分批进行
4. **数据一致性**: 重试成功后会自动从失败记录中移除，确保不重复处理

通过这套完整的失败记录跟踪和重试机制，您可以快速定位问题用户，精准修复问题，大大提高迁移任务的成功率和运维效率。
