# 状态变化检测和跨任务执行详细流程

## 🔍 状态变化检测机制详解

### 1. **检测触发时机**

```java
// 在迁移执行循环中，每处理一页公司数据时检查
for (int companyPage = startCompanyPage; ; companyPage++) {
    // 检查是否需要暂停
    if (shouldPauseOptimized(progress)) {
        break;
    }
    
    // 🔍 关键：检查是否需要进行增量检测
    if (progress.shouldPerformIncrementalCheck()) {
        log.info("Performing incremental detection check at company page: {}", companyPage);
        performIncrementalDetection(progress);  // 执行检测
        progress.updateLastIncrementalCheckTime();
    }
    
    // 继续处理当前页的公司数据...
}
```

**检测时机判断逻辑**：
```java
public boolean shouldPerformIncrementalCheck() {
    if (!enableIncrementalDetection || lastIncrementalCheckTime == null) {
        return false;
    }
    
    // 计算下次检测时间 = 上次检测时间 + 检测间隔
    LocalDateTime nextCheckTime = lastIncrementalCheckTime.plusMinutes(incrementalCheckIntervalMinutes);
    return LocalDateTime.now().isAfter(nextCheckTime);  // 当前时间超过下次检测时间
}
```

### 2. **状态变化检测详细流程**

#### 2.1 检测流程概览
```
┌─────────────────┐
│  触发检测       │
│ (每30分钟)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 获取已处理的    │
│ 公司列表        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 遍历每个公司    │
│ 检测状态变化    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 记录状态变化    │
│ 并分类处理      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 判断是否需要    │
│ 跨任务处理      │
└─────────────────┘
```

#### 2.2 具体检测代码实现

```java
private void performIncrementalDetection(OptimizedBitmapProgress progress) {
    log.info("Starting incremental detection for session: {}", progress.getSessionId());
    
    try {
        String migrationType = progress.getMigrationType();
        
        // 🔍 步骤1：获取已处理的公司列表
        List<String> processedCompanies = getProcessedCompanies(progress);
        
        // 🔍 步骤2：遍历每个公司，检测状态变化
        for (String companyId : processedCompanies) {
            detectCompanyStatusChanges(progress, companyId, migrationType);
        }
        
        // 🔍 步骤3：处理检测到的状态变化
        processDetectedStatusChanges(progress);
        
        log.info("Incremental detection completed for session: {}, detected changes: {}", 
                progress.getSessionId(), progress.getAllStatusChangeRecords().size());
                
    } catch (Exception e) {
        log.error("Failed to perform incremental detection for session: {}", progress.getSessionId(), e);
    }
}
```

#### 2.3 单个公司的状态变化检测

```java
private void detectCompanyStatusChanges(OptimizedBitmapProgress progress, String companyId, String originalMigrationType) {
    log.debug("Detecting status changes for company: {}", companyId);
    
    try {
        // 🔍 步骤1：获取该公司已处理的用户列表
        List<String> processedUsers = getProcessedUsersForCompany(progress, companyId);
        
        // 🔍 步骤2：逐个检测用户状态变化
        for (String userId : processedUsers) {
            // 🔍 关键：检测用户当前状态
            String currentStatus = detectUserCurrentStatus(companyId, userId);
            
            // 🔍 步骤3：对比原始状态和当前状态
            if (currentStatus != null && !originalMigrationType.equals(currentStatus)) {
                // 🔍 步骤4：记录状态变化
                progress.recordStatusChange(userId, companyId, originalMigrationType, currentStatus);
                
                log.info("Detected status change: companyId={}, userId={}, {}->{}",
                        companyId, userId, originalMigrationType, currentStatus);
            }
        }
        
    } catch (Exception e) {
        log.error("Failed to detect status changes for company: {}", companyId, e);
    }
}
```

### 3. **用户状态检测的核心逻辑**

#### 3.1 状态检测方法（需要您自定义实现）

```java
/**
 * 检测用户当前状态
 * 🔧 TODO: 请根据您的实际业务逻辑实现此方法
 */
private String detectUserCurrentStatus(String companyId, String userId) {
    try {
        // 🔍 方案1：查询数据库获取用户当前状态
        // EvalUser evalUser = evalUserService.getEvalUser(companyId, userId);
        // return evalUser.isFinished() ? "FINISHED" : "NO_FINISHED";
        
        // 🔍 方案2：调用业务服务检测状态
        // UserStatusDto status = userStatusService.getCurrentStatus(companyId, userId);
        // return status.isCompleted() ? "FINISHED" : "NO_FINISHED";
        
        // 🔍 方案3：通过任务状态判断
        // TaskStatus taskStatus = taskService.getTaskStatus(companyId, userId);
        // return taskStatus == TaskStatus.COMPLETED ? "FINISHED" : "NO_FINISHED";
        
        // 临时模拟实现（10%概率状态发生变化）
        if (Math.random() < 0.1) {
            return Math.random() < 0.5 ? "FINISHED" : "NO_FINISHED";
        }
        
        return null; // 无变化
        
    } catch (Exception e) {
        log.error("Failed to detect current status for user: companyId={}, userId={}", companyId, userId, e);
        return null;
    }
}
```

#### 3.2 实际业务实现示例

```java
// 示例1：基于评估任务状态检测
private String detectUserCurrentStatus(String companyId, String userId) {
    try {
        // 查询用户的评估任务
        EvalTask evalTask = evalTaskService.getEvalTask(companyId, userId);
        if (evalTask == null) {
            return null; // 用户不存在评估任务
        }
        
        // 根据任务状态判断
        switch (evalTask.getStatus()) {
            case COMPLETED:
            case FINISHED:
                return "FINISHED";
            case IN_PROGRESS:
            case PENDING:
            case STARTED:
                return "NO_FINISHED";
            default:
                return null; // 状态未变化
        }
        
    } catch (Exception e) {
        log.error("Failed to detect status for user: companyId={}, userId={}", companyId, userId, e);
        return null;
    }
}

// 示例2：基于评分结果检测
private String detectUserCurrentStatus(String companyId, String userId) {
    try {
        // 查询用户的评分结果
        List<EvalScore> scores = evalScoreService.getUserScores(companyId, userId);
        
        if (scores.isEmpty()) {
            return "NO_FINISHED"; // 没有评分结果，未完成
        }
        
        // 检查是否所有必需的评分都已完成
        boolean allScoresCompleted = scores.stream()
                .allMatch(score -> score.getStatus() == ScoreStatus.COMPLETED);
        
        return allScoresCompleted ? "FINISHED" : "NO_FINISHED";
        
    } catch (Exception e) {
        log.error("Failed to detect status for user: companyId={}, userId={}", companyId, userId, e);
        return null;
    }
}
```

### 4. **分离式任务的智能检测**

#### 4.1 任务分离模式下的检测逻辑

```java
public boolean shouldDetectStatusChange(String fromStatus, String toStatus) {
    if (!enableTaskSeparation) {
        return true; // 混合模式检测所有变化
    }
    
    // 🔍 任务分离模式下，只检测跨任务的状态变化
    if (taskType == TaskType.FINISHED_ONLY) {
        // FINISHED任务只关心变为NO_FINISHED的数据
        return "FINISHED".equals(fromStatus) && "NO_FINISHED".equals(toStatus);
    } else if (taskType == TaskType.NO_FINISHED_ONLY) {
        // NO_FINISHED任务只关心变为FINISHED的数据
        return "NO_FINISHED".equals(fromStatus) && "FINISHED".equals(toStatus);
    }
    
    return false;
}
```

#### 4.2 检测效率优化

```
传统方案：
┌─────────────────┐
│ 检测所有变化    │
│ FINISHED→FINISHED │ ❌ 无意义检测
│ FINISHED→NO_FINISHED │ ✅ 有效检测
│ NO_FINISHED→FINISHED │ ✅ 有效检测  
│ NO_FINISHED→NO_FINISHED │ ❌ 无意义检测
└─────────────────┘
检测效率：50%

分离式方案：
┌─────────────────┐    ┌─────────────────┐
│ FINISHED任务    │    │ NO_FINISHED任务 │
│ 只检测→NO_FINISHED │    │ 只检测→FINISHED │
│ 检测效率：100%   │    │ 检测效率：100%  │
└─────────────────┘    └─────────────────┘
总体检测效率：100%，检测量减少50%
```

## 🔄 跨任务执行机制详解

### 1. **跨任务变化识别**

```java
/**
 * 检查状态变化是否需要跨任务处理
 */
public boolean needsCrossTaskProcessing(StatusChangeRecord changeRecord) {
    if (!enableTaskSeparation) {
        return false; // 非分离模式不需要跨任务处理
    }
    
    // 🔍 判断变化后的状态应该由哪个任务处理
    TaskType targetTaskType = getTargetTaskType(changeRecord.getCurrentStatus());
    return targetTaskType != this.taskType; // 目标任务类型与当前任务不同
}

public TaskType getTargetTaskType(String currentStatus) {
    if ("FINISHED".equals(currentStatus)) {
        return TaskType.FINISHED_ONLY;
    } else if ("NO_FINISHED".equals(currentStatus)) {
        return TaskType.NO_FINISHED_ONLY;
    }
    return TaskType.MIXED;
}
```

### 2. **跨任务处理流程**

#### 2.1 跨任务处理触发

```java
// 🔍 方式1：自动触发（在检测到跨任务变化时）
private void processDetectedStatusChanges(OptimizedBitmapProgress progress) {
    List<StatusChangeRecord> statusChanges = progress.getStatusChangesNeedingReprocessing();
    
    // 分离跨任务变化和任务内变化
    List<StatusChangeRecord> crossTaskChanges = statusChanges.stream()
            .filter(progress::needsCrossTaskProcessing)
            .collect(Collectors.toList());
    
    List<StatusChangeRecord> inTaskChanges = statusChanges.stream()
            .filter(record -> !progress.needsCrossTaskProcessing(record))
            .collect(Collectors.toList());
    
    // 🔍 处理任务内变化
    processInTaskChanges(inTaskChanges);
    
    // 🔍 如果有跨任务变化，启动跨任务处理
    if (!crossTaskChanges.isEmpty()) {
        String crossTaskSessionId = startCrossTaskReprocessing(progress.getSessionId());
        log.info("Auto started cross-task processing: {}", crossTaskSessionId);
    }
}

// 🔍 方式2：手动触发
curl "http://localhost:8080/perf/task/startCrossTaskReprocessing?sessionId={sessionId}"
```

#### 2.2 跨任务处理详细流程

```java
public String startCrossTaskReprocessing(String sourceSessionId) {
    log.info("Starting cross-task reprocessing for session: {}", sourceSessionId);
    
    try {
        // 🔍 步骤1：加载源任务进度
        OptimizedBitmapProgress sourceProgress = OptimizedBitmapProgress.loadMetadata(sourceSessionId);
        
        // 🔍 步骤2：获取需要跨任务处理的状态变化记录
        List<StatusChangeRecord> crossTaskChanges = sourceProgress.getCrossTaskStatusChanges();
        
        // 🔍 步骤3：获取目标任务会话ID
        String targetSessionId = sourceProgress.getRelatedTaskSessionId();
        
        // 🔍 步骤4：创建跨任务处理会话
        String crossTaskSessionId = generateCrossTaskSessionId(sourceSessionId, targetSessionId);
        OptimizedBitmapProgress crossTaskProgress = new OptimizedBitmapProgress(crossTaskSessionId, "CROSS_TASK");
        
        // 🔍 步骤5：异步执行跨任务处理
        executeCrossTaskReprocessingAsync(crossTaskSessionId, crossTaskChanges, sourceProgress, targetSessionId);
        
        return crossTaskSessionId;
        
    } catch (Exception e) {
        log.error("Failed to start cross-task reprocessing for session: {}", sourceSessionId, e);
        throw new RuntimeException("启动跨任务处理失败: " + e.getMessage(), e);
    }
}
```

#### 2.3 跨任务数据转移和处理

```java
public void executeCrossTaskReprocessingAsync(String crossTaskSessionId, 
                                             List<StatusChangeRecord> crossTaskChanges,
                                             OptimizedBitmapProgress sourceProgress,
                                             String targetSessionId) {
    try {
        OptimizedBitmapProgress targetProgress = OptimizedBitmapProgress.loadMetadata(targetSessionId);
        
        // 🔍 遍历每个跨任务变化记录
        for (int i = 0; i < crossTaskChanges.size(); i++) {
            StatusChangeRecord changeRecord = crossTaskChanges.get(i);
            
            try {
                // 🔍 步骤1：在目标任务中重新处理用户
                boolean success = reprocessStatusChangedUser(changeRecord);
                
                if (success) {
                    // 🔍 步骤2：标记源任务中的状态变化为已转移
                    sourceProgress.markCrossTaskChangeAsTransferred(changeRecord.getUserId());
                    
                    // 🔍 步骤3：在目标任务中记录处理成功
                    targetProgress.incrementSuccess();
                    
                    log.info("Cross-task reprocessing successful: userId={}, {}->{}",
                            changeRecord.getUserId(), changeRecord.getOriginalStatus(), changeRecord.getCurrentStatus());
                } else {
                    log.warn("Cross-task reprocessing failed: userId={}", changeRecord.getUserId());
                }
                
            } catch (Exception e) {
                log.error("Error in cross-task reprocessing: {}", changeRecord, e);
            }
        }
        
        // 🔍 步骤4：保存所有进度
        sourceProgress.saveMetadata();
        targetProgress.saveMetadata();
        
    } catch (Exception e) {
        log.error("Cross-task reprocessing failed: crossTaskSessionId={}", crossTaskSessionId, e);
    }
}
```

### 3. **跨任务处理示例场景**

#### 3.1 具体业务场景

```
初始状态：
┌─────────────────┐    ┌─────────────────┐
│ FINISHED任务    │    │ NO_FINISHED任务 │
│ 处理USER_A_001  │    │ 处理USER_B_001  │
│ 处理USER_A_002  │    │ 处理USER_B_002  │
│ 处理USER_A_003  │    │ 处理USER_B_003  │
└─────────────────┘    └─────────────────┘

状态变化检测：
🔍 FINISHED任务检测到：USER_A_002 从 FINISHED → NO_FINISHED
🔍 NO_FINISHED任务检测到：USER_B_002 从 NO_FINISHED → FINISHED

跨任务处理：
┌─────────────────┐    ┌─────────────────┐
│ FINISHED任务    │───→│ NO_FINISHED任务 │
│ 转移USER_A_002  │    │ 接收USER_A_002  │
└─────────────────┘    └─────────────────┘
           ↑                      │
           │    ┌─────────────────┐│
           └────│ NO_FINISHED任务 │
                │ 转移USER_B_002  │
                └─────────────────┘

最终状态：
┌─────────────────┐    ┌─────────────────┐
│ FINISHED任务    │    │ NO_FINISHED任务 │
│ 处理USER_A_001  │    │ 处理USER_B_001  │
│ 接收USER_B_002  │    │ 处理USER_B_003  │
│ 处理USER_A_003  │    │ 接收USER_A_002  │
└─────────────────┘    └─────────────────┘
```

#### 3.2 API调用示例

```bash
# 1. 启动分离式任务
curl "http://localhost:8080/perf/task/startSeparatedTasks?migrationType=FINISHED"
# 返回：FINISHED_SESSION_ID

# 2. 查看跨任务状态变化（30分钟后）
curl "http://localhost:8080/perf/task/getCrossTaskStatusChanges?sessionId=FINISHED_SESSION_ID"
# 返回：检测到的跨任务状态变化列表

# 3. 启动跨任务处理
curl "http://localhost:8080/perf/task/startCrossTaskReprocessing?sessionId=FINISHED_SESSION_ID"
# 返回：CROSS_TASK_SESSION_ID

# 4. 监控跨任务处理进度
curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId=CROSS_TASK_SESSION_ID"
# 返回：跨任务处理进度
```

## 🎯 总结

状态变化检测和跨任务执行的完整流程：

1. **定时检测**：每30分钟自动检测已处理用户的状态变化
2. **智能分类**：区分任务内变化和跨任务变化
3. **精准转移**：将跨任务变化转移到对应的目标任务
4. **独立处理**：在目标任务中重新处理状态变化的用户
5. **状态同步**：更新源任务和目标任务的处理状态

这个机制确保了分离式任务能够智能地处理状态变化，实现真正的增量化处理！
