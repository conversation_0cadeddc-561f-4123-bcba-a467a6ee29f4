# 基于现有设计的优化方案

## 现有设计分析

基于您的反馈，我理解您已有以下核心组件：

### 1. 现有组件职责
- **ScorerDataMingrationAppSvc**: 应用服务层，负责迁移任务的生命周期管理
- **ScorerDataMingrationDmSvc**: 领域服务层，负责具体的迁移业务逻辑
- **OnScoreEvalRepo**: 仓储层，负责评分数据的查询和操作，包含`getOnScoreEvalMingration`、`batchAddEmpEvalScorer`等方法

## 优化方案：在现有类中集成位图功能

### 方案1：在ScorerDataMingrationAppSvc中集成位图管理

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class ScorerDataMingrationAppSvc {
    
    private final ScorerDataMingrationDmSvc scorerDataMingrationDmSvc;
    private final OnScoreEvalRepo onScoreEvalRepo;
    
    // 新增：位图管理组件
    private final ConcurrentBitmapManager bitmapManager;
    private final RecordIndexCalculator indexCalculator;
    
    /**
     * 启动优化的评分数据迁移
     * 在现有方法基础上增加位图支持
     */
    public String startScorerMigration(String migrationType, String operatorId) {
        log.info("Starting scorer migration with bitmap optimization: type={}", migrationType);
        
        // 创建会话和位图进度管理
        String sessionId = generateSessionId();
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);
        
        // 初始化数据量
        initializeMigrationCounts(progress);
        
        // 保存进度
        progress.saveMetadata();
        
        // 异步执行迁移
        executeScorerMigrationAsync(sessionId);
        
        return sessionId;
    }
    
    /**
     * 恢复中断的评分迁移任务
     */
    public boolean resumeScorerMigration(String sessionId) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null || !progress.canResume()) {
            return false;
        }
        
        progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        progress.saveMetadata();
        
        executeScorerMigrationAsync(sessionId);
        return true;
    }
    
    /**
     * 异步执行评分迁移
     */
    @Async("migrationTaskExecutor")
    public CompletableFuture<Void> executeScorerMigrationAsync(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found: {}", sessionId);
                return CompletableFuture.completedFuture(null);
            }
            
            progress.start();
            
            // 调用优化的领域服务
            scorerDataMingrationDmSvc.executeOptimizedMigration(progress, bitmapManager, indexCalculator);
            
            progress.complete();
            log.info("Scorer migration completed: {}", sessionId);
            
        } catch (Exception e) {
            log.error("Scorer migration failed: {}", sessionId, e);
            handleMigrationFailure(sessionId, e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    private void initializeMigrationCounts(OptimizedBitmapProgress progress) {
        // 使用OnScoreEvalRepo获取总数据量
        long totalRecords = onScoreEvalRepo.getTotalMigrationRecordCount(progress.getMigrationType());
        progress.getTotalRecords().set(totalRecords);
    }
}
```

### 方案2：在ScorerDataMingrationDmSvc中集成位图逻辑

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class ScorerDataMingrationDmSvc {
    
    private final OnScoreEvalRepo onScoreEvalRepo;
    
    /**
     * 执行优化的迁移（新增方法，保持原有方法不变）
     */
    public void executeOptimizedMigration(OptimizedBitmapProgress progress, 
                                        ConcurrentBitmapManager bitmapManager,
                                        RecordIndexCalculator indexCalculator) {
        
        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();
        
        // 从断点位置开始处理
        int startCompanyPage = progress.getCurrentCompanyPage();
        int companyPageSize = 10;
        int userPageSize = 1000;
        
        for (int companyPage = startCompanyPage; ; companyPage++) {
            // 检查是否需要暂停
            if (shouldPause(progress)) {
                break;
            }
            
            // 使用OnScoreEvalRepo获取公司列表
            List<String> companyIds = onScoreEvalRepo.getCompanyIdsByPage(migrationType, companyPage, companyPageSize);
            if (companyIds.isEmpty()) {
                break;
            }
            
            // 处理每个公司
            for (String companyId : companyIds) {
                processCompanyWithBitmap(progress, companyId, userPageSize, bitmapManager, indexCalculator);
                progress.updatePosition(companyId, companyPage, 1);
            }
            
            // 定期保存进度
            if (companyPage % 5 == 0) {
                progress.saveMetadata();
            }
        }
    }
    
    /**
     * 使用位图处理单个公司
     */
    private void processCompanyWithBitmap(OptimizedBitmapProgress progress, String companyId, 
                                        int userPageSize, ConcurrentBitmapManager bitmapManager,
                                        RecordIndexCalculator indexCalculator) {
        
        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();
        
        int startUserPage = companyId.equals(progress.getCurrentCompanyId()) ? 
                progress.getCurrentUserPage() : 1;
        
        for (int userPage = startUserPage; ; userPage++) {
            // 使用OnScoreEvalRepo获取用户列表
            List<String> userIds = onScoreEvalRepo.getUserIdsByPage(migrationType, companyId, userPage, userPageSize);
            if (userIds.isEmpty()) {
                break;
            }
            
            // 位图检查和批量处理
            List<String> usersToProcess = new ArrayList<>();
            List<Long> indexesToProcess = new ArrayList<>();
            
            for (int i = 0; i < userIds.size(); i++) {
                String userId = userIds.get(i);
                long globalIndex = indexCalculator.calculateGlobalIndex(
                        companyId, progress.getCurrentCompanyPage(), userPage, i, 10, userPageSize);
                
                // 位图检查：如果已处理则跳过
                if (!bitmapManager.getBit(sessionId, globalIndex, progress.getSegmentSize())) {
                    usersToProcess.add(userId);
                    indexesToProcess.add(globalIndex);
                }
            }
            
            if (!usersToProcess.isEmpty()) {
                // 调用您现有的批量处理方法
                boolean batchSuccess = processBatchUsersWithExistingMethods(companyId, usersToProcess, migrationType);
                
                if (batchSuccess) {
                    // 批量更新位图
                    for (Long globalIndex : indexesToProcess) {
                        bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                        progress.markProcessed(globalIndex, true);
                    }
                } else {
                    // 逐个处理失败的记录
                    processIndividualUsers(progress, companyId, usersToProcess, indexesToProcess, migrationType, bitmapManager);
                }
            }
            
            progress.updatePosition(companyId, progress.getCurrentCompanyPage(), userPage + 1);
            
            if (userPage % 10 == 0) {
                progress.saveMetadata();
            }
        }
    }
    
    /**
     * 使用您现有的方法批量处理用户
     */
    private boolean processBatchUsersWithExistingMethods(String companyId, List<String> userIds, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                return processBatchFinishedUsers(companyId, userIds);
            } else if ("NO_FINISHED".equals(migrationType)) {
                return processBatchNoFinishedUsers(companyId, userIds);
            }
            return false;
            
        } catch (Exception e) {
            log.error("Batch processing failed: companyId={}, users={}", companyId, userIds.size(), e);
            return false;
        }
    }
    
    /**
     * 批量处理已完成的用户 - 集成您现有的业务逻辑
     */
    private boolean processBatchFinishedUsers(String companyId, List<String> userIds) {
        try {
            // 1. 使用OnScoreEvalRepo查询需要迁移的数据
            List<EvalScoreResult> scoreResults = onScoreEvalRepo.getOnScoreEvalMingration(companyId, userIds);
            
            // 2. 使用OnScoreEvalRepo的批量保存方法
            onScoreEvalRepo.batchSaveScoreResult(scoreResults);
            
            // 3. 使用OnScoreEvalRepo的批量更新方法
            onScoreEvalRepo.batchUpdateScoreResult(scoreResults);
            
            // 4. 调用OnScoreEvalRepo的batchAddEmpEvalScorer方法
            List<EmpEvalScorer> scorers = buildEmpEvalScorers(companyId, userIds, scoreResults);
            onScoreEvalRepo.batchAddEmpEvalScorer(scorers);
            
            log.debug("Successfully batch processed finished users: companyId={}, count={}", companyId, userIds.size());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process finished users: companyId={}", companyId, e);
            return false;
        }
    }
    
    /**
     * 批量处理未完成的用户
     */
    private boolean processBatchNoFinishedUsers(String companyId, List<String> userIds) {
        try {
            // 类似的逻辑，使用OnScoreEvalRepo的相关方法
            // 根据您的具体业务逻辑实现
            
            log.debug("Successfully batch processed no-finished users: companyId={}, count={}", companyId, userIds.size());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process no-finished users: companyId={}", companyId, e);
            return false;
        }
    }
    
    /**
     * 逐个处理用户（批量失败时的降级处理）
     */
    private void processIndividualUsers(OptimizedBitmapProgress progress, String companyId, 
                                      List<String> userIds, List<Long> indexes, String migrationType,
                                      ConcurrentBitmapManager bitmapManager) {
        
        String sessionId = progress.getSessionId();
        
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            Long globalIndex = indexes.get(i);
            
            try {
                // 调用您现有的单个用户处理逻辑
                boolean success = processIndividualUser(companyId, userId, migrationType);
                
                // 更新位图
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, success);
                
            } catch (Exception e) {
                log.error("Error processing individual user: companyId={}, userId={}", companyId, userId, e);
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, false);
            }
        }
    }
    
    /**
     * 处理单个用户 - 调用您现有的逻辑
     */
    private boolean processIndividualUser(String companyId, String userId, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                // 调用您现有的单个用户处理方法
                return processSingleFinishedUser(companyId, userId);
            } else if ("NO_FINISHED".equals(migrationType)) {
                return processSingleNoFinishedUser(companyId, userId);
            }
            return false;
            
        } catch (Exception e) {
            log.error("Failed to process individual user: companyId={}, userId={}", companyId, userId, e);
            return false;
        }
    }
    
    // 您现有的方法保持不变，新增这些优化方法
    // private boolean processSingleFinishedUser(String companyId, String userId) { ... }
    // private boolean processSingleNoFinishedUser(String companyId, String userId) { ... }
}
```

### 方案3：在OnScoreEvalRepo中增加优化方法

```java
@Repository
public class OnScoreEvalRepo {
    
    // 您现有的方法保持不变
    // public List<EvalScoreResult> getOnScoreEvalMingration(String companyId, List<String> userIds) { ... }
    // public void batchAddEmpEvalScorer(List<EmpEvalScorer> scorers) { ... }
    
    /**
     * 新增：获取总迁移记录数
     */
    public long getTotalMigrationRecordCount(String migrationType) {
        if ("FINISHED".equals(migrationType)) {
            return getTotalFinishedRecordCount();
        } else if ("NO_FINISHED".equals(migrationType)) {
            return getTotalNoFinishedRecordCount();
        }
        return 0;
    }
    
    /**
     * 新增：分页获取公司ID列表
     */
    public List<String> getCompanyIdsByPage(String migrationType, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        
        if ("FINISHED".equals(migrationType)) {
            return getFinishedCompanyIds(offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return getNoFinishedCompanyIds(offset, pageSize);
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 新增：分页获取用户ID列表
     */
    public List<String> getUserIdsByPage(String migrationType, String companyId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        
        if ("FINISHED".equals(migrationType)) {
            return getFinishedUserIds(companyId, offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return getNoFinishedUserIds(companyId, offset, pageSize);
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 优化：批量版本的getOnScoreEvalMingration
     */
    public List<EvalScoreResult> getOnScoreEvalMingrationBatch(String companyId, List<String> userIds) {
        // 使用IN查询替代循环查询，提高性能
        return queryScoreResultsByUserIds(companyId, userIds);
    }
    
    /**
     * 优化：真正的批量插入batchAddEmpEvalScorer
     */
    public void batchAddEmpEvalScorerOptimized(List<EmpEvalScorer> scorers) {
        if (scorers.isEmpty()) {
            return;
        }
        
        // 使用MyBatis的批量插入或JDBC批量操作
        batchInsertEmpEvalScorers(scorers);
    }
    
    // 私有方法实现具体的数据库操作
    private long getTotalFinishedRecordCount() {
        // 实现查询已完成记录总数的SQL
        return 0; // 临时返回
    }
    
    private long getTotalNoFinishedRecordCount() {
        // 实现查询未完成记录总数的SQL
        return 0; // 临时返回
    }
    
    private List<String> getFinishedCompanyIds(int offset, int pageSize) {
        // 实现分页查询已完成任务的公司ID
        return new ArrayList<>(); // 临时返回
    }
    
    private List<String> getNoFinishedCompanyIds(int offset, int pageSize) {
        // 实现分页查询未完成任务的公司ID
        return new ArrayList<>(); // 临时返回
    }
    
    private List<String> getFinishedUserIds(String companyId, int offset, int pageSize) {
        // 实现分页查询已完成任务的用户ID
        return new ArrayList<>(); // 临时返回
    }
    
    private List<String> getNoFinishedUserIds(String companyId, int offset, int pageSize) {
        // 实现分页查询未完成任务的用户ID
        return new ArrayList<>(); // 临时返回
    }
    
    private List<EvalScoreResult> queryScoreResultsByUserIds(String companyId, List<String> userIds) {
        // 实现批量查询评分结果的SQL
        return new ArrayList<>(); // 临时返回
    }
    
    private void batchInsertEmpEvalScorers(List<EmpEvalScorer> scorers) {
        // 实现真正的批量插入SQL
    }
}
```

## 集成优势

### 1. 保持现有设计
- ✅ 不破坏现有的类职责划分
- ✅ 保持现有方法的接口不变
- ✅ 新增功能作为增强，不影响现有功能

### 2. 渐进式优化
- ✅ 可以逐步迁移到优化版本
- ✅ 支持新旧版本并行运行
- ✅ 风险可控，易于回滚

### 3. 性能提升
- ✅ 位图检查避免重复处理
- ✅ 批量操作提高数据库性能
- ✅ 断点续传减少重复工作

## 实施建议

### 第一步：在现有类中添加优化方法
1. 在`ScorerDataMingrationAppSvc`中添加位图管理
2. 在`ScorerDataMingrationDmSvc`中添加优化处理逻辑
3. 在`OnScoreEvalRepo`中添加分页和批量查询方法

### 第二步：配置开关控制
```java
@Value("${scorer.migration.use.bitmap:false}")
private boolean useBitmapOptimization;

public String startMigration(String migrationType, String operatorId) {
    if (useBitmapOptimization) {
        return startScorerMigration(migrationType, operatorId); // 新方法
    } else {
        return startOriginalMigration(migrationType, operatorId); // 原有方法
    }
}
```

### 第三步：逐步切换
1. 测试环境验证新功能
2. 生产环境小流量测试
3. 逐步增加新版本流量
4. 完全切换到新版本

这样的优化方案既保持了您现有的设计，又解决了断点恢复和内存膨胀问题。您觉得这个方案如何？
