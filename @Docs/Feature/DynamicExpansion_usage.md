# 动态扩展功能使用指南

## 🎯 功能概述

动态扩展功能解决了您提出的重要场景：**在迁移过程中或迁移完成后，动态添加新增的用户ID进行迁移**。

### 典型场景
- 🔄 **企业A正在迁移中**：已处理50%的用户，突然有新用户需要迁移
- 🔄 **企业A迁移完成**：发现还有遗漏的用户需要补充迁移
- 🔄 **业务系统新增用户**：实时产生的新用户需要立即加入迁移队列

## 🚀 核心功能

### 1. **手动添加动态用户**
- ✅ **单个添加**：手动添加单个用户到正在进行的迁移任务
- ✅ **批量添加**：一次性添加多个用户
- ✅ **状态匹配**：自动检查用户状态与任务类型是否匹配

### 2. **自动检测新增用户**
- ✅ **定时检测**：每15分钟自动检测新增用户（可配置）
- ✅ **业务集成**：可集成业务事件，实时检测新用户
- ✅ **智能过滤**：只添加符合当前任务类型的用户

### 3. **动态处理机制**
- ✅ **即时处理**：新增用户立即分配全局索引并处理
- ✅ **进度跟踪**：动态用户的处理进度独立跟踪
- ✅ **失败重试**：支持动态用户的失败重试机制

## 📋 配置启用

### 1. 启用动态扩展功能

在您的配置文件（如 `app-ding-uat.yml`）中添加：

```yaml
scorer:
  migration:
    # 启用动态扩展（默认已开启）
    dynamic-expansion:
      enabled: true
      check-interval-minutes: 15    # 自动检测间隔
```

### 2. 最简配置

```yaml
scorer:
  migration:
    dynamic-expansion.enabled: true
```

## 🔧 API使用指南

### 1. **手动添加单个动态用户**

```bash
# 添加单个动态用户
curl -X POST "http://localhost:8080/perf/task/addDynamicUser" \
  -d "sessionId=OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4" \
  -d "userId=NEW_USER_001" \
  -d "companyId=COMPANY_A" \
  -d "userStatus=FINISHED"

# 响应
{
  "success": true,
  "data": "添加成功"
}
```

### 2. **批量添加动态用户**

```bash
# 批量添加动态用户
curl -X POST "http://localhost:8080/perf/task/addDynamicUsers" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4",
    "users": {
      "NEW_USER_001": {
        "companyId": "COMPANY_A",
        "userStatus": "FINISHED"
      },
      "NEW_USER_002": {
        "companyId": "COMPANY_A", 
        "userStatus": "FINISHED"
      },
      "NEW_USER_003": {
        "companyId": "COMPANY_B",
        "userStatus": "NO_FINISHED"
      }
    }
  }'

# 响应
{
  "success": true,
  "data": 3  // 成功添加的用户数量
}
```

### 3. **查看动态扩展统计**

```bash
# 获取动态扩展统计信息
curl "http://localhost:8080/perf/task/getDynamicExpansionStatistics?sessionId={sessionId}"

# 响应示例
{
  "success": true,
  "data": "动态扩展统计:\n总动态用户数: 15\n待处理: 3\n已处理: 12\n按来源分组: {MANUAL=8, AUTO_DETECTION=5, BATCH_MANUAL=2}"
}
```

### 4. **获取动态用户列表**

```bash
# 获取所有动态用户
curl "http://localhost:8080/perf/task/getDynamicUsers?sessionId={sessionId}"

# 只获取待处理的动态用户
curl "http://localhost:8080/perf/task/getDynamicUsers?sessionId={sessionId}&needsProcessingOnly=true"

# 响应示例
{
  "success": true,
  "data": [
    {
      "userId": "NEW_USER_001",
      "companyId": "COMPANY_A",
      "userStatus": "FINISHED",
      "addedTime": "2025-06-29T15:30:00",
      "needsProcessing": false,
      "processed": true,
      "processedTime": "2025-06-29T15:31:00",
      "addedBy": "MANUAL",
      "globalIndex": 1310001
    }
  ]
}
```

### 5. **获取待处理的动态用户**

```bash
# 获取待处理的动态用户
curl "http://localhost:8080/perf/task/getPendingDynamicUsers?sessionId={sessionId}"
```

## 📊 使用场景示例

### 场景1：迁移过程中新增用户

```bash
# 1. 启动企业A的迁移
curl "http://localhost:8080/perf/task/startSeparatedTasks?migrationType=FINISHED"
# 返回: FINISHED_SESSION_ID

# 2. 迁移进行中，发现有新用户需要迁移
curl -X POST "http://localhost:8080/perf/task/addDynamicUser" \
  -d "sessionId=FINISHED_SESSION_ID" \
  -d "userId=URGENT_USER_001" \
  -d "companyId=COMPANY_A" \
  -d "userStatus=FINISHED"

# 3. 系统自动在下次检查时处理新增用户（最多15分钟）
# 或者立即查看处理状态
curl "http://localhost:8080/perf/task/getDynamicUsers?sessionId=FINISHED_SESSION_ID&needsProcessingOnly=true"
```

### 场景2：迁移完成后补充用户

```bash
# 1. 迁移已完成，发现遗漏用户
curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId=FINISHED_SESSION_ID"
# 状态: COMPLETED

# 2. 批量添加遗漏的用户
curl -X POST "http://localhost:8080/perf/task/addDynamicUsers" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "FINISHED_SESSION_ID",
    "users": {
      "MISSED_USER_001": {"companyId": "COMPANY_A", "userStatus": "FINISHED"},
      "MISSED_USER_002": {"companyId": "COMPANY_A", "userStatus": "FINISHED"},
      "MISSED_USER_003": {"companyId": "COMPANY_B", "userStatus": "FINISHED"}
    }
  }'

# 3. 查看补充处理进度
curl "http://localhost:8080/perf/task/getDynamicExpansionStatistics?sessionId=FINISHED_SESSION_ID"
```

### 场景3：业务系统实时新增用户

```bash
# 业务系统可以通过API实时添加新用户
# 例如：用户注册后立即加入迁移队列

# 新用户注册事件处理
function onUserRegistered(userId, companyId, userStatus) {
    curl -X POST "http://localhost:8080/perf/task/addDynamicUser" \
      -d "sessionId=${ACTIVE_MIGRATION_SESSION}" \
      -d "userId=${userId}" \
      -d "companyId=${companyId}" \
      -d "userStatus=${userStatus}"
}
```

## 🔍 自动检测机制

### 1. **检测触发时机**

系统会在以下时机自动检测新增用户：
- ✅ **定时检测**：每15分钟自动检测（可配置）
- ✅ **迁移过程中**：在处理每页公司数据时检查
- ✅ **手动触发**：可通过API手动触发检测

### 2. **自定义检测逻辑**

您需要在`ScorerDataMingrationAppSvc`中实现`detectNewUsers`方法：

```java
/**
 * 检测新增用户
 * TODO: 请根据您的实际业务逻辑实现此方法
 */
private List<DynamicUserInfo> detectNewUsers(String migrationType, OptimizedBitmapProgress progress) {
    List<DynamicUserInfo> newUsers = new ArrayList<>();
    
    // 方案1：查询数据库中最近新增的用户
    List<User> recentUsers = userService.getRecentUsers(progress.getLastDynamicCheckTime());
    for (User user : recentUsers) {
        if (needsMigration(user, migrationType)) {
            newUsers.add(new DynamicUserInfo(user.getId(), user.getCompanyId(), migrationType));
        }
    }
    
    // 方案2：监听业务事件获取新增用户
    List<UserEvent> userEvents = eventService.getUserEvents(progress.getLastDynamicCheckTime());
    for (UserEvent event : userEvents) {
        if (event.getType() == EventType.USER_CREATED) {
            newUsers.add(new DynamicUserInfo(event.getUserId(), event.getCompanyId(), migrationType));
        }
    }
    
    return newUsers;
}
```

## ⚙️ 配置参数说明

### 动态扩展配置

```yaml
scorer:
  migration:
    dynamic-expansion:
      enabled: true                           # 是否启用动态扩展
      check-interval-minutes: 15              # 自动检测间隔（分钟）
      auto-process-enabled: true              # 是否自动处理检测到的用户
      max-pending-users: 1000                 # 最大待处理用户数
      cleanup-processed-users: true           # 是否自动清理已处理的用户记录
```

## 🎯 分离式任务中的动态扩展

### 1. **智能任务匹配**

在分离式任务中，动态用户会自动匹配到对应的任务：

```
FINISHED任务：只接受userStatus=FINISHED的动态用户
NO_FINISHED任务：只接受userStatus=NO_FINISHED的动态用户
```

### 2. **跨任务动态添加**

```bash
# 向FINISHED任务添加FINISHED状态的用户
curl -X POST "http://localhost:8080/perf/task/addDynamicUser" \
  -d "sessionId=FINISHED_SESSION_ID" \
  -d "userStatus=FINISHED"  # ✅ 匹配

# 向FINISHED任务添加NO_FINISHED状态的用户
curl -X POST "http://localhost:8080/perf/task/addDynamicUser" \
  -d "sessionId=FINISHED_SESSION_ID" \
  -d "userStatus=NO_FINISHED"  # ❌ 不匹配，会被拒绝
```

## 🔧 监控和管理

### 1. **动态扩展监控**

```bash
# 监控动态扩展整体情况
curl "http://localhost:8080/perf/task/getDynamicExpansionStatistics?sessionId={sessionId}"

# 监控待处理用户数量
curl "http://localhost:8080/perf/task/getPendingDynamicUsers?sessionId={sessionId}"

# 监控整体迁移进度（包含动态用户）
curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId={sessionId}"
```

### 2. **性能监控重点**

- **动态用户处理延迟**：从添加到处理完成的时间
- **检测效率**：自动检测的准确性和及时性
- **处理成功率**：动态用户的处理成功率
- **内存使用**：动态用户记录的内存占用

## 🎉 总结

动态扩展功能完美解决了您的需求场景：

1. **灵活添加**：支持手动和自动两种方式添加新用户
2. **即时处理**：新增用户立即进入处理队列
3. **智能匹配**：自动匹配任务类型，避免错误添加
4. **完整跟踪**：提供详细的动态用户处理统计和监控
5. **高性能**：最小化对主迁移流程的影响

无论是迁移过程中的紧急新增，还是迁移完成后的补充处理，都能完美支持！
