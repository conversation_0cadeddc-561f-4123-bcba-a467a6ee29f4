/**
 * 测试 SessionId 清理功能
 * 用于验证路径分隔符清理和时间戳提取逻辑
 */
public class SessionId_clean_test {
    
    public static void main(String[] args) {
        testCleanSessionId();
    }
    
    /**
     * 测试 cleanSessionId 方法
     */
    public static void testCleanSessionId() {
        System.out.println("=== SessionId 清理测试 ===");
        
        // 测试用例1：包含路径分隔符的sessionId
        String sessionId1 = "/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0";
        String cleaned1 = cleanSessionId(sessionId1);
        System.out.println("测试1:");
        System.out.println("  原始: " + sessionId1);
        System.out.println("  清理: " + cleaned1);
        System.out.println("  预期: startOptimizedMigration-9288b60ccfe0");
        System.out.println("  结果: " + (cleaned1.equals("startOptimizedMigration-9288b60ccfe0") ? "✅ 通过" : "❌ 失败"));
        System.out.println();
        
        // 测试用例2：包含13位时间戳的sessionId
        String sessionId2 = "/v2/api/perf/task/startOptimizedMigration-1703123456789";
        String cleaned2 = cleanSessionId(sessionId2);
        System.out.println("测试2:");
        System.out.println("  原始: " + sessionId2);
        System.out.println("  清理: " + cleaned2);
        System.out.println("  预期: startOptimizedMigration-1703123456789");
        System.out.println("  结果: " + (cleaned2.equals("startOptimizedMigration-1703123456789") ? "✅ 通过" : "❌ 失败"));
        System.out.println();
        
        // 测试用例3：正常的sessionId
        String sessionId3 = "normalSessionId123";
        String cleaned3 = cleanSessionId(sessionId3);
        System.out.println("测试3:");
        System.out.println("  原始: " + sessionId3);
        System.out.println("  清理: " + cleaned3);
        System.out.println("  预期: normalSessionId123");
        System.out.println("  结果: " + (cleaned3.equals("normalSessionId123") ? "✅ 通过" : "❌ 失败"));
        System.out.println();
        
        // 测试用例4：Windows路径
        String sessionId4 = "C:\\temp\\migration\\session-abc123def456";
        String cleaned4 = cleanSessionId(sessionId4);
        System.out.println("测试4:");
        System.out.println("  原始: " + sessionId4);
        System.out.println("  清理: " + cleaned4);
        System.out.println("  预期: session-abc123def456");
        System.out.println("  结果: " + (cleaned4.contains("session-abc123def456") ? "✅ 通过" : "❌ 失败"));
        System.out.println();
    }
    
    /**
     * 复制 OptimizedBitmapProgress 中的 cleanSessionId 方法进行测试
     */
    private static String cleanSessionId(String sessionId) {
        if (sessionId == null) {
            return null;
        }
        
        System.out.println("cleanSessionId input: " + sessionId);
        
        // 移除路径分隔符和特殊字符，只保留字母、数字、下划线和连字符
        String cleaned = sessionId.replaceAll("[/\\\\:*?\"<>|]", "_");
        System.out.println("After replaceAll: " + cleaned);
        
        // 如果sessionId看起来像URL路径，提取最后一部分
        if (cleaned.contains("_") && cleaned.length() > 50) {
            System.out.println("Processing long sessionId with underscores: " + cleaned);
            String[] parts = cleaned.split("_");
            System.out.println("Split into " + parts.length + " parts: " + String.join(", ", parts));
            
            // 查找包含时间戳的部分（通常是最后几部分）
            for (int i = parts.length - 1; i >= 0; i--) {
                System.out.println("Checking part[" + i + "]: " + parts[i]);
                // 检查是否包含时间戳（13位数字）或十六进制ID（8-12位字母数字）
                if (parts[i].matches(".*\\d{13}.*") || parts[i].matches(".*[a-f0-9]{8,12}.*")) {
                    System.out.println("Found timestamp/hex in part[" + i + "]: " + parts[i]);
                    // 从这部分开始重新组合
                    StringBuilder result = new StringBuilder();
                    for (int j = i; j < parts.length; j++) {
                        if (result.length() > 0) result.append("_");
                        result.append(parts[j]);
                    }
                    cleaned = result.toString();
                    System.out.println("Extracted cleaned sessionId: " + cleaned);
                    break;
                }
            }
        }
        
        System.out.println("Cleaned sessionId: " + sessionId + " -> " + cleaned);
        return cleaned;
    }
}
