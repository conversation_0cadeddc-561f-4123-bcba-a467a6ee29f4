# SessionId 生成调试指南

## 🔍 问题分析

从您的文件列表可以看出，实际保存的文件名是：
- `3adf798b.metadata`
- `078566be.metadata` 
- `17512900_97190_5a1c5c97.metadata`
- `17512900_97190_572_15d81.metadata`

但是Controller返回的sessionId是：`/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0`

这说明**文件保存和加载使用了不同的sessionId**！

## 🎯 调试步骤

### 1. **测试sessionId生成**

```bash
curl "http://localhost:8080/perf/task/testSessionIdGeneration"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "originalSessionId": "OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4",
    "cleanedSessionId": "OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4",
    "baseDir": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\migration",
    "metadataFile": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\migration\\OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4.metadata",
    "timestamp": 1735459200000
  }
}
```

### 2. **启动迁移任务并观察日志**

```bash
curl -X POST "http://localhost:8080/v2/api/perf/task/startOptimizedMigration" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "migrationType=FINISHED"
```

**关键日志输出：**
```
🔍 Request URI: /v2/api/perf/task/startOptimizedMigration
🔍 Request URL: http://localhost:8080/v2/api/perf/task/startOptimizedMigration
🔍 TID: abc123def456
🔍 Migration Type: FINISHED
🔍 Operator ID: admin
🔍 生成的sessionId: OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4
🔍 SessionId长度: 45
🔍 SessionId是否包含路径分隔符: false
🔍 SessionId是否以OPTIMIZED开头: true
🔍 响应对象: SingleResponse{success=true, data=OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4}
```

### 3. **检查实际返回的sessionId**

如果返回的sessionId不是预期格式，可能的原因：

#### **原因1：配置问题**
检查是否启用了位图优化：
```bash
curl "http://localhost:8080/perf/task/checkOptimizationConfig"
```

#### **原因2：回退到原始方法**
如果位图优化未启用，会调用`startOriginalMigration`：
```java
if (!useBitmapOptimization || bitMap == null || indexCalculator == null) {
    log.warn("Bitmap optimization not enabled, falling back to original method");
    return startOriginalMigration(migrationType, operatorId);
}
```

#### **原因3：任务分离模式**
如果启用了任务分离，会调用`startSeparatedTasks`：
```java
if (enableTaskSeparation) {
    return startSeparatedTasks(tid, migrationType, operatorId);
}
```

### 4. **检查配置状态**

```bash
curl "http://localhost:8080/perf/task/getOptimizationConfig"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "useBitmapOptimization": true,
    "enableTaskSeparation": true,
    "enableIncrementalDetection": true,
    "bitMapAvailable": true,
    "indexCalculatorAvailable": true
  }
}
```

## 🔧 可能的问题和解决方案

### **问题1：位图优化未启用**

**症状：** 返回的sessionId格式不是`OPTIMIZED_SCORER_MIGRATION_`开头

**解决方案：**
1. 检查配置文件中的`scorer.migration.use-bitmap`设置
2. 确保`BitMap`和`IndexCalculator` Bean正确注入

### **问题2：文件名不匹配**

**症状：** 
- 返回sessionId：`OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4`
- 实际文件名：`3adf798b.metadata`

**可能原因：**
1. 有其他地方在生成不同格式的sessionId
2. 文件保存时使用了不同的sessionId
3. 存在多个迁移任务同时运行

**解决方案：**
1. 清理旧的测试文件
2. 检查是否有其他迁移任务在运行
3. 验证文件保存逻辑

### **问题3：请求路径被返回**

**症状：** 返回的sessionId是`/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0`

**可能原因：**
1. 有拦截器或过滤器修改了响应
2. 前端或代理服务器修改了响应
3. 存在其他Controller处理了相同的路径

**解决方案：**
1. 检查是否有多个Controller映射相同路径
2. 检查拦截器配置
3. 直接调用服务层方法测试

## 🧪 深度调试

### **1. 直接调用服务层**

创建测试接口直接调用服务层：
```java
@RequestMapping("perf/task/directTestMigration")
public SingleResponse directTestMigration(String migrationType) {
    try {
        String sessionId = mingrationAppSvc.generateOptimizedSessionId();
        return SingleResponse.of(sessionId);
    } catch (Exception e) {
        return SingleResponse.buildFailure("测试失败: " + e.getMessage());
    }
}
```

### **2. 检查Bean注入状态**

```java
@RequestMapping("perf/task/checkBeanStatus")
public SingleResponse checkBeanStatus() {
    Map<String, Object> status = new HashMap<>();
    status.put("mingrationAppSvc", mingrationAppSvc != null);
    status.put("mingrationAppSvcClass", mingrationAppSvc != null ? mingrationAppSvc.getClass().getName() : "null");
    return SingleResponse.of(status);
}
```

### **3. 检查文件系统状态**

```bash
# 清理旧文件
rm -rf /tmp/migration/*
# 或 Windows
del /q "%TEMP%\migration\*"

# 重新启动迁移
curl -X POST "http://localhost:8080/v2/api/perf/task/startOptimizedMigration" \
  -d "migrationType=FINISHED"

# 立即检查生成的文件
ls -la /tmp/migration/
# 或 Windows
dir "%TEMP%\migration"
```

## 📋 检查清单

- [ ] 确认返回的sessionId格式
- [ ] 检查位图优化配置状态
- [ ] 验证Bean注入状态
- [ ] 清理旧的测试文件
- [ ] 检查日志输出
- [ ] 验证文件保存路径
- [ ] 确认没有多个Controller冲突

按照这个调试指南，我们应该能够找到sessionId不匹配的根本原因！
