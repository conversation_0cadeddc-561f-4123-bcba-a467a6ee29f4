# 数据迁移功能单元测试指南

## 🎯 测试概述

为数据迁移功能创建了完整的单元测试套件，覆盖了所有核心功能模块。测试遵循项目现有的测试风格和规范。

## 📋 测试文件结构

### 1. **测试类文件**

```
perf/
├── perf-web/src/test/java/com/polaris/kpi/controller/eval/task/
│   └── ScorerDataMingrationControllerTest.java          # Controller层测试
├── perf-biz/src/test/java/com/polaris/kpi/eval/app/task/appsvc/
│   ├── ScorerDataMingrationAppSvcTest.java              # 服务层测试
│   └── TestDataFactory.java                            # 测试数据工厂
├── perf-domain/src/test/java/com/polaris/kpi/eval/domain/migration/entity/
│   └── OptimizedBitmapProgressTest.java                # 实体类测试
└── src/test/resources/
    └── application-test.yml                            # 测试配置文件
```

### 2. **测试工具文件**

```
perf/
├── run-tests.sh                                        # 测试运行脚本
└── @Docs/Testing/
    └── UnitTest_guide.md                              # 测试指南（本文档）
```

## 🚀 运行测试

### 1. **使用脚本运行（推荐）**

```bash
# 给脚本执行权限
chmod +x run-tests.sh

# 运行所有数据迁移相关测试
./run-tests.sh
```

### 2. **使用Maven命令运行**

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ScorerDataMingrationControllerTest
mvn test -Dtest=ScorerDataMingrationAppSvcTest
mvn test -Dtest=OptimizedBitmapProgressTest

# 运行所有数据迁移相关测试
mvn test -Dtest="*Migration*Test"

# 运行测试并生成报告
mvn test surefire-report:report
```

### 3. **在IDE中运行**

- **IntelliJ IDEA**：右键测试类 → Run 'TestClassName'
- **Eclipse**：右键测试类 → Run As → JUnit Test

## 📊 测试覆盖范围

### 1. **ScorerDataMingrationControllerTest**

#### 基础迁移功能测试
- ✅ `testStartOptimizedMigration_Success` - 启动优化迁移成功场景
- ✅ `testStartOptimizedMigration_Failure` - 启动优化迁移失败场景
- ✅ `testGetOptimizedMigrationStatus_Success` - 获取迁移状态成功场景
- ✅ `testGetOptimizedMigrationStatus_NotFound` - 会话不存在场景
- ✅ `testPauseOptimizedMigration_Success` - 暂停迁移成功场景
- ✅ `testResumeOptimizedMigration_Success` - 恢复迁移成功场景
- ✅ `testStopOptimizedMigration_Success` - 停止迁移成功场景

#### 失败记录跟踪测试
- ✅ `testGetFailureRecords_Success` - 获取失败记录成功场景
- ✅ `testRetryFailureRecords_Success` - 重试失败记录成功场景
- ✅ `testGetFailureStatistics_Success` - 获取失败统计成功场景

#### 分离式任务测试
- ✅ `testStartSeparatedTasks_Success` - 启动分离式任务成功场景
- ✅ `testGetSeparatedTasksOverview_Success` - 获取分离式任务概览成功场景

#### 增量检测测试
- ✅ `testStartIncrementalReprocessing_Success` - 启动增量重新处理成功场景
- ✅ `testGetStatusChangeStatistics_Success` - 获取状态变化统计成功场景
- ✅ `testGetStatusChangeRecords_Success` - 获取状态变化记录成功场景
- ✅ `testStartCrossTaskReprocessing_Success` - 启动跨任务重新处理成功场景

#### 动态扩展测试
- ✅ `testAddDynamicUser_Success` - 添加动态用户成功场景
- ✅ `testAddDynamicUser_Failure` - 添加动态用户失败场景
- ✅ `testAddDynamicUsers_Success` - 批量添加动态用户成功场景
- ✅ `testGetDynamicExpansionStatistics_Success` - 获取动态扩展统计成功场景
- ✅ `testGetDynamicUsers_Success` - 获取动态用户列表成功场景
- ✅ `testGetPendingDynamicUsers_Success` - 获取待处理动态用户成功场景

### 2. **ScorerDataMingrationAppSvcTest**

#### 基础迁移功能测试
- ✅ `testStartOptimizedMigration_Success` - 启动优化迁移成功场景
- ✅ `testStartOptimizedMigration_BitmapNotEnabled` - 位图优化未启用场景
- ✅ `testStartSeparatedTasks_Success` - 启动分离式任务成功场景
- ✅ `testGetOptimizedMigrationStatus_Success` - 获取优化迁移状态成功场景
- ✅ `testPauseOptimizedMigration_Success` - 暂停优化迁移成功场景

#### 动态扩展功能测试
- ✅ `testAddDynamicUser_Success` - 添加动态用户成功场景
- ✅ `testAddDynamicUsers_Success` - 批量添加动态用户成功场景
- ✅ `testGetDynamicExpansionStatistics_Success` - 获取动态扩展统计成功场景
- ✅ `testGetDynamicUsers_Success` - 获取动态用户列表成功场景

#### 增量检测功能测试
- ✅ `testStartIncrementalReprocessing_Success` - 启动增量重新处理成功场景
- ✅ `testGetStatusChangeStatistics_Success` - 获取状态变化统计成功场景

#### 分离式任务功能测试
- ✅ `testGetRelatedTaskSessionId_Success` - 获取关联任务会话ID成功场景
- ✅ `testStartCrossTaskReprocessing_Success` - 启动跨任务重新处理成功场景
- ✅ `testGetSeparatedTasksOverview_Success` - 获取分离式任务概览成功场景

#### 边界条件和异常测试
- ✅ `testNullParameterHandling` - 空参数处理测试
- ✅ `testInvalidSessionIdHandling` - 无效会话ID处理测试

### 3. **OptimizedBitmapProgressTest**

#### 基础功能测试
- ✅ `testConstructorAndBasicProperties` - 构造函数和基础属性测试
- ✅ `testLifecycleStatusManagement` - 生命周期状态管理测试
- ✅ `testCounterOperations` - 计数器操作测试
- ✅ `testStatusCheckMethods` - 状态检查方法测试

#### 失败记录跟踪测试
- ✅ `testFailureRecordManagement` - 失败记录管理测试
- ✅ `testGetFailureRecordsByErrorType` - 按错误类型获取失败记录测试
- ✅ `testFailureRecordRetry` - 失败记录重试测试

#### 任务分离功能测试
- ✅ `testEnableTaskSeparation` - 启用任务分离测试
- ✅ `testShouldDetectStatusChange` - 状态变化检测逻辑测试

#### 增量检测功能测试
- ✅ `testEnableIncrementalDetection` - 启用增量检测测试
- ✅ `testStatusChangeRecordManagement` - 状态变化记录管理测试

#### 动态扩展功能测试
- ✅ `testEnableDynamicExpansion` - 启用动态扩展测试
- ✅ `testDynamicUserManagement` - 动态用户管理测试
- ✅ `testBatchAddDynamicUsers` - 批量添加动态用户测试
- ✅ `testMarkDynamicUserAsProcessed` - 动态用户处理标记测试

#### 统计信息测试
- ✅ `testGetStatistics` - 获取统计信息测试
- ✅ `testGetDynamicExpansionStatistics` - 获取动态扩展统计信息测试

## 🔧 测试配置

### 1. **测试环境配置**

```yaml
# application-test.yml
spring:
  profiles:
    active: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver

scorer:
  migration:
    use-bitmap: true
    task-separation.enabled: true
    incremental.enabled: true
    dynamic-expansion.enabled: true
```

### 2. **测试数据工厂**

`TestDataFactory`类提供了创建各种测试数据的便捷方法：

```java
// 创建基础进度对象
OptimizedBitmapProgress progress = TestDataFactory.createBasicProgress();

// 创建带失败记录的进度对象
OptimizedBitmapProgress progressWithFailures = TestDataFactory.createProgressWithFailures();

// 创建动态用户映射
Map<String, Map<String, String>> users = TestDataFactory.createDynamicUsersMap(5);
```

## 📈 测试报告

### 1. **查看测试报告**

运行测试后，可以查看以下报告：

```bash
# HTML测试报告
open target/site/surefire-report.html

# XML测试报告
ls target/surefire-reports/

# 测试覆盖率报告（如果配置了JaCoCo）
open target/site/jacoco/index.html
```

### 2. **测试输出示例**

```
========================================
开始运行数据迁移功能单元测试
========================================

1. 运行 OptimizedBitmapProgress 实体类测试...
✅ OptimizedBitmapProgress 测试通过

2. 运行 ScorerDataMingrationAppSvc 服务层测试...
✅ ScorerDataMingrationAppSvc 测试通过

3. 运行 ScorerDataMingrationController 控制器测试...
✅ ScorerDataMingrationController 测试通过

4. 运行所有数据迁移相关测试...
✅ 所有数据迁移测试通过

测试完成！
```

## 🎯 测试最佳实践

### 1. **测试命名规范**

```java
// 格式：test[方法名]_[场景]
public void testStartOptimizedMigration_Success()
public void testAddDynamicUser_Failure()
public void testGetFailureRecords_NotFound()
```

### 2. **测试结构（AAA模式）**

```java
@Test
public void testMethodName_Scenario() {
    // Given - 准备测试数据
    String migrationType = "FINISHED";
    Mockito.when(service.method()).thenReturn(expectedResult);
    
    // When - 执行被测试方法
    SingleResponse response = controller.method(migrationType);
    
    // Then - 验证结果
    Assert.assertTrue("响应应该成功", response.isSuccess());
    Assert.assertEquals("数据应该匹配", expectedData, response.getData());
    Mockito.verify(service).method();
}
```

### 3. **Mock使用规范**

```java
// 使用@Mock注解
@Mock
private ScorerDataMingrationAppSvc mingrationAppSvc;

// 在测试方法中设置Mock行为
Mockito.when(mingrationAppSvc.startOptimizedMigration(anyString(), anyString()))
        .thenReturn(TEST_SESSION_ID);

// 验证Mock方法调用
Mockito.verify(mingrationAppSvc).startOptimizedMigration(migrationType, operatorId);
```

## 🔍 故障排除

### 1. **常见问题**

#### 问题1：测试运行失败
```bash
# 解决方案：清理并重新编译
mvn clean compile test-compile
mvn test
```

#### 问题2：Mock对象注入失败
```java
// 确保使用正确的注解
@RunWith(PowerMockRunner.class)
public class TestClass {
    @InjectMocks
    private ControllerClass controller;
    
    @Mock
    private ServiceClass service;
}
```

#### 问题3：测试配置文件未加载
```java
// 在测试类上添加配置注解
@TestPropertySource(locations = "classpath:application-test.yml")
```

### 2. **调试技巧**

```java
// 在测试中添加日志输出
System.out.println("Debug: " + response.getData());

// 使用断点调试
// 在IDE中设置断点，以Debug模式运行测试

// 验证Mock调用次数
Mockito.verify(service, times(1)).method();
Mockito.verify(service, never()).anotherMethod();
```

## 📝 扩展测试

### 1. **添加新的测试方法**

```java
/**
 * 测试新功能 - 成功场景
 */
@Test
public void testNewFeature_Success() {
    // Given
    // 准备测试数据
    
    // When
    // 执行被测试方法
    
    // Then
    // 验证结果
}
```

### 2. **添加集成测试**

```java
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
public class MigrationIntegrationTest {
    // 集成测试代码
}
```

通过这套完整的单元测试，您可以确保数据迁移功能的质量和稳定性！
