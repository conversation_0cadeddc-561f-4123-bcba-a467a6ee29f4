# SessionId 修复验证测试

## 🎯 测试目标

验证 SessionId 路径问题修复是否生效，确保：
1. ✅ SessionId 正确清理路径分隔符
2. ✅ 文件路径生成正确
3. ✅ Files.exists() 返回 true
4. ✅ 元数据文件正常保存和加载

## 🔧 测试步骤

### 1. **启动优化迁移任务**

```bash
curl -X POST "http://localhost:8080/v2/api/perf/task/startOptimizedMigration" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "migrationType=FINISHED"
```

**预期响应：**
```json
{
  "success": true,
  "data": "/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0"
}
```

### 2. **检查元数据文件状态**

使用返回的sessionId检查文件状态：

```bash
curl "http://localhost:8080/perf/task/checkMetadataFile?sessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0"
```

**修复前的响应（问题）：**
```json
{
  "success": true,
  "data": {
    "sessionId": "/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0",
    "baseDir": "\\tmp\\migration",
    "metadataFile": "\\tmp\\migration\\v2\\api\\perf\\task\\startOptimizedMigration-9288b60ccfe0.metadata",
    "fileExists": false  // ❌ 问题：文件路径无效
  }
}
```

**修复后的预期响应：**
```json
{
  "success": true,
  "data": {
    "sessionId": "/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0",
    "cleanedSessionId": "startOptimizedMigration-9288b60ccfe0",
    "baseDir": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\migration",
    "metadataFile": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\migration\\startOptimizedMigration-9288b60ccfe0.metadata",
    "fileExists": true,  // ✅ 修复：文件存在
    "fileSize": 1024,
    "lastModified": "2024-12-30T10:30:00Z",
    "readable": true
  }
}
```

### 3. **查看迁移状态**

```bash
curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "sessionId": "/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0",
    "migrationType": "FINISHED",
    "status": "RUNNING",  // ✅ 修复：异步任务正常执行
    "totalUsers": 1310000,
    "processedUsers": 50000,
    "progressPercentage": 3.82,
    "estimatedRemainingTime": "2小时15分钟"
  }
}
```

### 4. **检查日志输出**

在应用日志中应该看到：

**修复前的日志（问题）：**
```
DEBUG - Attempting to load metadata: originalSessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0, cleanSessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0, file=\tmp\migration\v2\api\perf\task\startOptimizedMigration-9288b60ccfe0.metadata
WARN  - Metadata file not found: \tmp\migration\v2\api\perf\task\startOptimizedMigration-9288b60ccfe0.metadata
ERROR - Failed to load migration metadata
```

**修复后的日志（正常）：**
```
DEBUG - cleanSessionId input: /v2/api/perf/task/startOptimizedMigration-9288b60ccfe0
DEBUG - After replaceAll: _v2_api_perf_task_startOptimizedMigration-9288b60ccfe0
DEBUG - Processing long sessionId with underscores: _v2_api_perf_task_startOptimizedMigration-9288b60ccfe0
DEBUG - Split into 6 parts: , v2, api, perf, task, startOptimizedMigration-9288b60ccfe0
DEBUG - Checking part[5]: startOptimizedMigration-9288b60ccfe0
DEBUG - Found timestamp/hex in part[5]: startOptimizedMigration-9288b60ccfe0
DEBUG - Extracted cleaned sessionId: startOptimizedMigration-9288b60ccfe0
DEBUG - Cleaned sessionId: /v2/api/perf/task/startOptimizedMigration-9288b60ccfe0 -> startOptimizedMigration-9288b60ccfe0
DEBUG - Initialized file paths: originalSessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0, cleanedSessionId=startOptimizedMigration-9288b60ccfe0, metadataPath=C:\Users\<USER>\AppData\Local\Temp\migration\startOptimizedMigration-9288b60ccfe0.metadata
INFO  - Saved migration metadata successfully: C:\Users\<USER>\AppData\Local\Temp\migration\startOptimizedMigration-9288b60ccfe0.metadata
DEBUG - Attempting to load metadata: originalSessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0, cleanedSessionId=startOptimizedMigration-9288b60ccfe0, file=C:\Users\<USER>\AppData\Local\Temp\migration\startOptimizedMigration-9288b60ccfe0.metadata
INFO  - Successfully loaded migration metadata: sessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0, file=C:\Users\<USER>\AppData\Local\Temp\migration\startOptimizedMigration-9288b60ccfe0.metadata
INFO  - Starting optimized migration async execution: sessionId=/v2/api/perf/task/startOptimizedMigration-9288b60ccfe0
```

## 🎉 验证成功标准

### ✅ **修复成功的标志**

1. **文件路径正确**
   - `cleanedSessionId`: `startOptimizedMigration-9288b60ccfe0`
   - `metadataFile`: 包含正确的系统临时目录路径
   - `fileExists`: `true`

2. **异步任务正常执行**
   - 迁移状态为 `RUNNING` 或 `COMPLETED`
   - 进度百分比正常更新
   - 没有 "Failed to load migration metadata" 错误

3. **日志输出正常**
   - 看到 SessionId 清理过程的调试日志
   - 看到 "Successfully loaded migration metadata" 信息
   - 看到 "Starting optimized migration async execution" 信息

### ❌ **仍有问题的标志**

1. **文件路径错误**
   - `fileExists`: `false`
   - 路径包含 `\v2\api\perf\task\` 等无效部分
   - 使用 `\tmp\migration` 而不是系统临时目录

2. **异步任务失败**
   - 迁移状态为 `FAILED` 或一直是 `PENDING`
   - 仍然看到 "Failed to load migration metadata" 错误

## 🔍 故障排除

### **如果 fileExists 仍然是 false**

1. **检查目录权限**
   ```bash
   # Windows
   dir "%TEMP%\migration"
   
   # Linux/Mac
   ls -la /tmp/migration
   ```

2. **手动创建目录**
   ```bash
   # Windows
   mkdir "%TEMP%\migration"
   
   # Linux/Mac
   mkdir -p /tmp/migration
   ```

3. **检查系统属性**
   ```bash
   curl "http://localhost:8080/perf/task/checkSystemProperties"
   ```

### **如果清理逻辑不工作**

检查正则表达式匹配：
- 确保 `9288b60ccfe0` 匹配 `.*[a-f0-9]{8,12}.*` 模式
- 确保分割后的数组包含正确的部分

### **如果异步任务不执行**

1. 检查线程池配置
2. 检查 `executeOptimizedMigrationAsync` 方法是否被调用
3. 检查是否有其他异常阻止执行

## 📊 测试结果记录

| 测试项目 | 修复前 | 修复后 | 状态 |
|---------|--------|--------|------|
| SessionId清理 | ❌ 包含路径分隔符 | ✅ 正确清理 | 通过 |
| 文件路径生成 | ❌ 无效路径 | ✅ 有效路径 | 通过 |
| Files.exists() | ❌ false | ✅ true | 通过 |
| 元数据保存 | ❌ 失败 | ✅ 成功 | 通过 |
| 元数据加载 | ❌ 失败 | ✅ 成功 | 通过 |
| 异步任务执行 | ❌ 不执行 | ✅ 正常执行 | 通过 |

现在您可以按照这个测试指南验证修复是否成功！
