# loadMetadata 返回 null 问题修复指南

## 🔍 问题分析

在调试 `executeOptimizedMigrationAsync` 方法时，`OptimizedBitmapProgress.loadMetadata(sessionId)` 返回 null，导致异步执行流程无法继续。

### 问题原因分析

1. **文件路径问题**：元数据文件可能没有保存到正确的路径
2. **目录不存在**：保存文件的目录可能不存在
3. **权限问题**：可能没有读写权限
4. **异步执行时序问题**：异步方法可能在保存完成前就开始执行
5. **序列化问题**：对象序列化/反序列化可能失败

## ✅ 修复方案

### 1. **增强 saveMetadata 方法**

添加了目录创建和错误处理：

```java
public void saveMetadata() {
    try {
        // 确保目录存在
        Path metadataPath = Paths.get(metadataFilePath);
        Path parentDir = metadataPath.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
            log.info("Created metadata directory: {}", parentDir);
        }
        
        // 先保存当前位图段
        saveCurrentSegment();
        
        // 保存元数据
        try (ObjectOutputStream oos = new ObjectOutputStream(
                new BufferedOutputStream(Files.newOutputStream(metadataPath)))) {
            oos.writeObject(this);
            lastSaveTime = LocalDateTime.now();
            log.info("Saved migration metadata successfully: {}", metadataFilePath);
        }
        
    } catch (IOException e) {
        log.error("Failed to save migration metadata: {}", metadataFilePath, e);
        throw new RuntimeException("Failed to save migration metadata", e);
    }
}
```

### 2. **增强 loadMetadata 方法**

添加了详细的调试日志：

```java
public static OptimizedBitmapProgress loadMetadata(String sessionId) {
    String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
    String metadataFile = baseDir + "/" + sessionId + ".metadata";
    Path metadataPath = Paths.get(metadataFile);
    
    log.debug("Attempting to load metadata: sessionId={}, file={}", sessionId, metadataFile);
    
    if (Files.exists(metadataPath)) {
        try (ObjectInputStream ois = new ObjectInputStream(
                new BufferedInputStream(Files.newInputStream(metadataPath)))) {
            OptimizedBitmapProgress progress = (OptimizedBitmapProgress) ois.readObject();
            log.info("Successfully loaded migration metadata: sessionId={}, file={}", sessionId, metadataFile);
            return progress;
        } catch (Exception e) {
            log.error("Failed to load migration metadata: sessionId={}, file={}", sessionId, metadataFile, e);
        }
    } else {
        log.warn("Metadata file does not exist: sessionId={}, file={}", sessionId, metadataFile);
        
        // 列出目录中的文件以便调试
        try {
            Path parentDir = metadataPath.getParent();
            if (parentDir != null && Files.exists(parentDir)) {
                log.debug("Files in metadata directory: {}", 
                        Files.list(parentDir).map(Path::getFileName).collect(Collectors.toList()));
            } else {
                log.warn("Metadata directory does not exist: {}", parentDir);
            }
        } catch (Exception e) {
            log.debug("Failed to list metadata directory", e);
        }
    }
    
    return null;
}
```

### 3. **添加重试机制**

在 `executeOptimizedMigrationAsync` 中添加了重试逻辑：

```java
@Async("migrationTaskExecutor")
public void executeOptimizedMigrationAsync(String sessionId, String tid) {
    MDC.put("tid", tid);
    try {
        log.info("Starting async optimized migration execution for session: {}", sessionId);

        // 增加重试机制，因为异步执行可能在保存完成前开始
        OptimizedBitmapProgress progress = loadMetadataWithRetry(sessionId, 3, 1000);
        if (progress == null) {
            log.error("Migration progress not found for session after retries: {}", sessionId);
            return;
        }
        
        // 继续执行...
    } catch (Exception e) {
        log.error("Optimized migration failed for session: {}", sessionId, e);
        handleOptimizedMigrationFailure(sessionId, e.getMessage());
    } finally {
        MDC.remove("tid");
    }
}

private OptimizedBitmapProgress loadMetadataWithRetry(String sessionId, int maxRetries, long retryIntervalMs) {
    for (int i = 0; i <= maxRetries; i++) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress != null) {
            log.info("Successfully loaded metadata for session: {} on attempt {}", sessionId, i + 1);
            return progress;
        }
        
        if (i < maxRetries) {
            log.warn("Failed to load metadata for session: {} on attempt {}, retrying in {}ms", 
                    sessionId, i + 1, retryIntervalMs);
            try {
                Thread.sleep(retryIntervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting to retry metadata loading");
                break;
            }
        }
    }
    
    log.error("Failed to load metadata for session: {} after {} attempts", sessionId, maxRetries + 1);
    return null;
}
```

### 4. **添加调试接口**

新增了元数据文件状态检查接口：

```java
@RequestMapping("perf/task/checkMetadataFile")
public SingleResponse checkMetadataFile(String sessionId) {
    Map<String, Object> result = new HashMap<>();
    
    // 检查文件路径
    String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
    String metadataFile = baseDir + "/" + sessionId + ".metadata";
    Path metadataPath = Paths.get(metadataFile);
    
    result.put("sessionId", sessionId);
    result.put("baseDir", baseDir);
    result.put("metadataFile", metadataFile);
    result.put("fileExists", Files.exists(metadataPath));
    
    if (Files.exists(metadataPath)) {
        result.put("fileSize", Files.size(metadataPath));
        result.put("lastModified", Files.getLastModifiedTime(metadataPath).toString());
        result.put("readable", Files.isReadable(metadataPath));
    }
    
    // 检查目录
    Path parentDir = metadataPath.getParent();
    if (parentDir != null) {
        result.put("dirExists", Files.exists(parentDir));
        result.put("dirWritable", Files.isWritable(parentDir));
    }
    
    return SingleResponse.of(result);
}
```

## 🚀 调试步骤

### 1. **检查元数据文件状态**

调用调试接口检查文件状态：

```bash
curl "http://localhost:8080/perf/task/checkMetadataFile?sessionId=YOUR_SESSION_ID"
```

预期响应：
```json
{
  "success": true,
  "data": {
    "sessionId": "OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4",
    "baseDir": "/tmp/migration",
    "metadataFile": "/tmp/migration/OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4.metadata",
    "fileExists": true,
    "fileSize": 2048,
    "lastModified": "2024-12-29T10:30:00Z",
    "readable": true,
    "dirExists": true,
    "dirWritable": true
  }
}
```

### 2. **查看日志输出**

启动迁移时应该看到：
```
INFO - Saved migration metadata successfully: /tmp/migration/OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4.metadata
INFO - Starting async optimized migration execution for session: OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4
INFO - Successfully loaded metadata for session: OPTIMIZED_FINISHED_MIGRATION_1735459200000_a1b2c3d4 on attempt 1
```

### 3. **检查文件系统**

手动检查文件是否存在：
```bash
ls -la /tmp/migration/
ls -la /tmp/migration/*YOUR_SESSION_ID*
```

### 4. **检查权限**

确保应用有读写权限：
```bash
# 检查目录权限
ls -ld /tmp/migration/

# 如果需要，创建目录并设置权限
mkdir -p /tmp/migration
chmod 755 /tmp/migration
```

## 🔧 常见问题和解决方案

### 问题1：目录不存在

**现象**：日志显示 "Metadata directory does not exist"

**解决方案**：
```bash
mkdir -p /tmp/migration
chmod 755 /tmp/migration
```

### 问题2：权限不足

**现象**：日志显示 "Permission denied"

**解决方案**：
```bash
chmod 755 /tmp/migration
chown your_app_user:your_app_group /tmp/migration
```

### 问题3：序列化失败

**现象**：日志显示 "ClassNotFoundException" 或 "InvalidClassException"

**解决方案**：
1. 确保 `OptimizedBitmapProgress` 类实现了 `Serializable`
2. 检查类的 `serialVersionUID`
3. 确保所有字段都是可序列化的

### 问题4：异步时序问题

**现象**：文件存在但加载时为null

**解决方案**：
- 重试机制已经添加，会自动重试3次
- 可以增加重试次数或间隔时间

### 问题5：文件损坏

**现象**：文件存在但无法读取

**解决方案**：
```bash
# 删除损坏的文件，重新启动迁移
rm /tmp/migration/YOUR_SESSION_ID.metadata
```

## 🎯 验证修复效果

### 成功的调试流程

1. **启动迁移**：
   ```bash
   curl -X POST "http://localhost:8080/perf/task/startOptimizedMigration" \
     -d "migrationType=FINISHED"
   ```

2. **检查文件状态**：
   ```bash
   curl "http://localhost:8080/perf/task/checkMetadataFile?sessionId=返回的会话ID"
   ```

3. **查看迁移状态**：
   ```bash
   curl "http://localhost:8080/perf/task/getOptimizedMigrationStatus?sessionId=返回的会话ID"
   ```

### 预期结果

- 元数据文件成功创建
- 异步任务成功加载进度对象
- 迁移流程正常执行
- 不再出现 "Migration progress not found" 错误

通过这些修复，您的 `executeOptimizedMigrationAsync` 方法应该能够正常加载元数据并继续执行了！
