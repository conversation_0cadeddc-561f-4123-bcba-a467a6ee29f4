# resumeOptimizedMigration 方法修正说明

## 🔧 问题分析

您提到的问题很重要！`resumeOptimizedMigration` 是业务层方法，而 `progress.resume()` 是实体层方法，它们的层次和职责不同。

### ❌ 原始问题
```java
// 业务层方法（ScorerDataMingrationAppSvc）
public boolean resumeOptimizedMigration(String sessionId) {
    // ...
    progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);  // 直接设置状态
    progress.saveMetadata();
    // ...
}

// 实体层方法（OptimizedBitmapProgress）
public void resume() {  // 这个方法之前不存在
    // ...
}
```

### ✅ 修正方案

## 📋 修正的内容

### 1. **添加缺失的实体层方法**

#### **添加 `canResume()` 方法**
```java
/**
 * 检查是否可以恢复
 * 只有暂停状态的任务可以恢复
 */
public boolean canResume() {
    return status == MigrationStatus.PAUSED;
}
```

#### **添加 `setStatus()` 方法**
```java
/**
 * 设置迁移状态
 * 注意：建议使用具体的状态方法如start(), pause(), resume(), complete()等
 */
public void setStatus(MigrationStatus status) { 
    this.status = status;
    this.lastUpdateTime = LocalDateTime.now();
}
```

#### **添加 `stop()` 方法**
```java
/**
 * 停止迁移
 */
public void stop() {
    this.status = MigrationStatus.STOPPED;
    this.endTime = LocalDateTime.now();
    this.lastUpdateTime = LocalDateTime.now();
    saveMetadata(); // 停止时保存进度
    log.info("Migration stopped: sessionId={}", sessionId);
}
```

### 2. **修正业务层方法使用实体层方法**

#### **修正前（直接设置状态）**
```java
public boolean resumeOptimizedMigration(String sessionId) {
    // ...
    if (!progress.canResume()) {
        return false;
    }
    
    // ❌ 直接设置状态，不规范
    progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
    progress.saveMetadata();
    // ...
}
```

#### **修正后（使用实体方法）**
```java
public boolean resumeOptimizedMigration(String sessionId) {
    // ...
    if (!progress.canResume()) {
        return false;
    }
    
    // ✅ 使用实体的resume()方法，更规范
    progress.resume();  // 内部会自动设置状态、更新时间、保存元数据
    // ...
}
```

### 3. **添加完整的业务层方法**

#### **添加 `stopOptimizedMigration` 方法**
```java
/**
 * 停止优化迁移任务
 *
 * @param sessionId 会话ID
 * @return 是否成功停止
 */
public boolean stopOptimizedMigration(String sessionId) {
    log.info("Stopping optimized migration: {}", sessionId);

    if (!useBitmapOptimization) {
        log.warn("Bitmap optimization not enabled, cannot stop optimized migration");
        return false;
    }

    try {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null) {
            log.error("Migration progress not found for session: {}", sessionId);
            return false;
        }

        if (progress.isCompleted() || progress.isStopped()) {
            log.warn("Migration is already completed or stopped: {}", progress.getStatus());
            return false;
        }

        // 停止迁移 - 使用实体的stop()方法
        progress.stop();
        log.info("Optimized migration stopped successfully: {}", sessionId);
        return true;

    } catch (Exception e) {
        log.error("Failed to stop optimized migration: {}", sessionId, e);
        return false;
    }
}
```

## 🎯 层次结构说明

### **实体层（OptimizedBitmapProgress）**
负责状态管理和数据持久化：
```java
// 基础状态操作
public void start()     // 开始迁移
public void pause()     // 暂停迁移
public void resume()    // 恢复迁移
public void stop()      // 停止迁移
public void complete()  // 完成迁移

// 状态检查
public boolean isRunning()
public boolean isPaused()
public boolean canResume()
public boolean isCompleted()
public boolean isStopped()
```

### **业务层（ScorerDataMingrationAppSvc）**
负责业务逻辑和流程控制：
```java
// 业务操作
public boolean resumeOptimizedMigration(String sessionId)  // 恢复业务流程
public boolean pauseOptimizedMigration(String sessionId)   // 暂停业务流程
public boolean stopOptimizedMigration(String sessionId)    // 停止业务流程

// 状态查询
public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId)
```

## 📊 方法对应关系

| 业务层方法 | 实体层方法 | 说明 |
|-----------|-----------|------|
| `resumeOptimizedMigration()` | `progress.resume()` | 恢复迁移 |
| `pauseOptimizedMigration()` | `progress.pause()` | 暂停迁移 |
| `stopOptimizedMigration()` | `progress.stop()` | 停止迁移 |
| `startOptimizedMigration()` | `progress.start()` | 开始迁移 |

## 🎯 使用示例

### **正确的调用方式**

#### **在Controller中调用业务方法**
```java
@RequestMapping("perf/task/resumeOptimizedMigration")
public SingleResponse resumeOptimizedMigration(String sessionId) {
    try {
        // 调用业务层方法
        boolean success = mingrationAppSvc.resumeOptimizedMigration(sessionId);
        return SingleResponse.of(success);
    } catch (Exception e) {
        return SingleResponse.buildFailure("RESUME_ERROR", e.getMessage());
    }
}
```

#### **在业务方法中调用实体方法**
```java
public boolean resumeOptimizedMigration(String sessionId) {
    // 业务逻辑验证
    if (!useBitmapOptimization) {
        return false;
    }
    
    // 加载实体
    OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
    if (progress == null || !progress.canResume()) {
        return false;
    }
    
    // 调用实体方法
    progress.resume();  // 实体负责状态管理
    
    // 业务逻辑处理
    executeOptimizedMigrationAsync(sessionId);
    
    return true;
}
```

#### **在测试中的使用**
```java
@Test
public void testResumeOptimizedMigration() {
    // 测试业务层方法
    boolean result = appSvc.resumeOptimizedMigration(TEST_SESSION_ID);
    Assert.assertTrue(result);
}

@Test
public void testProgressResume() {
    // 测试实体层方法
    OptimizedBitmapProgress progress = createMockProgress();
    progress.pause();
    Assert.assertTrue(progress.canResume());
    
    progress.resume();
    Assert.assertTrue(progress.isRunning());
}
```

## 📋 修正总结

### **修正的文件：2个**
- `OptimizedBitmapProgress.java` - 添加了实体层方法
- `ScorerDataMingrationAppSvc.java` - 修正了业务层方法调用

### **添加的方法：4个**
- `canResume()` - 检查是否可以恢复
- `setStatus()` - 设置状态（通用方法）
- `stop()` - 停止迁移
- `stopOptimizedMigration()` - 停止迁移业务方法

### **修正的调用：1个**
- `resumeOptimizedMigration()` 中使用 `progress.resume()` 而不是直接设置状态

### **设计原则**
- **单一职责**：实体负责状态，业务负责流程
- **封装性**：通过方法而不是直接操作属性
- **一致性**：所有状态操作都通过实体方法
- **可维护性**：清晰的层次结构

现在业务层和实体层的方法调用关系更加清晰和规范了！
