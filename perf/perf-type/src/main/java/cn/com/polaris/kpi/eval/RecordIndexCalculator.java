package cn.com.polaris.kpi.eval;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 记录索引计算器
 * 为优化位图方案提供精确的记录索引计算
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Component
@Slf4j
public class RecordIndexCalculator {

    /**
     * 公司起始索引缓存
     */
    private final Map<String, Long> companyStartIndexCache = new ConcurrentHashMap<>();

    /**
     * 计算记录的全局索引
     * 
     * @param companyId 公司ID
     * @param companyPage 公司页码
     * @param userPage 用户页码
     * @param userIndexInPage 用户在页面中的索引
     * @param companyPageSize 公司页大小
     * @param userPageSize 用户页大小
     * @return 全局记录索引
     */
    public long calculateGlobalIndex(String companyId, int companyPage, int userPage, 
                                   int userIndexInPage, int companyPageSize, int userPageSize) {
        
        // 方案1：基于页码的线性计算（推荐用于迁移场景）
        return calculateLinearIndex(companyPage, userPage, userIndexInPage, companyPageSize, userPageSize);
    }

    /**
     * 线性索引计算（基于分页顺序）
     * 适用于数据迁移场景，保证索引的连续性和可预测性
     * 
     * @param companyPage 公司页码（从1开始）
     * @param userPage 用户页码（从1开始）
     * @param userIndexInPage 用户在页面中的索引（从0开始）
     * @param companyPageSize 公司页大小
     * @param userPageSize 用户页大小
     * @return 线性索引
     */
    private long calculateLinearIndex(int companyPage, int userPage, int userIndexInPage, 
                                    int companyPageSize, int userPageSize) {
        
        // 计算前面所有公司页的记录数
        long previousCompanyPagesRecords = (long) (companyPage - 1) * companyPageSize * userPageSize;
        
        // 计算当前公司页中前面用户页的记录数
        long previousUserPagesRecords = (long) (userPage - 1) * userPageSize;
        
        // 当前用户在页面中的位置
        long currentUserIndex = userIndexInPage;
        
        // 全局索引
        long globalIndex = previousCompanyPagesRecords + previousUserPagesRecords + currentUserIndex;
        
        log.debug("Linear index calculation: companyPage={}, userPage={}, userIndex={} -> globalIndex={}", 
                companyPage, userPage, userIndexInPage, globalIndex);
        
        return globalIndex;
    }

    /**
     * 基于公司实际数据量的索引计算
     * 适用于需要精确反映数据分布的场景
     * 
     * @param companyId 公司ID
     * @param userPage 用户页码
     * @param userIndexInPage 用户在页面中的索引
     * @param userPageSize 用户页大小
     * @return 基于实际数据的索引
     */
    public long calculateDataBasedIndex(String companyId, int userPage, int userIndexInPage, int userPageSize) {
        
        // 获取公司的起始索引（缓存）
        long companyStartIndex = getCompanyStartIndex(companyId);
        
        // 计算公司内的相对索引
        long relativeIndex = (long) (userPage - 1) * userPageSize + userIndexInPage;
        
        // 全局索引
        long globalIndex = companyStartIndex + relativeIndex;
        
        log.debug("Data-based index calculation: companyId={}, userPage={}, userIndex={} -> globalIndex={}", 
                companyId, userPage, userIndexInPage, globalIndex);
        
        return globalIndex;
    }

    /**
     * 获取公司的起始索引
     * 
     * @param companyId 公司ID
     * @return 起始索引
     */
    private long getCompanyStartIndex(String companyId) {
        return companyStartIndexCache.computeIfAbsent(companyId, this::calculateCompanyStartIndex);
    }

    /**
     * 计算公司的起始索引
     * 这里需要根据您的业务逻辑实现
     * 
     * @param companyId 公司ID
     * @return 起始索引
     */
    private long calculateCompanyStartIndex(String companyId) {
        // 方案1：基于公司ID的哈希值
        // return Math.abs(companyId.hashCode()) % 1000000;
        
        // 方案2：基于数据库查询（推荐）
        // 这里需要调用您的仓储方法获取公司在全局排序中的位置
        // 例如：SELECT SUM(user_count) FROM companies WHERE company_id < ?
        
        // 方案3：基于公司创建时间排序
        // 根据公司创建时间确定在全局索引中的位置
        
        // 临时实现：简单的哈希计算
        long hash = Math.abs(companyId.hashCode());
        return (hash % 100) * 10000; // 每个公司预留1万个索引位置
    }

    /**
     * 反向计算：从全局索引获取位置信息
     * 
     * @param globalIndex 全局索引
     * @param companyPageSize 公司页大小
     * @param userPageSize 用户页大小
     * @return 位置信息
     */
    public IndexPosition getPositionFromIndex(long globalIndex, int companyPageSize, int userPageSize) {
        
        long recordsPerCompanyPage = (long) companyPageSize * userPageSize;
        
        // 计算公司页码
        int companyPage = (int) (globalIndex / recordsPerCompanyPage) + 1;
        
        // 计算公司页内的相对索引
        long indexInCompanyPage = globalIndex % recordsPerCompanyPage;
        
        // 计算用户页码
        int userPage = (int) (indexInCompanyPage / userPageSize) + 1;
        
        // 计算用户在页面中的索引
        int userIndexInPage = (int) (indexInCompanyPage % userPageSize);
        
        return new IndexPosition(companyPage, userPage, userIndexInPage);
    }

    /**
     * 验证索引计算的正确性
     * 
     * @param companyPage 公司页码
     * @param userPage 用户页码
     * @param userIndexInPage 用户索引
     * @param companyPageSize 公司页大小
     * @param userPageSize 用户页大小
     * @return 是否验证通过
     */
    public boolean validateIndexCalculation(int companyPage, int userPage, int userIndexInPage, 
                                          int companyPageSize, int userPageSize) {
        
        // 正向计算
        long globalIndex = calculateLinearIndex(companyPage, userPage, userIndexInPage, companyPageSize, userPageSize);
        
        // 反向计算
        IndexPosition position = getPositionFromIndex(globalIndex, companyPageSize, userPageSize);
        
        // 验证是否一致
        boolean isValid = position.getCompanyPage() == companyPage &&
                         position.getUserPage() == userPage &&
                         position.getUserIndexInPage() == userIndexInPage;
        
        if (!isValid) {
            log.error("Index calculation validation failed: original({},{},{}) -> index({}) -> calculated({},{},{})",
                    companyPage, userPage, userIndexInPage, globalIndex,
                    position.getCompanyPage(), position.getUserPage(), position.getUserIndexInPage());
        }
        
        return isValid;
    }

    /**
     * 清理缓存
     */
    public void clearCache() {
        companyStartIndexCache.clear();
        log.info("Record index calculator cache cleared");
    }

    /**
     * 位置信息内部类
     */
    public static class IndexPosition {
        private final int companyPage;
        private final int userPage;
        private final int userIndexInPage;

        public IndexPosition(int companyPage, int userPage, int userIndexInPage) {
            this.companyPage = companyPage;
            this.userPage = userPage;
            this.userIndexInPage = userIndexInPage;
        }

        public int getCompanyPage() { return companyPage; }
        public int getUserPage() { return userPage; }
        public int getUserIndexInPage() { return userIndexInPage; }

        @Override
        public String toString() {
            return String.format("IndexPosition{companyPage=%d, userPage=%d, userIndexInPage=%d}", 
                    companyPage, userPage, userIndexInPage);
        }
    }

    /**
     * 批量计算索引（优化性能）
     * 
     * @param companyId 公司ID
     * @param userPage 用户页码
     * @param userCount 用户数量
     * @param userPageSize 用户页大小
     * @return 索引数组
     */
    public long[] calculateBatchIndexes(String companyId, int userPage, int userCount, int userPageSize) {
        long[] indexes = new long[userCount];
        
        for (int i = 0; i < userCount; i++) {
            indexes[i] = calculateDataBasedIndex(companyId, userPage, i, userPageSize);
        }
        
        return indexes;
    }
}
