package cn.com.polaris.kpi.eval;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.BitSet;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 并发安全的位图管理器
 * 解决分段文件的并发访问问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Component
@Slf4j
public class ConcurrentBitMap {

    /**
     * 段级别的读写锁
     */
    private final ConcurrentHashMap<String, ReadWriteLock> segmentLocks = new ConcurrentHashMap<>();

    /**
     * 内存中的位图段缓存
     */
    private final ConcurrentHashMap<String, BitSet> segmentCache = new ConcurrentHashMap<>();

    /**
     * 段的最后访问时间（用于LRU清理）
     */
    private final ConcurrentHashMap<String, Long> lastAccessTime = new ConcurrentHashMap<>();

    /**
     * 最大缓存段数
     */
    private static final int MAX_CACHED_SEGMENTS = 5;

    /**
     * 线程安全地获取位图段
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @return 位图段
     */
    public BitSet getSegment(String sessionId, long segmentIndex) {
        String segmentKey = buildSegmentKey(sessionId, segmentIndex);
        ReadWriteLock lock = getSegmentLock(segmentKey);
        
        lock.readLock().lock();
        try {
            // 先从缓存获取
            BitSet cached = segmentCache.get(segmentKey);
            if (cached != null) {
                updateAccessTime(segmentKey);
                return (BitSet) cached.clone(); // 返回副本避免并发修改
            }
        } finally {
            lock.readLock().unlock();
        }
        
        // 缓存未命中，需要从文件加载
        lock.writeLock().lock();
        try {
            // 双重检查
            BitSet cached = segmentCache.get(segmentKey);
            if (cached != null) {
                updateAccessTime(segmentKey);
                return (BitSet) cached.clone();
            }
            
            // 从文件加载
            BitSet segment = loadSegmentFromFile(sessionId, segmentIndex);
            
            // 缓存管理
            manageCache(segmentKey, segment);
            
            return (BitSet) segment.clone();
            
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 线程安全地更新位图段
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @param segment 位图段
     */
    public void updateSegment(String sessionId, long segmentIndex, BitSet segment) {
        String segmentKey = buildSegmentKey(sessionId, segmentIndex);
        ReadWriteLock lock = getSegmentLock(segmentKey);
        
        lock.writeLock().lock();
        try {
            // 更新缓存
            segmentCache.put(segmentKey, (BitSet) segment.clone());
            updateAccessTime(segmentKey);
            
            // 异步保存到文件
            saveSegmentToFileAsync(sessionId, segmentIndex, segment);
            
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 线程安全地设置位图位
     * 
     * @param sessionId 会话ID
     * @param globalIndex 全局索引
     * @param segmentSize 段大小
     */
    public void setBit(String sessionId, long globalIndex, int segmentSize) {
        long segmentIndex = globalIndex / segmentSize;
        int localIndex = (int) (globalIndex % segmentSize);
        
        String segmentKey = buildSegmentKey(sessionId, segmentIndex);
        ReadWriteLock lock = getSegmentLock(segmentKey);
        
        lock.writeLock().lock();
        try {
            BitSet segment = getOrCreateSegment(sessionId, segmentIndex);
            segment.set(localIndex);
            
            // 更新缓存
            segmentCache.put(segmentKey, segment);
            updateAccessTime(segmentKey);
            
            // 标记需要保存
            markSegmentDirty(segmentKey);
            
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 线程安全地检查位图位
     * 
     * @param sessionId 会话ID
     * @param globalIndex 全局索引
     * @param segmentSize 段大小
     * @return 是否已设置
     */
    public boolean getBit(String sessionId, long globalIndex, int segmentSize) {
        // 🔧 修复：检查segmentSize是否为0，避免除零错误
        if (segmentSize <= 0) {
            log.warn("Invalid segmentSize: {}, using default value 100000", segmentSize);
            segmentSize = 100000; // 使用默认段大小
        }

        long segmentIndex = globalIndex / segmentSize;
        int localIndex = (int) (globalIndex % segmentSize);

        BitSet segment = getSegment(sessionId, segmentIndex);
        return segment.get(localIndex);
    }

    /**
     * 获取或创建位图段
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @return 位图段
     */
    private BitSet getOrCreateSegment(String sessionId, long segmentIndex) {
        String segmentKey = buildSegmentKey(sessionId, segmentIndex);
        
        BitSet segment = segmentCache.get(segmentKey);
        if (segment == null) {
            segment = loadSegmentFromFile(sessionId, segmentIndex);
            if (segment == null) {
                segment = new BitSet(100000); // 默认段大小
            }
        }
        
        return segment;
    }

    /**
     * 从文件加载位图段（带文件锁）
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @return 位图段
     */
    private BitSet loadSegmentFromFile(String sessionId, long segmentIndex) {
        String segmentFile = buildSegmentFilePath(sessionId, segmentIndex);
        Path segmentPath = Paths.get(segmentFile);
        
        if (!Files.exists(segmentPath)) {
            return new BitSet(100000);
        }
        
        try (FileChannel channel = FileChannel.open(segmentPath, StandardOpenOption.READ);
             FileLock lock = channel.tryLock(0L, Long.MAX_VALUE, true)) { // 共享锁
            
            if (lock == null) {
                log.warn("Could not acquire file lock for reading: {}", segmentFile);
                return new BitSet(100000);
            }
            
            try (ObjectInputStream ois = new ObjectInputStream(
                    new BufferedInputStream(Files.newInputStream(segmentPath)))) {
                
                BitSet segment = (BitSet) ois.readObject();
                log.debug("Loaded bitmap segment from file: {}", segmentFile);
                return segment;
                
            } catch (ClassNotFoundException e) {
                log.error("Failed to deserialize bitmap segment: {}", segmentFile, e);
                return new BitSet(100000);
            }
            
        } catch (IOException e) {
            log.error("Failed to load bitmap segment: {}", segmentFile, e);
            return new BitSet(100000);
        }
    }

    /**
     * 异步保存位图段到文件（带文件锁）
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @param segment 位图段
     */
    private void saveSegmentToFileAsync(String sessionId, long segmentIndex, BitSet segment) {
        // 使用线程池异步保存
        CompletableFuture.runAsync(() -> saveSegmentToFile(sessionId, segmentIndex, segment));
    }

    /**
     * 保存位图段到文件（带文件锁）
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @param segment 位图段
     */
    private void saveSegmentToFile(String sessionId, long segmentIndex, BitSet segment) {
        String segmentFile = buildSegmentFilePath(sessionId, segmentIndex);
        Path segmentPath = Paths.get(segmentFile);
        
        try {
            // 确保目录存在
            Files.createDirectories(segmentPath.getParent());
            
            try (FileChannel channel = FileChannel.open(segmentPath, 
                    StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);
                 FileLock lock = channel.tryLock()) { // 独占锁
                
                if (lock == null) {
                    log.warn("Could not acquire file lock for writing: {}", segmentFile);
                    return;
                }
                
                try (ObjectOutputStream oos = new ObjectOutputStream(
                        new BufferedOutputStream(Files.newOutputStream(segmentPath)))) {
                    
                    oos.writeObject(segment);
                    oos.flush();
                    log.debug("Saved bitmap segment to file: {}", segmentFile);
                }
            }
        } catch (IOException e) {
            log.error("Failed to save bitmap segment: {}", segmentFile, e);
        }
    }

    /**
     * 获取段锁
     * 
     * @param segmentKey 段键
     * @return 读写锁
     */
    private ReadWriteLock getSegmentLock(String segmentKey) {
        return segmentLocks.computeIfAbsent(segmentKey, k -> new ReentrantReadWriteLock());
    }

    /**
     * 构建段键
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @return 段键
     */
    private String buildSegmentKey(String sessionId, long segmentIndex) {
        return sessionId + "_seg_" + segmentIndex;
    }

    /**
     * 构建段文件路径
     * 
     * @param sessionId 会话ID
     * @param segmentIndex 段索引
     * @return 文件路径
     */
    private String buildSegmentFilePath(String sessionId, long segmentIndex) {
        String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
        return baseDir + "/" + sessionId + ".bitmap.seg" + segmentIndex;
    }

    /**
     * 更新访问时间
     * 
     * @param segmentKey 段键
     */
    private void updateAccessTime(String segmentKey) {
        lastAccessTime.put(segmentKey, System.currentTimeMillis());
    }

    /**
     * 管理缓存（LRU清理）
     * 
     * @param segmentKey 段键
     * @param segment 位图段
     */
    private void manageCache(String segmentKey, BitSet segment) {
        segmentCache.put(segmentKey, segment);
        updateAccessTime(segmentKey);
        
        // 如果缓存超过限制，清理最久未使用的段
        if (segmentCache.size() > MAX_CACHED_SEGMENTS) {
            cleanupOldestSegment();
        }
    }

    /**
     * 清理最久未使用的段
     */
    private void cleanupOldestSegment() {
        String oldestKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, Long> entry : lastAccessTime.entrySet()) {
            if (entry.getValue() < oldestTime) {
                oldestTime = entry.getValue();
                oldestKey = entry.getKey();
            }
        }
        
        if (oldestKey != null) {
            segmentCache.remove(oldestKey);
            lastAccessTime.remove(oldestKey);
            segmentLocks.remove(oldestKey);
            log.debug("Cleaned up oldest segment from cache: {}", oldestKey);
        }
    }

    /**
     * 标记段为脏（需要保存）
     * 
     * @param segmentKey 段键
     */
    private void markSegmentDirty(String segmentKey) {
        // 可以实现一个脏段跟踪机制，定期批量保存
        // 这里简化处理，直接异步保存
    }

    /**
     * 强制保存所有缓存的段
     * 
     * @param sessionId 会话ID
     */
    public void flushAllSegments(String sessionId) {
        log.info("Flushing all segments for session: {}", sessionId);
        
        segmentCache.entrySet().parallelStream()
                .filter(entry -> entry.getKey().startsWith(sessionId))
                .forEach(entry -> {
                    String segmentKey = entry.getKey();
                    long segmentIndex = extractSegmentIndex(segmentKey);
                    saveSegmentToFile(sessionId, segmentIndex, entry.getValue());
                });
    }

    /**
     * 从段键提取段索引
     * 
     * @param segmentKey 段键
     * @return 段索引
     */
    private long extractSegmentIndex(String segmentKey) {
        String[] parts = segmentKey.split("_seg_");
        return Long.parseLong(parts[1]);
    }

    /**
     * 清理会话相关的所有资源
     * 
     * @param sessionId 会话ID
     */
    public void cleanupSession(String sessionId) {
        log.info("Cleaning up session resources: {}", sessionId);
        
        // 清理缓存
        segmentCache.entrySet().removeIf(entry -> entry.getKey().startsWith(sessionId));
        lastAccessTime.entrySet().removeIf(entry -> entry.getKey().startsWith(sessionId));
        segmentLocks.entrySet().removeIf(entry -> entry.getKey().startsWith(sessionId));
        
        // 删除文件
        try {
            String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
            Files.list(Paths.get(baseDir))
                    .filter(path -> path.getFileName().toString().startsWith(sessionId))
                    .forEach(path -> {
                        try {
                            Files.deleteIfExists(path);
                        } catch (IOException e) {
                            log.warn("Failed to delete file: {}", path, e);
                        }
                    });
        } catch (IOException e) {
            log.error("Failed to cleanup session files: {}", sessionId, e);
        }
    }
}
