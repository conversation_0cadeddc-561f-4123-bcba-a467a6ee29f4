package com.polaris.kpi.eval.domain.migration.entity;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * OptimizedBitmapProgress 单元测试
 */
@RunWith(PowerMockRunner.class)
public class OptimizedBitmapProgressTest {

    private OptimizedBitmapProgress progress;
    private static final String TEST_SESSION_ID = "TEST_SESSION_001";
    private static final String TEST_MIGRATION_TYPE = "FINISHED";
    private static final String TEST_USER_ID = "test_user_001";
    private static final String TEST_COMPANY_ID = "test_company_001";

    @Before
    public void setUp() {
        progress = new OptimizedBitmapProgress(TEST_SESSION_ID, TEST_MIGRATION_TYPE);
    }

    @After
    public void tearDown() {
        progress = null;
    }

    // ==================== 基础功能测试 ====================

    /**
     * 测试构造函数和基础属性
     */
    @Test
    public void testConstructorAndBasicProperties() {
        // Then
        Assert.assertEquals("会话ID应该匹配", TEST_SESSION_ID, progress.getSessionId());
        Assert.assertEquals("迁移类型应该匹配", TEST_MIGRATION_TYPE, progress.getMigrationType());
        Assert.assertEquals("初始状态应该是PENDING", OptimizedBitmapProgress.MigrationStatus.PENDING, progress.getStatus());
        Assert.assertNotNull("创建时间不应该为空", progress.getCreatedTime());
        Assert.assertEquals("初始总记录数应该为0", 0, progress.getTotalRecords().get());
        Assert.assertEquals("初始处理数应该为0", 0, progress.getProcessedCount().get());
    }

    /**
     * 测试生命周期状态管理
     */
    @Test
    public void testLifecycleStatusManagement() {
        // Test start
        progress.start();
        Assert.assertEquals("启动后状态应该是RUNNING", OptimizedBitmapProgress.MigrationStatus.RUNNING, progress.getStatus());
        Assert.assertNotNull("启动时间不应该为空", progress.getStartTime());

        // Test pause
        progress.pause();
        Assert.assertEquals("暂停后状态应该是PAUSED", OptimizedBitmapProgress.MigrationStatus.PAUSED, progress.getStatus());

        // Test resume
        progress.resume();
        Assert.assertEquals("恢复后状态应该是RUNNING", OptimizedBitmapProgress.MigrationStatus.RUNNING, progress.getStatus());

        // Test complete
        progress.complete();
        Assert.assertEquals("完成后状态应该是COMPLETED", OptimizedBitmapProgress.MigrationStatus.COMPLETED, progress.getStatus());
        Assert.assertNotNull("结束时间不应该为空", progress.getEndTime());
    }

    /**
     * 测试计数器操作
     */
    @Test
    public void testCounterOperations() {
        // Set total records
        progress.getTotalRecords().set(1000);
        Assert.assertEquals("总记录数应该匹配", 1000, progress.getTotalRecords().get());

        // Test increment operations
        progress.incrementProcessed();
        progress.incrementSuccess();
        Assert.assertEquals("处理数应该为1", 1, progress.getProcessedCount().get());
        Assert.assertEquals("成功数应该为1", 1, progress.getSuccessCount().get());

        progress.incrementFailure();
        Assert.assertEquals("失败数应该为1", 1, progress.getFailureCount().get());

        // Test progress percentage
        double percentage = progress.getProgressPercentage();
        Assert.assertEquals("进度百分比应该正确", 0.1, percentage, 0.01);
    }

    /**
     * 测试状态检查方法
     */
    @Test
    public void testStatusCheckMethods() {
        // Initial state
        Assert.assertTrue("初始状态应该是pending", progress.isPending());
        Assert.assertFalse("初始状态不应该是running", progress.isRunning());
        Assert.assertFalse("初始状态不应该是completed", progress.isCompleted());

        // Running state
        progress.start();
        Assert.assertFalse("运行状态不应该是pending", progress.isPending());
        Assert.assertTrue("运行状态应该是running", progress.isRunning());
        Assert.assertFalse("运行状态不应该是completed", progress.isCompleted());

        // Completed state
        progress.complete();
        Assert.assertFalse("完成状态不应该是pending", progress.isPending());
        Assert.assertFalse("完成状态不应该是running", progress.isRunning());
        Assert.assertTrue("完成状态应该是completed", progress.isCompleted());
    }

    // ==================== 失败记录跟踪测试 ====================

    /**
     * 测试失败记录添加和管理
     */
    @Test
    public void testFailureRecordManagement() {
        // Add failure record
        progress.addFailureRecord(TEST_USER_ID, TEST_COMPANY_ID, 1001L, "测试错误", "TEST_ERROR");

        // Verify failure record
        List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getFailureRecords();
        Assert.assertEquals("失败记录数量应该为1", 1, failureRecords.size());

        OptimizedBitmapProgress.FailureRecord record = failureRecords.get(0);
        Assert.assertEquals("用户ID应该匹配", TEST_USER_ID, record.getUserId());
        Assert.assertEquals("公司ID应该匹配", TEST_COMPANY_ID, record.getCompanyId());
        Assert.assertEquals("全局索引应该匹配", 1001L, record.getGlobalIndex());
        Assert.assertEquals("错误信息应该匹配", "测试错误", record.getErrorMessage());
        Assert.assertEquals("错误类型应该匹配", "TEST_ERROR", record.getErrorType());
    }

    /**
     * 测试按错误类型获取失败记录
     */
    @Test
    public void testGetFailureRecordsByErrorType() {
        // Add multiple failure records with different error types
        progress.addFailureRecord(TEST_USER_ID + "_1", TEST_COMPANY_ID, 1001L, "超时错误", "TIMEOUT_ERROR");
        progress.addFailureRecord(TEST_USER_ID + "_2", TEST_COMPANY_ID, 1002L, "验证错误", "VALIDATION_ERROR");
        progress.addFailureRecord(TEST_USER_ID + "_3", TEST_COMPANY_ID, 1003L, "超时错误", "TIMEOUT_ERROR");

        // Get failure records by error type
        List<OptimizedBitmapProgress.FailureRecord> timeoutErrors = progress.getFailureRecordsByErrorType("TIMEOUT_ERROR");
        List<OptimizedBitmapProgress.FailureRecord> validationErrors = progress.getFailureRecordsByErrorType("VALIDATION_ERROR");

        Assert.assertEquals("超时错误记录数量应该为2", 2, timeoutErrors.size());
        Assert.assertEquals("验证错误记录数量应该为1", 1, validationErrors.size());
    }

    /**
     * 测试失败记录重试
     */
    @Test
    public void testFailureRecordRetry() {
        // Add failure record
        progress.addFailureRecord(TEST_USER_ID, TEST_COMPANY_ID, 1001L, "测试错误", "TEST_ERROR");
        OptimizedBitmapProgress.FailureRecord record = progress.getFailureRecords().get(0);

        // Test retry
        int initialRetryCount = record.getRetryCount();
        record.incrementRetryCount();
        Assert.assertEquals("重试次数应该增加", initialRetryCount + 1, record.getRetryCount());
        Assert.assertNotNull("最后重试时间不应该为空", record.getLastRetryTime());
    }

    // ==================== 任务分离功能测试 ====================

    /**
     * 测试启用任务分离
     */
    @Test
    public void testEnableTaskSeparation() {
        String relatedSessionId = "RELATED_SESSION_001";
        
        // Enable task separation
        progress.enableTaskSeparation(OptimizedBitmapProgress.TaskType.FINISHED_ONLY, relatedSessionId);

        Assert.assertTrue("任务分离应该被启用", progress.isEnableTaskSeparation());
        Assert.assertEquals("任务类型应该匹配", OptimizedBitmapProgress.TaskType.FINISHED_ONLY, progress.getTaskType());
        Assert.assertEquals("关联会话ID应该匹配", relatedSessionId, progress.getRelatedTaskSessionId());
    }

    /**
     * 测试状态变化检测逻辑
     */
    @Test
    public void testShouldDetectStatusChange() {
        // Enable task separation for FINISHED_ONLY
        progress.enableTaskSeparation(OptimizedBitmapProgress.TaskType.FINISHED_ONLY, "RELATED_SESSION");

        // Test detection logic
        Assert.assertTrue("应该检测FINISHED->NO_FINISHED变化", 
                progress.shouldDetectStatusChange("FINISHED", "NO_FINISHED"));
        Assert.assertFalse("不应该检测FINISHED->FINISHED变化", 
                progress.shouldDetectStatusChange("FINISHED", "FINISHED"));
        Assert.assertFalse("不应该检测NO_FINISHED->FINISHED变化", 
                progress.shouldDetectStatusChange("NO_FINISHED", "FINISHED"));
    }

    // ==================== 增量检测功能测试 ====================

    /**
     * 测试启用增量检测
     */
    @Test
    public void testEnableIncrementalDetection() {
        long checkInterval = 30;
        
        // Enable incremental detection
        progress.enableIncrementalDetection(checkInterval);

        Assert.assertTrue("增量检测应该被启用", progress.isEnableIncrementalDetection());
        Assert.assertEquals("检测间隔应该匹配", checkInterval, progress.getIncrementalCheckIntervalMinutes());
        Assert.assertNotNull("最后检测时间不应该为空", progress.getLastIncrementalCheckTime());
    }

    /**
     * 测试状态变化记录管理
     */
    @Test
    public void testStatusChangeRecordManagement() {
        String originalStatus = "FINISHED";
        String currentStatus = "NO_FINISHED";

        // Record status change
        progress.recordStatusChange(TEST_USER_ID, TEST_COMPANY_ID, originalStatus, currentStatus);

        // Verify status change record
        List<OptimizedBitmapProgress.StatusChangeRecord> statusChanges = progress.getAllStatusChangeRecords();
        Assert.assertEquals("状态变化记录数量应该为1", 1, statusChanges.size());

        OptimizedBitmapProgress.StatusChangeRecord record = statusChanges.get(0);
        Assert.assertEquals("用户ID应该匹配", TEST_USER_ID, record.getUserId());
        Assert.assertEquals("公司ID应该匹配", TEST_COMPANY_ID, record.getCompanyId());
        Assert.assertEquals("原始状态应该匹配", originalStatus, record.getOriginalStatus());
        Assert.assertEquals("当前状态应该匹配", currentStatus, record.getCurrentStatus());
        Assert.assertTrue("应该需要重新处理", record.isNeedsReprocessing());
    }

    // ==================== 动态扩展功能测试 ====================

    /**
     * 测试启用动态扩展
     */
    @Test
    public void testEnableDynamicExpansion() {
        long checkInterval = 15;
        
        // Enable dynamic expansion
        progress.enableDynamicExpansion(checkInterval);

        Assert.assertTrue("动态扩展应该被启用", progress.isEnableDynamicExpansion());
        Assert.assertEquals("检测间隔应该匹配", checkInterval, progress.getDynamicCheckIntervalMinutes());
        Assert.assertNotNull("最后检测时间不应该为空", progress.getLastDynamicCheckTime());
    }

    /**
     * 测试动态用户管理
     */
    @Test
    public void testDynamicUserManagement() {
        String userStatus = "FINISHED";
        String addedBy = "MANUAL";

        // Add dynamic user
        boolean success = progress.addDynamicUser(TEST_USER_ID, TEST_COMPANY_ID, userStatus, addedBy);
        Assert.assertTrue("添加动态用户应该成功", success);

        // Verify dynamic user
        List<OptimizedBitmapProgress.DynamicUserRecord> dynamicUsers = progress.getAllDynamicUsers();
        Assert.assertEquals("动态用户数量应该为1", 1, dynamicUsers.size());

        OptimizedBitmapProgress.DynamicUserRecord record = dynamicUsers.get(0);
        Assert.assertEquals("用户ID应该匹配", TEST_USER_ID, record.getUserId());
        Assert.assertEquals("公司ID应该匹配", TEST_COMPANY_ID, record.getCompanyId());
        Assert.assertEquals("用户状态应该匹配", userStatus, record.getUserStatus());
        Assert.assertEquals("添加来源应该匹配", addedBy, record.getAddedBy());
        Assert.assertTrue("应该需要处理", record.isNeedsProcessing());
        Assert.assertFalse("不应该已处理", record.isProcessed());
    }

    /**
     * 测试批量添加动态用户
     */
    @Test
    public void testBatchAddDynamicUsers() {
        Map<String, Map<String, String>> users = new HashMap<>();
        
        Map<String, String> user1 = new HashMap<>();
        user1.put("companyId", TEST_COMPANY_ID);
        user1.put("userStatus", "FINISHED");
        users.put(TEST_USER_ID + "_1", user1);
        
        Map<String, String> user2 = new HashMap<>();
        user2.put("companyId", TEST_COMPANY_ID);
        user2.put("userStatus", "FINISHED");
        users.put(TEST_USER_ID + "_2", user2);

        // Batch add dynamic users
        int successCount = progress.addDynamicUsers(users, "BATCH_MANUAL");
        Assert.assertEquals("成功添加数量应该为2", 2, successCount);

        // Verify dynamic users
        List<OptimizedBitmapProgress.DynamicUserRecord> dynamicUsers = progress.getAllDynamicUsers();
        Assert.assertEquals("动态用户总数应该为2", 2, dynamicUsers.size());
    }

    /**
     * 测试动态用户处理标记
     */
    @Test
    public void testMarkDynamicUserAsProcessed() {
        // Add dynamic user
        progress.addDynamicUser(TEST_USER_ID, TEST_COMPANY_ID, "FINISHED", "MANUAL");
        
        // Mark as processed
        long globalIndex = 1001L;
        progress.markDynamicUserAsProcessed(TEST_USER_ID, globalIndex);

        // Verify processed status
        List<OptimizedBitmapProgress.DynamicUserRecord> dynamicUsers = progress.getAllDynamicUsers();
        OptimizedBitmapProgress.DynamicUserRecord record = dynamicUsers.get(0);
        
        Assert.assertTrue("应该已处理", record.isProcessed());
        Assert.assertFalse("不应该需要处理", record.isNeedsProcessing());
        Assert.assertEquals("全局索引应该匹配", globalIndex, record.getGlobalIndex());
        Assert.assertNotNull("处理时间不应该为空", record.getProcessedTime());
    }

    // ==================== 统计信息测试 ====================

    /**
     * 测试获取统计信息
     */
    @Test
    public void testGetStatistics() {
        // Set up test data
        progress.getTotalRecords().set(1000);
        progress.getProcessedCount().set(500);
        progress.getSuccessCount().set(480);
        progress.getFailureCount().set(20);
        progress.addFailureRecord(TEST_USER_ID, TEST_COMPANY_ID, 1001L, "测试错误", "TEST_ERROR");

        // Get statistics
        String statistics = progress.getStatistics();

        Assert.assertNotNull("统计信息不应该为空", statistics);
        Assert.assertTrue("统计信息应该包含总记录数", statistics.contains("1000"));
        Assert.assertTrue("统计信息应该包含处理数", statistics.contains("500"));
        Assert.assertTrue("统计信息应该包含成功数", statistics.contains("480"));
        Assert.assertTrue("统计信息应该包含失败数", statistics.contains("20"));
    }

    /**
     * 测试获取动态扩展统计信息
     */
    @Test
    public void testGetDynamicExpansionStatistics() {
        // Add dynamic users
        progress.addDynamicUser(TEST_USER_ID + "_1", TEST_COMPANY_ID, "FINISHED", "MANUAL");
        progress.addDynamicUser(TEST_USER_ID + "_2", TEST_COMPANY_ID, "FINISHED", "AUTO_DETECTION");
        
        // Mark one as processed
        progress.markDynamicUserAsProcessed(TEST_USER_ID + "_1", 1001L);

        // Get statistics
        String statistics = progress.getDynamicExpansionStatistics();

        Assert.assertNotNull("动态扩展统计信息不应该为空", statistics);
        Assert.assertTrue("统计信息应该包含总数", statistics.contains("2"));
        Assert.assertTrue("统计信息应该包含待处理数", statistics.contains("1"));
        Assert.assertTrue("统计信息应该包含已处理数", statistics.contains("1"));
    }
}
