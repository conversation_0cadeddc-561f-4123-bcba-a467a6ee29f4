package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.EmpStaff;
import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.perf.www.common.em.CompanyItemTypeEnum;
import com.perf.www.common.em.KpiItemTypeEnum;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.domain.task.dmsvc.EvalChangeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleLogField;
import com.polaris.kpi.eval.domain.task.entity.admineval.EvalRuleOpLogMeta;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.flow.AsDisplayFlowRs;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.entity.js.JSElComputer;
import com.polaris.kpi.eval.domain.task.entity.log.ItemDynamicLog;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrAction;
import com.polaris.kpi.eval.domain.task.entity.okr.OkrActionUpdate;
import com.polaris.kpi.eval.domain.task.ext.EvalOkrItemInfo;
import com.polaris.kpi.eval.domain.task.type.NodeWeight;
import com.polaris.kpi.eval.domain.task.type.ResultInputEmp;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.extData.domain.entity.ExtDataItemFieldCorr;
import com.polaris.kpi.org.domain.emp.entity.LeaderManger;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.lufei.ibatis.common.data.ToDataBuilder;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * {
 * "id":"",
 * "companyId":"公司id",
 * "orgId":"部门id",
 * "taskId":"考核任务id",
 * "empId":"被考核人id",
 * "taskUserId":"task user 表的id",
 * "reviewer":"审核人信息",
 * "kpiTypeId":"指标类id",
 * "kpiTypeName":"指标类名称",
 * "kpiTypeWeight":"指标类权重",
 * "kpiItemId":"指标项id",
 * "kpiItemName":"指标项名称",
 * "itemTargetValue":"指标项目标值",
 * "itemFinishValue":"指标项完成值",
 * "itemUnit":"指标项单位",
 * "itemWeight":"指标项权重",
 * "resultInputType":"结果录入类型",
 * "resultInputEmpId":"结果录入人id",
 * "backup":"备份，用于指标变更审核时的恢复操作",
 * "examineOperType":"被考核人确认指标时操作类型：增删改",
 * "isDeleted":"是否删除",
 * "createdUser":"创建用户",
 * "createdTime":"创建时间",
 * "updatedUser":"修改用户",
 * "updatedTime":"修改时间",
 * "itemRule":"考核规则",
 * "scoringRule":"计分规则",
 * "scorerType":"指标评分人类型（按评分流程/指定员工/指定主管）",
 * "scorerObjId":"指定评分人json串",
 * "itemType":"指标项类型（量化/非量化）",
 * "multipleReviewersType":"多人审核时，and会签，or或签",
 * "kpiTypeClassify":"指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项",
 * "plusLimit":"加分上限",
 * "subtractLimit":"减分上限",
 * "maxExtraScore":"本类别最大加减分上限",
 * "order":"排序，数字小的排前面",
 * "itemFormula":"指标计算公式",
 * "itemAutoScore":"指标自动计算后的分数",
 * "thresholdJson":"量化指标阈值设置json",
 * "urgingFlag":"完成值录入催办标识",
 * "itemActualFormula":"代入真实值后的计算公式",
 * "autoScoreExFlag":"自动算分异常标识",
 * "formulaCondition":"公式条件",
 * "itemFieldJson":"指标关联的阈值字段",
 * "isTypeLocked":"类别锁定类型",
 * "isOkr":"是否OKR类别，true/false",
 * "typeOrder":"类别排序",
 * "okrRefFlag":"指标被OKR关联的标记",
 * "reserveOkrWeight":"预留OKR权重",
 * "pointsRule":"积分规则",
 * "pointsNum":"指标积分",
 * "itemScoreValue":"指标评分分值",
 * "inputFormat":"录入格式",
 * "workItemFinishValue":"工作事项完成情况说明",
 * "itemPlanFlag":"是否同步了年度指标计划",
 * "showTargetValue":"是否展示目标值",
 * "mustResultInput":"指标完成值是否必需录入",
 * "showFinishBar":"完成度进度条 默认开启 1=显示,0=不显示",
 * "version":"版本号",
 * }
 ***/
@Setter
@Getter
@Slf4j
public class EvalKpi extends BaseLevelGroupIdConf implements EvalOkrItemInfo, IItemScoreRuleOwn {
    private String id;//
    @JSONField(serialize = false)
    private TenantId companyId;//公司id
    private String orgId;//部门id
    private String taskId;//考核任务id
    private String empId;//被考核人id
    private String taskUserId;//task user 表的id
    private String taskStatus;  //task_user.task_status  任务状态
    private String reviewer;//审核人信息
    private String kpiTypeId;//指标类id
    private String kpiTypeName;//指标类名称
    private BigDecimal kpiTypeWeight;//指标类权重
    private String kpiItemId;//指标项id
    private String kpiItemName;//指标项名称
    private BigDecimal itemTargetValue;//指标项目标值
    private BigDecimal itemFinishValue;//指标项完成值
    private String itemUnit;//指标项单位
    private BigDecimal itemWeight;//指标项权重
    private String resultInputType;//结果录入类型
    private String resultInputEmpId;//结果录入人id
    private String resultInputEmpName;//！！！日志中需要这个字段！！！
    private String backup;//备份，用于指标变更审核时的恢复操作
    private String examineOperType;//被考核人确认指标时操作类型：增删改
    private String itemRule;//考核规则
    private String scoringRule;//计分规则
    private String scorerType;//指标评分类型（exam:按自定评分流程/user:定向[员工/主管]）
    //private String scorerObjId;//指定评分人json串
    private List<StaffConfItem> scorerObjId;//指定评分人json串
    private String itemType;//指标项类型（量化/非量化）
    private String multipleReviewersType;//多人审核时，and会签，or或签
    private String kpiTypeClassify;//指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项
    private BigDecimal plusLimit;//加分上限
    private BigDecimal subtractLimit;//减分上限
    private BigDecimal maxExtraScore;//本类别最大加减分上限
    private Integer order;//排序，数字小的排前面
    private String itemFormula;//指标计算公式
    private BigDecimal itemAutoScore;//指标自动计算后的分数
    private String thresholdJson;//量化指标阈值设置json
    private String urgingFlag;//完成值录入催办标识
    private String itemActualFormula;//代入真实值后的计算公式
    private String autoScoreExFlag;//自动算分异常标识
    private String formulaCondition;//公式条件
    private String itemFieldJson;//指标关联的阈值字段
    private String isTypeLocked;//类别锁定类型
    private String isOkr;//是否OKR类别，true/false
    private Integer typeOrder;//类别排序
    private String okrRefFlag;//指标被OKR关联的标记
    private BigDecimal reserveOkrWeight;//预留OKR权重
    //计算使用
//    private List<PerfEvalRefPointDetail> pointsRule;
    private BigDecimal pointsNum;//指标积分
    private String itemScoreValue;//指标评分分值
    private String inputFormat;//录入格式
    private String workItemFinishValue;//工作事项完成情况说明
    private String itemPlanFlag;//是否同步了年度指标计划
    private String showTargetValue;//是否展示目标值
    private Integer mustResultInput;//指标完成值是否必需录入
    private Integer showFinishBar;//完成度进度条 默认开启 1=显示,0=不显示
    private Integer isNewEmp;
    private String managerLevel;
    private String itemFullScoreCfg;
    private int finalSubmitFinishValue;//最终提交完成值标识
    private String itemFinishValueText;
    private KpiItemLimitCnt itemLimitCnt;
    private int openOkrScore;//兼容字段，不推荐使用，以后依据类别为准
    private BigDecimal okrScore;//okr 原始分数
    private PlusSubInterval plusSubInterval;
    private IndLevelGroup indLevelGroup;         //生成人员考核时,从模板或者考核表复制来
    private String indLevel;         //指标结果等级
    @JsonAryColumn(StaffConfItem.class)
    private List<StaffConfItem> finishValueAudit;//完成值审核
    private String itemTargetValueText; //指标项文本类型目标值
    @JsonAryColumn(ObjItem.class)
    private List<ObjItem> inputRole;    //录入角色
    private Integer finishValueType;//指标完成值类型
    private String okrGoalId;            //okr经营计划指标关联的目标ID
    private String categoryId;      //指标所属分类
    private String categoryName;        //指标分类名称
    @JsonColumn
    private KpiOtherReqField otherReqField; //指标其它属性必填配置json串（如完成值必填、附件必填、备注必填）
    private Integer finishValueAuditStatus; // 完成值审核结果：0无需审核 （默认状态），1审核通过 ，2审核驳回
    private String finishValueAuditReason; // 完成值审核理由
    @JsonAryColumn(ExtDataItemFieldCorr.class)
    private List<ExtDataItemFieldCorr> itemFieldCorr; //指标字段关联外部数据字段
    private Integer finishValueSource = 1; // 1-手动录入，2-外部数据对接

    public EvalKpi() {
    }

    private List<KpiEmp> inputEmps; //用于考核规则保存至考核表用
    private List<String> itemTags;
    @JSONField(serialize = false)
    private EvalRefOkr refOkr;
    //    @JSONField(serialize = false)
    private List<EvalFormulaField> formulaFields;
    private EvalItemScoreRule itemScoreRule; //ItemEvaluate

   // private List<EvalScoreResult> waitScores;//待提交的评分
    private List<EvalScoreResult> waitScoresOld;//【 原来的】待提交的评分

    private List<EvalScorerNodeKpiItem> waitScores = new ArrayList<>();//【新的】该指标所有的评价人评分环节
   // private List<EmpEvalScorerNode> v3AlreadyScorerNodes;//已提交的评分环节评分指标
    private List<EvalScorerNodeKpiItem> v3AlreadyScorerNodes = new ArrayList<>();//已提交的评分环节评分指标
    private List<NodeOfItemScore> alreadyNodes = new ArrayList<>();
    private Boolean isSubmit;
    private String finishValueComment;
    private String files;
    /**
     * okr相关字段
     */
    private String targetId;
    private String targetName;
    private List<String> targetTags;
    private String okrTaskName;
    private String deptName;
    private String evaluateStartDate;
    private String actionId;
    private BigDecimal startValue;
    private BigDecimal endValue;
    private String actionName;
    private BigDecimal finishValue;
    private BigDecimal selfScore;
    private BigDecimal superiorScore;
    private BigDecimal score;

    private String actionValueSet; //kr
    private BigDecimal krSelfScore;
    private String krSelfContent;
    private BigDecimal krSuperiorScore;
    private String krSuperiorContent;
    private List<OkrActionUpdate> okrActionUpdates;        //okr更新记录对象集合

    private BigDecimal itemFinalScore;//指标最终得分
    private BigDecimal itemFinalOriginalScore;//指标最终原始得分
    private BigDecimal itemScore;//指标得分
    private BigDecimal itemOriginalScore;//指标最终原始得分
    private BigDecimal itemFinalSelfScore;//指标最终自评得分
    private BigDecimal itemFinalPeerScore;//指标最终同级互评得分
    private BigDecimal itemFinalSubScore;//指标最终下级互评得分
    private BigDecimal itemFinalSuperiorScore;//指标最终上级得分
    private BigDecimal itemFinalAppointScore;//指标最终指定评
    private BigDecimal itemFinalItemScore;//指标最终定向评评分
    private BigDecimal itemItemScore;//指标定向评评分 未乘以指标权重
    private BigDecimal itemSelfScore;//指标自评得分（不带指标权重）
    private BigDecimal itemPeerScore;//指标同级互评得分（不带指标权重）
    private BigDecimal itemSubScore;//指标下级互评得分（不带指标权重）
    private BigDecimal itemSuperiorScore;//指标上级得分（不带指标权重）
    private BigDecimal itemAppointScore;//指标指定评（不带指标权重）

    private BigDecimal progress;
    private String progressStatus;
    private BigDecimal weightValue;
    private Integer formulaType;//公式类型

    @JSONField(serialize = false)
    private List<EvalAudit> appointAudits = new ArrayList<>();
    private List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();      //指标自定义字段
    private List<ItemDynamicLog> inputChangeRecord;      //指标更新记录
    private String vetoFlag;            //否决标识(true为否决，false为不否决)
    private InputFinishValCache finishValCache;      //指标完成值暂存

    private ItemFinalNodeScoreV3 v3FinalWeightItemScore = new ItemFinalNodeScoreV3(); //指标各环节分计算

    public void initTaskUserId(String id) {
        this.taskUserId = id;
        if (CollUtil.isNotEmpty(formulaFields)) {
            for (EvalFormulaField formulaField : formulaFields) {
                formulaField.setTaskUserId(id);
            }
        }
        if (itemScoreRule != null) {
            itemScoreRule.setTaskUserId(id);
        }
    }
    public void accup(String companyId, String opEmpId) {
        this.companyId = new TenantId(companyId);
        this.updatedTime = new Date();
        this.updatedUser = opEmpId;
    }
    public String asKpiItemKey() {
        return String.format("%s&%s", kpiItemId, kpiTypeId);
    }
    //是否开启阈值
    @JSONField(serialize = false)
    public boolean isOpenItemField() {
        if (itemFieldJson == null || StrUtil.isBlank(itemFieldJson)) {
            return false;
        }
        return !JSONArray.parseArray(itemFieldJson).isEmpty();
    }

    public void factor(String typeName, BigDecimal typeWeight,
                       String classify, BigDecimal maxExtraScore,
                       String isTypeLocked, String isOkr, Integer typeOrder, String itemLimitCnt) {
        this.kpiTypeName = typeName;
        this.kpiTypeWeight = typeWeight;
        this.kpiTypeClassify = classify;
        this.maxExtraScore = maxExtraScore;
        this.isTypeLocked = isTypeLocked;
        this.isOkr = isOkr;
        this.typeOrder = typeOrder;
        if (StrUtil.isNotBlank(itemLimitCnt)) {
            this.itemLimitCnt = JSONUtil.toBean(itemLimitCnt, KpiItemLimitCnt.class);
        }
    }

    public void copyItem(String itemName, BigDecimal itemValue, String resultInputUserId) {
        this.id = UUID.randomUUID().toString();
        this.kpiItemName = itemName;
        this.itemTargetValue = itemValue;
        this.resultInputEmpId = resultInputUserId;
    }

    public void decomposeValue(BigDecimal value) {
        this.itemTargetValue = value;
        this.itemPlanFlag = "true";
    }

    public void baseId(TenantId companyId, String taskId, String empId, EmpId createdUser, String taskUserId) {
        this.companyId = companyId;
        this.taskId = taskId;
        this.empId = empId;
        this.createdUser = createdUser.getId();
        this.taskUserId = taskUserId;
    }

    //是否是自动计算评分
    @JSONField(serialize = false, deserialize = false)
    public boolean isAutoItem() {
        return ItemEvalType.isAutoType(getScorerType());
    }

    @JSONField(serialize = false, deserialize = false)
    public Boolean isTargetValueSettingOpen() {
        return Boolean.valueOf(showTargetValue);
    }

    public void accpItemRule(EvalItemScoreRule rule) {
        if (Objects.isNull(rule)) {
            return;
        }
        this.itemScoreRule = rule;
    }

    public void customItemScoreRule(EvalItemScoreRule customItemScoreRule) {
        if (customItemScoreRule == null) {
            return;
        }
        this.itemScoreRule = customItemScoreRule;
        this.itemScoreRule.setTaskId(taskId);
        this.itemScoreRule.setTaskUserId(taskUserId);
        this.itemScoreRule.setCreatedUser(createdUser);
        this.itemScoreRule.setCompanyId(companyId);
        this.itemScoreRule.setKpiItemId(kpiItemId);
        this.itemScoreRule.setKpiTypeId(kpiTypeId);
        //this.scorerType = ItemEvalType.EXAM;
    }


    public void setOrderIfNeed(int order) {
        if (this.order == null) {
            this.order = order;
        }
    }

    /**
     * 指标类别 加减分项
     */
    public static final String KPI_TYPE_CLASSIFY_PLUS_SUBTRACT = "plusSub";
    /**
     * 指标类别 加分项
     */
    public static final String KPI_TYPE_CLASSIFY_PLUS = "plus";
    /**
     * 指标类别 减分项
     */
    public static final String KPI_TYPE_CLASSIFY_SUBTRACT = "subtract";

    /**
     * 指标类别 一票否决
     */
    public static final String KPI_TYPE_CLASSIFY_ONE_VOTE_VETO = "oneVoteVeto";

    /**
     * 指标类别 自定义
     */
    public static final String KPI_TYPE_CLASSIFY_CUSTOM = "custom";

    @JSONField(serialize = false)
    public boolean isPlusSubType() {
        return KPI_TYPE_CLASSIFY_PLUS_SUBTRACT.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isPlusType() {
        return KPI_TYPE_CLASSIFY_PLUS.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isSubtractType() {
        return KPI_TYPE_CLASSIFY_SUBTRACT.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public boolean isOneVoteVetoType() {
        return KPI_TYPE_CLASSIFY_ONE_VOTE_VETO.equals(kpiTypeClassify);
    }

    //在设置绩效模板时，可选择完成值的录入类型：由被考核人录入 exam，指定员工录入 user，无需录入 no
    public boolean ifSelfInput() {
        return BusinessConstant.EXAM.equals(getResultInputType());
    }

    @JSONField(serialize = false)
    public boolean isSpecifyUser() {
        return BusinessConstant.APPROVER_TYPE_USER.equals(getResultInputType());
    }

    @JSONField(serialize = false)
    public boolean isSpecifyManager() {
        return BusinessConstant.APPROVER_TYPE_MANAGER.equals(getResultInputType());
    }

    @JSONField(serialize = false)
    public boolean isSpecifyRole() {
        return BusinessConstant.APPROVER_TYPE_ROLE.equals(getResultInputType());
    }

    //完成值录入人
    public String inputEmpName(KpiSyncReqConf kpiSyncReqConf) {
        boolean required = this.mustResultInput != null && this.mustResultInput == 1;
        if (StrUtil.isBlank(this.resultInputEmpName)) {
            this.resultInputEmpName = CollUtil.join(CollUtil.map(this.inputEmps, emp -> emp.getEmpName(), true), ",");
        }
        ResultInputEmp inputEmp = new ResultInputEmp(this.resultInputType, required, this.resultInputEmpId, this.resultInputEmpName, this.otherReqField);
        if (kpiSyncReqConf.isOpen()) {//如果是维度上统一配置，此处不校验必填
            return inputEmp.getLabelName();
        }
        return inputEmp.getFinishValueLabelName();//如果维度上设置指标单独配置，需校验指标必填选项
    }

    public Collection<String> inputEmp(String evalEmpId) {
        if (ifSelfInput()) {
            return Arrays.asList(evalEmpId);
        }
        if (StrUtil.isBlank(getResultInputEmpId())) {
            return Collections.emptyList();
        }
        return Arrays.asList(getResultInputEmpId().split(","));
    }

    public void copyFormulaFields(List<EvalFormulaField> formulas) {
        if (CollUtil.isEmpty(formulas)) {
            return;
        }
        for (EvalFormulaField formula : formulas) {
            formula.setTaskId(taskId);
            formula.setTaskUserId(taskUserId);
            formula.setCompanyId(this.companyId);
        }
        this.formulaFields = formulas;
        this.formulaCondition = JSONObject.toJSONString(formulas);
    }

    public void copyNewEmp(Integer isNewEmp) {
        this.isNewEmp = isNewEmp;
    }

    public boolean isNotInput() {
        if (isAutoItem() && finishValueIsNull()) {
            return true;
        }
        if (!Objects.equals(resultInputType, "no") && Objects.equals(mustResultInput, 1) && finishValueIsNull()) {
            return true;
        }
        return false;
    }

    public boolean isNotInput(String finishValue) {
        if (isAutoItem() && finishValueIsNull(finishValue)) {
            return true;
        }
        if (Objects.equals(mustResultInput, 1) && finishValueIsNull(finishValue)) {
            return true;
        }
        return false;
    }

    //除不需要录入的指标是否录入
    @JSONField(serialize = false)
    public boolean isNotInputVal() {
        if (!Objects.equals(resultInputType, "no") && finishValueIsNull()) {
            return true;
        }
        return false;
    }

    @JSONField(serialize = false)
    public boolean isMustNotInputVal() {
        if (isMustResultInput() && finishValueIsNull()) {
            return true;
        }
        return false;
    }

    //必填指标未提交
    @JSONField(serialize = false)
    public boolean isMustNotSubmitVal() {
        if (isMustResultInput() && !isFinalSubmit()) {
            return true;
        }
        return false;
    }

    //指标未提交
    @JSONField(serialize = false)
    public boolean isNotSubmitVal() {
        if (!Objects.equals(resultInputType, "no") && !isFinalSubmit()) {
            return true;
        }
        return false;
    }

    /**
     * 不需要录入
     */
    @JSONField(serialize = false)
    public boolean isNoInputVal() {
        return Objects.equals(resultInputType, "no");
    }

    public boolean finishValueIsNull() {
        return this.itemFinishValue == null && StrUtil.isEmpty(this.workItemFinishValue) && StrUtil.isEmpty(this.itemFinishValueText);
    }

    public boolean finishValueIsNull(String finishValue) {
        return finishValue == null;
    }

    public boolean isFinalSubmit() {
        return Objects.equals(finalSubmitFinishValue, 1);
    }

    public BigDecimal computeAuto(Boolean typeWeightOpen, BigDecimal fullScore, Boolean isFullScoreRange, Boolean openItemAutoScoreWeightAuth) {
        Map<String, Double> param = new HashMap<>();

        if (CollUtil.isNotEmpty(this.fieldValueList)) {
            for (ItemCustomFieldValue fieldValue : this.fieldValueList) {
                if (fieldValue.isThreshold()) {
                    if (StrUtil.isBlank(fieldValue.getFieldValue())) {
                        continue;
                    }
                    param.put(fieldValue.getFieldName(), Double.valueOf(fieldValue.getFieldValue()));
                }
            }
        }
        //完成值与目标值为系统默认，自动计算时，从指标中取值。（因为可以一直更新变更完成值。）只取表中阈值字段
        for (EvalFormulaField formulaField : formulaFields) {
            if (Objects.equals(formulaField.getFormulaFieldName(), "完成值")
                    || Objects.equals(formulaField.getFormulaFieldName(), "目标值")
                    || Objects.equals(formulaField.getFormulaFieldName(), "指标权重")) {
                continue;
            }
            if (Objects.isNull(param.get(formulaField.getFormulaFieldName()))) {
                param.put(formulaField.getFormulaFieldName(), formulaField.getFormulaFieldValue().doubleValue());
            }
        }
        log.info("formulaFields参数-{}", JSONObject.toJSONString(param));
        BigDecimal ONE_HUNDRED = new BigDecimal(100);
        //完成值没有存到公式字段表，因为它在不停的变化
        BigDecimal finishValue = getItemFinishValue() == null ? BigDecimal.ZERO : getItemFinishValue();
        param.put("完成值", finishValue.doubleValue());
        BigDecimal targetValue = getItemTargetValue() == null ? BigDecimal.ZERO : getItemTargetValue();
        param.put("目标值", targetValue.doubleValue());
        BigDecimal itemWeight = getItemWeight() == null ? BigDecimal.ZERO : getItemWeight();
        param.put("指标权重", itemWeight.divide(ONE_HUNDRED).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP).doubleValue());
        //String replacedFormula = StrUtil.replace(itemFormula, "'", "");
        Object stepRs = new JSElComputer(itemFormula).eval(param);
        log.info("1 指标={}, 公式={}，计算后的结果={},参数={}", kpiItemId, itemFormula, stepRs, JSONObject.toJSONString(param));
        //taskBiz.3824
        this.itemAutoScore = new BigDecimal(stepRs.toString());
        BigDecimal itemScore = new BigDecimal(stepRs.toString());
        boolean openFullAuth = openItemAutoScoreWeightAuth || isFullScoreRange;
        if ("true".equals(this.itemFullScoreCfg)) {
            //如果没有配置系统默认满分值，默认为100分
            if (fullScore == null) {
                fullScore = new BigDecimal(100);
            }
            if (this.itemAutoScore.compareTo(BigDecimal.ZERO) == -1) {
                this.itemAutoScore = BigDecimal.ZERO;
            }
            //如果开启了指标自动计算加和或者0-100满分权重-//指标开启满分限制，如果大于fullScore权重上限，则取满分上限*指标权重分
            if (Objects.nonNull(this.getItemWeight()) && !openFullAuth) {
                fullScore = fullScore.multiply(this.getItemWeight()).divide(ONE_HUNDRED).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
                log.info("fullScore = 指标权重={}，fullScore得分={}", this.getItemWeight(), fullScore);
            }
            if (this.itemAutoScore.compareTo(fullScore) == 1) {
                this.itemAutoScore = fullScore;
            }
        }

        log.info("乘以指标权重之前 isFullScoreRange：{}，openItemAutoScoreWeightAuth={}，得分={}", isFullScoreRange, openItemAutoScoreWeightAuth, itemAutoScore);
        if (Objects.nonNull(this.getItemWeight()) && openFullAuth) {
            this.itemAutoScore = itemAutoScore.multiply(this.getItemWeight()).divide(ONE_HUNDRED).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
            log.info("乘以指标权重={}，得分={}", this.getItemWeight(), itemAutoScore);
        }

        if (typeWeightOpen && Objects.nonNull(this.getKpiTypeWeight())) {
            this.itemAutoScore = itemAutoScore.multiply(this.getKpiTypeWeight()).divide(ONE_HUNDRED).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
            log.info("乘以指标类权重={}，得分={}", this.getKpiTypeWeight(), itemAutoScore);
        }
        log.info("最终计算指标得分={}", itemAutoScore);


        this.itemScore =  itemScore;
        this.itemOriginalScore =  this.itemScore;
        this.itemFinalScore =  this.itemAutoScore;
        this.itemFinalOriginalScore =  this.itemAutoScore;
        log.info("自动计算分，未乘以（指标和维度）权重分={}", itemScore);
        return this.itemAutoScore;
    }

    //指标中的评分人
    @JSONField(serialize = false)
    public List<EmpStaff> getScorers(Map<String, List<RoleRefEmp>> roleRefEmps, Map<String, List<LevelManager>> levelManagers) {
        List<EmpStaff> staffs = new ArrayList<>();
        if (scorerObjId == null) {
            return staffs;
        }
        // 解析指标中的人 [{"obj_type":"role","objItems":[]},{"obj_type":"user","objItems":[{"objId":"1098002","objName":"陈列"},{"objId":"1093007","objName":"张旺"}]}]
        log.info("定向评分人josn字段：{}", JSONArray.toJSONString(this.scorerObjId));
        log.info("定向评分人roleRefEmps字段：{}", roleRefEmps == null ? null : JSONArray.toJSONString(roleRefEmps));
        for (StaffConfItem staffConf : scorerObjId) {
            if (staffConf == null || staffConf.getObjItems() == null) {
                continue;
            }
            List<EmpStaff> scorers = staffConf.getScorers(roleRefEmps, levelManagers);
            staffs.addAll(scorers);
        }
        return staffs;
    }

    //是否定向评分流程
    @JSONField(serialize = false)
    public boolean isDirectional() {
        return ItemEvalType.isItemScore(scorerType);
    }

    @JSONField(serialize = false)
    public boolean isFinishValueAudit() {
        if (CollUtil.isEmpty(this.finishValueAudit)) {
            return false;
        }
        List<StaffConfItem> confItemList = this.finishValueAudit.stream().filter(audit -> "taskEmp".equals(audit.getObjType()) || (!"taskEmp".equals(audit.getObjType()) && !audit.getObjItems().isEmpty())).collect(Collectors.toList());
        return confItemList.size() > 0;
    }

    @JSONField(serialize = false)
    public boolean isNormal() {
        if (isDirectional()) {
            return false;
        }
        if (isAutoItem()) {
            return false;
        }
        return true;
    }

    @JSONField(serialize = false)
    public List<EvalScoreResult> getWaitSubmittedScores(String scorerType) {
        if (this.waitScoresOld != null && !this.waitScoresOld.isEmpty()) {
            return new ListWrap<>(waitScoresOld).groupBy(EvalScoreResult::getScorerType).groupGet(scorerType);
        }
        return new ArrayList<>();
    }


    public String asMapKey() {
        return String.format("%s&%s", kpiItemId, kpiTypeId);
    }

    //百分比小数形式指标权重
    public BigDecimal percentItemWeight(boolean itemWeightOpened) {
        //指标权重设置0时默认按0计算
        if (BigDecimal.ZERO.compareTo(itemWeight) == 0) {
            return BigDecimal.ZERO;
        }
        if (!itemWeightOpened || itemWeight == null) {
            return BigDecimal.ONE;
        }
        if (isPlusType() || isSubtractType()) {
            return BigDecimal.ONE;
        }
        return pecWeight();
    }

    public BigDecimal pecWeight(boolean submitWithWeight) {
        if (submitWithWeight) {
            return BigDecimal.ONE;
        }
        return itemWeight.divide(new BigDecimal(100));
    }

    public BigDecimal pecWeight() {
        return itemWeight.divide(new BigDecimal(100));
    }

    //百分比小数形式分类权重
    public BigDecimal percentTypeWeight(Boolean typeWeightOpen) {
        if (!typeWeightOpen) {
            return BigDecimal.ONE;
        }
        if (kpiTypeWeight == null) {
            return BigDecimal.ONE;
        }
        return kpiTypeWeight.divide(new BigDecimal(100));
    }

    // 360及简易分发定非定向评任务项
    public void dispatchSelfRaterItem(String evalEmpId, List<EvalScoreResult> raterItems) {
        if (isDirectional()) {
            return;
        }
        SubScoreNodeEnum node = SubScoreNodeEnum.SELF_SCORE;

        //已经存在了
        List<EvalScoreResult> nodeScores = this.getWaitSubmittedScores(SubScoreNodeEnum.SELF_SCORE.getScene());
        if (CollUtil.isNotEmpty(nodeScores)) {
            for (EvalScoreResult nodeScore : nodeScores) {
                nodeScore.doDispatched();
                raterItems.addAll(nodeScores);
            }
            return;
        }

        EvalScoreResult item = createRaterItem(node, evalEmpId, Pecent.ONE_HUNDRED);
        raterItems.add(item);
    }

    //指标中的评分人
    public List<Rater> directScorers() {
        List<Rater> staffs = new ArrayList<>();
        if (scorerObjId == null) {
            return staffs;
        }
        // 解析指标中的人 [{"obj_type":"role","objItems":[]},{"obj_type":"user","objItems":[{"objId":"1098002","objName":"陈列"},{"objId":"1093007","objName":"张旺"}]}]
        log.info("定向评分人josn字段：{}", JSONArray.toJSONString(this.scorerObjId));
        for (StaffConfItem staffConf : scorerObjId) {
            for (ObjItem item : staffConf.getObjItems()) {
                Rater staff = new Rater(item.getObjId(), item.getObjName(), Pecent.ONE_HUNDRED);
                staffs.add(staff);
            }
        }
        return staffs;
    }

    public List<EvalScoreResult> dispatch(SubScoreNodeEnum node, int order, boolean isOpenAvgWeightCompute) {
        List<EvalScoreResult> raterResults = new ArrayList<>();
        //自动计算指标||无需评分不分发
        if (isAutoItem()) {
            return raterResults;
        }
        //使用okr分数的指标无需派发
        if (openOkrScore()) {
            return raterResults;
        }
        //定向的处理指标
        if (node == SubScoreNodeEnum.ITEM_SCORE) {
            return this.dispatchDirectIf();
        }
        if (isDirectional()) {
            return raterResults;
        }
        //其它类型指标
        if (itemScoreRule == null) {
            return raterResults;
        }
        INodeConf iNodeConf = itemScoreRule.getNodeConf(node, order, empId);
        if (iNodeConf == null) {//有些标多级,有的一级. 有些指标是无f规则
            return raterResults;
        }
        List<EvalScoreResult> rrs = dispacthNodeConf(iNodeConf, isOpenAvgWeightCompute);
        return rrs;
    }

    @Nullable
    private List<EvalScoreResult> dispacthNodeConf(INodeConf iNodeConf, boolean isOpenAvgWeightCompute) {
        List<EvalScoreResult> raterResults = new ArrayList<>();
        if (isDispatched(iNodeConf.node(), iNodeConf.order())) {//已完成评分,跳过环节
            //如果不是全部评分完成,则继续往下
            List<EvalScoreResult> hasWaitDispatchRs = hasWaitDispatchRs(iNodeConf);
            if (!hasWaitDispatchRs.isEmpty()) {
                return hasWaitDispatchRs;
            }
            return raterResults;
        }
        //重置后再次执行进入,使用已存在的scoreResult,解决已转交的scorer.
        List<EvalScoreResult> hasWaitDispatchRs = hasWaitDispatchRs(iNodeConf);
        if (!hasWaitDispatchRs.isEmpty()) {
            return hasWaitDispatchRs;
        }
        if (iNodeConf.openedRaters().isEmpty()) {
            return raterResults;
        }
        // 开启了平均权重 或者不在不是同级|下级互评场景
        if (isOpenAvgWeightCompute || !SubScoreNodeEnum.mutualScoreScene().contains(iNodeConf.node().getScene())) {
            iNodeConf.avgWeight();
        }

        for (Rater scorer : iNodeConf.openedRaters()) {
            if (null == scorer.getWeight()) {
                scorer.setWeight(new BigDecimal("100.00"));
            }
            if (!isOpenAvgWeightCompute && SubScoreNodeEnum.mutualScoreScene().contains(iNodeConf.node().getScene())) {
                scorer.setWeight(new BigDecimal("100.00"));//开启平均分，且是互评场景，评分人权重置为100%
            }
            EvalScoreResult result = createResult(iNodeConf.node(), iNodeConf.order(), iNodeConf.multiType(), scorer);
            raterResults.add(result);
        }
        return raterResults;
    }

    //重置后再次执行进入,使用已存在的scoreResult,解决已转交的scorer.
    public List<EvalScoreResult> hasWaitDispatchRs(INodeConf iNodeConf) {
        List<EvalScoreResult> rs = new ArrayList<>();
        if (waitScoresOld == null || waitScoresOld.isEmpty()) {
            return Collections.emptyList();
        }
        List<EvalScoreResult> nodeScores = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(iNodeConf.node().getScene())
                .stream().filter(sResult -> sResult.isWaitDispatch()).collect(Collectors.toList());
        for (EvalScoreResult nodeScore : nodeScores) {
            //指定评分重置result的状态全部重置回去，不去分order
            if (nodeScore.isAppointNode() && "wait".equals(nodeScore.getAuditStatus())) {
                nodeScore.doDispatched();
                rs.add(nodeScore);
            }
            //已分发再分发一次
            if (iNodeConf.order() == nodeScore.getApprovalOrder()) {
                nodeScore.doDispatched();
                rs.add(nodeScore);
            }
        }
        return rs;
    }

    public List<EvalScoreResult> dispatchDirectIf() {
        List<EvalScoreResult> raterResults = new ArrayList<>();
        if (!isDirectional()) {
            return raterResults;
        }
        StdNode itemNode = new StdNode(SubScoreNodeEnum.ITEM_SCORE, BaseAuditNode.MULTI_OR, directScorers());
        List<EvalScoreResult> alreadys = hasWaitDispatchRs(itemNode);
        if (!alreadys.isEmpty()) {
            return alreadys;
        }
        for (Rater scorer : directScorers()) {
            EvalScoreResult result = createResult(SubScoreNodeEnum.ITEM_SCORE, 1, BaseAuditNode.MULTI_OR, scorer);
            raterResults.add(result);
        }
        return raterResults;
    }

    @NotNull
    private EvalScoreResult createResult(SubScoreNodeEnum node, int order, String multiOrAndMode, Rater scorer) {
        EvalScoreResult result = new EvalScoreResult(companyId, taskId, orgId, empId, createdUser);
        order = scorer.getApprovalOrder() != null ? scorer.getApprovalOrder() : order;
        result.appendAuditor(node.getScene(), scorer.getEmpId(), multiOrAndMode, order, null, null);
        result.appendItemId(kpiItemId, kpiTypeId);
        result.setScoreWeight(scorer.getWeight());
        result.setTaskUserId(taskUserId);
        return result;
    }


    // 360及简易分发定定向评任务项
    public void dispatchDirectRaterItem(List<EvalScoreResult> raterItems, Map<String, List<RoleRefEmp>> roleRefEmps, Map<String, List<LevelManager>> levelManagers) {
        if (!isDirectional()) {
            return;
        }
        SubScoreNodeEnum node = SubScoreNodeEnum.ITEM_SCORE;

        List<EvalScoreResult> waitSubmittedScores = this.getWaitSubmittedScores(node.getScene());//新版重置之后修改原来的 score result
        if (CollUtil.isNotEmpty(waitSubmittedScores)) {
            raterItems.addAll(waitSubmittedScores);
            return;
        }

        for (EmpStaff scorer : getScorers(roleRefEmps, levelManagers)) {
            EvalScoreResult item = createRaterItem(node, scorer.getEmpId(), Pecent.ONE_HUNDRED);
            raterItems.add(item);
        }
    }

    public EvalScoreResult createRaterItem(SubScoreNodeEnum scene, String scorerId, BigDecimal weight) {
        EvalScoreResult result =
                new EvalScoreResult(companyId, taskId, orgId, empId, createdUser);
        result.appendAuditor(scene.getScene(), scorerId, multipleReviewersType, 1, id, null);
        result.appendItemId(kpiItemId, kpiTypeId);
        result.setScoreWeight(weight == null ? new BigDecimal(100) : weight);
        return result;
    }

    public void addSelfScoreIfOpt(List<EvalScoreResult> rs, String evalEmpId) {
        if (!itemScoreRule.selfNodeIsOpened()) {
            return;
        }
        EvalScoreResult result = createRaterItem(SubScoreNodeEnum.SELF_SCORE, evalEmpId, Pecent.ONE_HUNDRED);
        rs.add(result);
    }

    public void formulaFields(List<EvalFormulaField> fields) {
        this.formulaFields = fields;
    }

    public boolean hasItemScoreRule() {
        return itemScoreRule != null;
    }

    public boolean isOkrType() {
        return StringUtils.isNotBlank(isOkr) && Boolean.TRUE.toString().equals(isOkr);
    }

    public boolean isBusinessPlanItem() {
        return Objects.equals(this.itemType, KpiItemTypeEnum.BUSINESS_ITEM.getType());
    }

    public boolean hasInputNum() {
        return Objects.equals(this.itemType, KpiItemTypeEnum.MEASURABLE.getType()) || Objects.equals(this.inputFormat, "num");
    }

    public boolean hasInputText() {
        return StrUtil.isBlank(this.inputFormat) ? Objects.equals(this.itemType, KpiItemTypeEnum.NON_MEASURABLE.getType())
                : (Objects.equals(this.itemType, KpiItemTypeEnum.NON_MEASURABLE.getType()) && !hasInputNum());
    }

    // 解析指标中的人 [{"obj_type":"role","objItems":[]},{"obj_type":"user","objItems":[{"objId":"1098002","objName":"陈列"},{"objId":"1093007","objName":"张旺"}]}]
    public List<String> appointRoleIds() {
        //List<KpiItemRater> kpiItemRaters = JSONObject.parseArray(this.scorerObjId, KpiItemRater.class);
        if (scorerObjId == null) {
            return new ArrayList<>();
        }
        //scorerObjId.stream().filter(staffConfItem -> staffConfItem.isRoleType())
        List<StaffConfItem> roles = scorerObjId.stream().filter(staffConfItem -> staffConfItem.isRoleType()).collect(Collectors.toList());
        if (CollUtil.isEmpty(roles)) {
            return new ArrayList<>();
        }
        List<ObjItem> objItems = roles.get(0).getObjItems();
        if (CollUtil.isEmpty(objItems)) {
            return new ArrayList<>();
        }
        return objItems.stream().map(o -> o.getObjId()).collect(Collectors.toList());
    }

    public Boolean isWorkItem() {
        return BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(kpiTypeClassify);
    }

    @JSONField(serialize = false)
    public List<String> getInputRaterIds() {
        if (isNoInputVal()) {
            return Collections.emptyList();//如果是无需录入则直接返回空
        }
        if ("exam".equals(resultInputType)) {
            return Arrays.asList(empId);
        }
        if (StrUtil.isBlank(resultInputEmpId)) {
            return Collections.emptyList();
        }
        return Arrays.asList(resultInputEmpId.split(","));
    }

    public List<EvalRuleOpLogMeta> newLog() {
        List<EvalRuleOpLogMeta> rs = new ArrayList<>();
        EvalRuleOpLogMeta mdType = new EvalRuleOpLogMeta("新建了考核指标", kpiItemName);
        rs.add(mdType);
        return rs;
    }


    public List<EvalRuleOpLogMeta> compare(EvalKpi after, KpiSyncReqConf kpiSyncReqConf) {
        List<EvalRuleOpLogMeta> rs = new ArrayList<>();

        EvalRuleOpLogMeta mdType = new EvalRuleOpLogMeta("修改了考核指标", kpiItemName);
        mdType.addField(EvalRuleLogField.createIf("指标名称", kpiItemName, after.kpiItemName));
        mdType.addField(EvalRuleLogField.createIf("指标权重", itemWeightName(), after.itemWeightName()));
        mdType.addField(EvalRuleLogField.createIf("指标类型", itemTypeName(), after.itemTypeName()));
        mdType.addField(EvalRuleLogField.createIf("考核标准", itemRule, after.itemRule));
        mdType.addField(EvalRuleLogField.createIf("计分规则", scoringRule, after.scoringRule));
        mdType.addField(EvalRuleLogField.createIf("目标值设置", showTargetValueName(), after.showTargetValueName()));
//        if (after.isTargetValueSettingOpen()) {
        //目标值设置修改为开启之后才有目标值的变化
        mdType.addField(EvalRuleLogField.createIf("目标值", itemTargetValueName(), after.itemTargetValueName()));
//        }
        mdType.addField(EvalRuleLogField.createIf("完成值录入人", inputEmpName(kpiSyncReqConf), after.inputEmpName(kpiSyncReqConf)));//之前注释掉了
        mdType.addField(EvalRuleLogField.createIf("录入方式", inputFormatName(), after.inputFormatName()));
        mdType.addField(EvalRuleLogField.createIf("完成度展示", showFinishBarName(), after.showFinishBarName()));
        mdType.addField(EvalRuleLogField.createIf("指标评分方式", scorerTypeName(), after.scorerTypeName()));
        mdType.addField(EvalRuleLogField.createIf("定向评分人", scorerIdName(), after.scorerIdName()));
        mdType.addField(EvalRuleLogField.createIf("完成值审核", finisheValueAuditName(), after.finisheValueAuditName()));
        mdType.addField(EvalRuleLogField.createIf("自动计算公式", itemFormula, after.itemFormula));
        if (!after.isAutoItem()) {
            //不是自动自动计算时要比较评分范围变化，自动计算的时候不用考虑指标评分范围
            mdType.addField(EvalRuleLogField.createIf("指标评分范围", itemScoreValueName(), after.itemScoreValueName()));
        }
        if (itemScoreRule != null) {
            List<EvalRuleLogField> itemRuleDiff = itemScoreRule.compare(after.itemScoreRule);
            mdType.addAllField(itemRuleDiff);
        }

        if (mdType.isNotEmpty()) {
            rs.add(mdType);
        }

        return rs;
    }

    private String itemScoreValueName() {
        if (itemScoreValue == null) {
            return "";
        }
        return JSONUtil.toBean(itemScoreValue, ItemScoreValue.class).itemScoreName();
    }


    private String scorerTypeName() {
        if (isDirectional()) {
            return "定向评分";
        }
        if (isAutoItem()) {
            return "自动计算";
        }
        return "考核任务设置的评价人进行评价";
    }

    private String scorerIdName() {
        if (scorerObjId == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        scorerObjId.forEach(item -> {
            for (ObjItem objItem : item.getObjItems()) {
                sb.append(item.name()).append(objItem.getObjName());
            }
        });
        return sb.toString();
    }

    private String finisheValueAuditName() {
        if (CollUtil.isEmpty(finishValueAudit)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        finishValueAudit.forEach(item -> {
            for (ObjItem objItem : item.getObjItems()) {
                sb.append(item.name()).append(objItem.getObjName());
            }
        });
        return sb.toString();
    }


    private String showTargetValueName() {
        return Boolean.valueOf(showTargetValue) ? "开启" : "关闭";
    }

    public String showFinishBarName() {
        if (Objects.isNull(showFinishBar)) {
            showFinishBar = 0;
        }
        return showFinishBar == 1 ? "开启" : "关闭";
    }

    private String itemWeightName() {
        if (this.itemWeight == null) {
            return null;
        }
        return this.itemWeight.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "%";
    }

    private String itemTargetValueName() {
        if (this.itemTargetValue == null) {
            return null;
        }
        return this.itemTargetValue.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() + "";
    }

    private String itemTypeName() {
        if (StrUtil.isEmpty(this.itemType)) {
            return this.itemType;
        }
        return StrUtil.equals("measurable", this.itemType) ? "量化指标" : "非量化指标";
    }

    private String inputFormatName() {
        if (StrUtil.isEmpty(this.inputFormat)) {
            return this.inputFormat;
        }
        return StrUtil.equals("num", this.inputFormat) ? "数值录入" : "文本录入";
    }

    public void belongIf(EvalItemScoreRule itRule) {
        if (itRule.getKpiItemId().equals(this.kpiItemId)) {
            this.itemScoreRule = itRule;
        }
    }

    public boolean isManagerInput() {
        return "manager".equals(this.resultInputType);
    }

    public void submitFinish(FinishValue finishValue) {
        this.setFinalSubmitFinishValue(finishValue.getFinalSubmitFinishValue());
        this.setItemFinishValue(finishValue.getItemFinishValue());
        this.setItemFinishValueText(finishValue.getItemFinishValueText());
        this.setWorkItemFinishValue(finishValue.getWorkItemFinishValue());
    }

    public String beforeItemFinishValue(String workItemFinishValue) {
        if (StrUtil.isNotEmpty(workItemFinishValue)) {
            return this.workItemFinishValue;
        }
        if ("non-measurable".equals(this.itemType) && "text".equals(this.inputFormat)) {
            return this.itemFinishValueText;
        }
        return itemFinishValue == null ? null : String.valueOf(this.itemFinishValue);
    }

    public String afterItemFinishValue(BigDecimal itemFinishValue, String itemFinishValueText, String workItemFinishValue) {
        if (StrUtil.isNotEmpty(workItemFinishValue)) {
            return workItemFinishValue;
        }
        if ("non-measurable".equals(this.itemType) && "text".equals(this.inputFormat)) {
            return itemFinishValueText;
        }
        return itemFinishValue == null ? null : String.valueOf(itemFinishValue);
    }

    public boolean isWorkItemFinished() {
        if (!isWorkItem()) {
            return false;
        }
        if (StrUtil.isEmpty(this.workItemFinishValue)) {
            return false;
        }
        return true;

    }

    public String isSubmitFinishValueScene() {
        if (finalSubmitFinishValue == 1) {
            return OperationLogSceneEnum.SUBMIT_FINISH_VALUE.getScene();
        }
        return OperationLogSceneEnum.INPUT_FINISH_VALUE.getScene();
    }

    public Boolean openOkrScore() {
        return Boolean.TRUE.toString().equals(isOkr) && 1 == openOkrScore;
    }

    public Boolean hasActionId() {
        return Boolean.TRUE.toString().equals(isOkr) && StrUtil.isNotBlank(actionId);
    }

    public void initBaseInfo(TenantId tenantId, EmpId opEmpId, String evalUserId, String kpiTypeId) {
        this.companyId = tenantId;
        this.updatedUser = opEmpId.getId();
        this.createdUser = opEmpId.getId();
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.taskUserId = evalUserId;
        this.kpiTypeId = kpiTypeId;
        if (Objects.nonNull(itemScoreRule)) {
            itemScoreRule.initBaseInfo(tenantId, opEmpId, evalUserId, kpiTypeId, kpiItemId, taskId);
        }
        if (CollUtil.isEmpty(formulaFields)) {
            return;
        }
        formulaFields.forEach(f -> {
            f.initBaseInfo(tenantId, opEmpId, evalUserId, kpiItemId, taskId);
        });
    }

    public boolean belongItem(SubScoreNodeEnum node, String belongEmpId) {
        //所有指标相同的评分人及环节
        if (itemScoreRule == null) {
            return true;
        }
        return itemScoreRule.belongItem(node, belongEmpId);
    }

    private List<EvalScoreResult> dis2SameScoreRs(Collection<? extends Rater> raters, SubScoreNodeEnum nodeEnum, Integer subOrder) {
        List<EvalScoreResult> rs = new ArrayList<>();
        for (Rater rater : raters) {
            EvalScoreResult subRs = createRaterItem(nodeEnum, rater.getEmpId(), rater.getWeight());
            subRs.setApprovalOrder(subOrder);
            rs.add(subRs);
        }
        return rs;
    }

    public List<NodeWeight> nodeWeight() {
        NodeWeight sup = new NodeWeight(AuditEnum.SUPERIOR_SCORE.getScene(), itemScoreRule.getSuperiorScoreWeight());
        NodeWeight self = new NodeWeight(AuditEnum.SELF_SCORE.getScene(), itemScoreRule.getSelfScoreWeight());
        NodeWeight peer = new NodeWeight(AuditEnum.PEER_SCORE.getScene(), itemScoreRule.getPeerScoreWeight());
        NodeWeight subPeer = new NodeWeight(AuditEnum.SUB_SCORE.getScene(), itemScoreRule.getSubScoreWeight());
        NodeWeight appoint = new NodeWeight(AuditEnum.APPOINT_SCORE.getScene(), itemScoreRule.getAppointScoreWeight());
        return Arrays.asList(sup, self, peer, subPeer, appoint);
    }

    public boolean hasDirectionalRater() {
        if (!isDirectional()) {
            return false;
        }
        for (StaffConfItem staffConfItem : scorerObjId) {
            if (CollUtil.isNotEmpty(staffConfItem.getObjItems())) {
                return true;
            }
        }
        return false;
    }

    //标准化权重
    public void initItemWeight(boolean submitWithWgt) {
        ////开启了0到满分分值乘以权重并且权重不为0的时候才初始化权重
        //if (submitWithWgt && !itemWeightIsZeroNotCompute()) {
        //    itemWeight = Pecent.ONE_HUNDRED;
        //    return;
        //}
        if (itemWeight == null) {
            itemWeight = BigDecimal.ZERO;
        }
    }

    public void checkReSubmited(EvalScoreResult submited, BigDecimal pecWeight) {
        //SubScoreNodeEnum nodeEnum = SubScoreNodeEnum.fromStr(submited.getScorerType()); scoreKpiItems
        ListWrap<EvalScoreResult> wrap = new ListWrap<>(waitScoresOld);
        EvalScoreResult wait = wrap.asMap(EvalScoreResult::getId).mapGet(submited.getId());
        if (wait == null) {
            throw new KpiI18NException("msg.task.empeval.submitItemScoreFlowChange", "该被考核的评价流程已发生变化，暂无法提交");//评分流程发生变化
        }
        if (wait.isPassed()) {
            throw new KpiI18NException("msg.task.empeval.reSubmitScore", "该被考核的评价已被他人完成了");//已或签,或者提交过
        }
    }

    public List<EvalScoreResult> fixItemScore(BigDecimal pecTypeWgt, boolean submitWithWeight) {
        for (EvalScoreResult wait : waitScoresOld) {
            SubScoreNodeEnum nodeEnum = SubScoreNodeEnum.fromStr(wait.getScorerType());
            BigDecimal nodeWeight = isDirectional() ? Pecent.ONE : itemScoreRule.nodePecWeight(nodeEnum);
            if (isPlusType()) {
                wait.computePlusScore(nodeWeight);
            } else if (isSubtractType()) {
                wait.computeSubtractScore(nodeWeight);
            } else if (isPlusSubType()) {
                wait.computePlusSubScore(nodeWeight);
            } else {//按常规指标,事项计算
                wait.computeFinalScore(pecTypeWgt, pecWeight(submitWithWeight), nodeWeight);
            }
        }
        return waitScoresOld;
    }

    public EvalScoreResult computeItemScore(EvalScoreResult submited, BigDecimal pecTypeWgt, boolean submitWithWeight) {
        SubScoreNodeEnum nodeEnum = SubScoreNodeEnum.fromStr(submited.getScorerType());
        ListWrap<EvalScoreResult> wrap = new ListWrap<>(waitScoresOld);
        EvalScoreResult wait = wrap.asMap(EvalScoreResult::getId).mapGet(submited.getId());
        wait.submitSrcScore(submited, submited.getScorerId());
        wait.setScoreAttUrl(submited.getScoreAttUrl());
        wait.setScoreOption(submited.getScoreOption());
        BigDecimal nodeWeight = isDirectional() ? Pecent.ONE : Objects.isNull(nodeEnum) ? Pecent.ONE : itemScoreRule.nodePecWeight(nodeEnum);
        if (isPlusType()) {
            wait.computePlusScore(nodeWeight);
        } else if (isSubtractType()) {
            wait.computeSubtractScore(nodeWeight);
        } else if (isPlusSubType()) {
            wait.computePlusSubScore(nodeWeight);
        } else if (isOneVoteVetoType()) {
            wait.setVetoFlag(submited.getVetoFlag());
        } else {//按常规指标,事项计算
            wait.computeFinalScore(pecTypeWgt, pecWeight(submitWithWeight), nodeWeight);
        }
        return wait;
        //wait.upScore(submited);
    }

    public boolean nodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        if (isEvalClosed()) {
            return true;
        }
        List<EvalScoreResult> scoreRsGroup = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(node.getScene());
        if (CollUtil.isEmpty(scoreRsGroup)) {//当前指标下)已无需要提交的node环节(不包含未分发的
            return true;
        }
        List<EvalScoreResult> matchOrders = scoreRsGroup.stream().filter(rs -> rs.getApprovalOrder() == nodeOrder).collect(Collectors.toList());
        if (matchOrders.isEmpty()) {//当前指标无此层级
            return true;
        }
        EvalScoreResult lastRs = null;
        for (EvalScoreResult result : scoreRsGroup) {
            if (result.getApprovalOrder() != nodeOrder) {
                continue;
            }
            lastRs = result;
            if (result.isAndMode() && !result.isPassed()) {
                return false;
            }
            if (result.isOrMode() && result.isPassed()) {
                return true;
            }
            //if (result.isItemNode()) {
            //    return true;
            //}
        }
        if (lastRs == null) {
            return false;
        }
        return lastRs.isAndMode() ? true : false;
    }

    public boolean isDispatched(SubScoreNodeEnum node, int nodeOrder) {
        if (isEvalClosed()) {
            return true;
        }
        List<EvalScoreResult> scoreRsGroup = new ListWrap<>(waitScoresOld).groupBy(r -> r.getScorerType()).groupGet(node.getScene());
        if (CollUtil.isEmpty(scoreRsGroup)) {//当前指标下)已无需要提交的node环节(不包含未分发的
            return false;
        }
        List<EvalScoreResult> matchOrders = scoreRsGroup.stream().filter(rs -> rs.getApprovalOrder() == nodeOrder && !rs.isWaitDispatch()).collect(Collectors.toList());
        return CollUtil.isNotEmpty(matchOrders);
    }


    public boolean allScoreRsPassed() {
        if (waitScoresOld.isEmpty()) {//无需评价的指标不分发.
            return true;
        }
        //所有或的在完成时都标记为pass了,此处不再识别or, and
        for (EvalScoreResult result : waitScoresOld) {
            if (!result.isPassed()) {
                return false;
            }
        }
        return true;
    }

    public List<EvalScoreResult> markOrModeScoreRs(SubScoreNodeEnum node, int nodeOrder, String submitId) {
        List<EvalScoreResult> orScoreRs = new ArrayList<>();
        List<EvalScoreResult> scoreRsGroup = new ListWrap<>(waitScoresOld)
                .groupBy(r -> r.getScorerType())
                .groupGet(node.getScene());
        for (EvalScoreResult result : scoreRsGroup) {
            if (result.getApprovalOrder() != nodeOrder || StrUtil.equals(result.getId(), submitId)) {
                continue;
            }
            if (result.isOrMode()) {
                result.passed();
                result.setIsDeleted(Boolean.TRUE.toString());
                orScoreRs.add(result);
            }
        }
        return orScoreRs;
    }

    public Collection<String> allScoreType() {
        if (itemScoreRule == null) {
            return Collections.emptyList();
        }
        return itemScoreRule.allScoreType();
    }


    public void reSetAttrInfo(String taskUserId, String taskId) {
        if (itemScoreRule != null) {
            itemScoreRule.setId(null);
            itemScoreRule.setTaskUserId(taskUserId);
            itemScoreRule.setTaskId(taskId);
        }
        if (CollUtil.isEmpty(formulaFields)) {
            return;
        }
        for (EvalFormulaField formulaField : formulaFields) {
            formulaField.setId(null);
            formulaField.setTaskUserId(taskUserId);
            formulaField.setTaskId(taskId);
        }
    }

    public void rmItemRule() {
        this.itemScoreRule = null;
    }

    public void builder(EvalRefOkr refOkr, EmpEvalKpiType type, String updateEmp) {
        //id = refOkr.getTaskKpiId();
        itemWeight = refOkr.getItemWeight();
        kpiItemName = refOkr.getActionName();
        kpiTypeWeight = type.getKpiTypeWeight();
        itemTargetValue = BigDecimal.ZERO;
        itemUnit = refOkr.getUnit();
        resultInputType = BusinessConstant.NO;
        scorerType = BusinessConstant.EXAM;
        itemType = CompanyItemTypeEnum.NON_MEASURABLE.getType();
        isOkr = BusinessConstant.TRUE;
        updatedTime = new Date();
        updatedUser = updateEmp;
        this.openOkrScore = refOkr.getOpenOkrScore();
        this.okrScore = refOkr.getOkrScore();
        //指标加减分上限为类别上限
        if (BusinessConstant.KPI_TYPE_CLASSIFY_PLUS.equals(type.getKpiTypeClassify())) {
            plusLimit = type.getMaxExtraScore();
        } else if (BusinessConstant.KPI_TYPE_CLASSIFY_SUBTRACT.equals(type.getKpiTypeClassify())) {
            subtractLimit = type.getMaxExtraScore();
        }
    }

    public void replInputEmpId(String fromEmpId, String toEmpId) {
        this.resultInputType = "user";
        if (StrUtil.isBlank(resultInputEmpId)) {
            this.resultInputEmpId = toEmpId;
            return;
        }
        this.resultInputEmpId = resultInputEmpId.replace(fromEmpId, toEmpId);
    }

    public void userInputType() {
        this.resultInputType = "user";
    }

    public void reComputeItemWeight() {
        //待评分的指标权重
        BigDecimal itemWeight = notSubmitedItemWeight();
        //需要重新加权重的audit
        List<EvalAudit> submitedAudits = hasSubmitedAudits();
        Pecent itemAvgWeight = new Pecent(itemWeight, submitedAudits.size());
        for (EvalAudit audit : submitedAudits) {
            if (submitedAudits.get(0).getId().equals(audit.getId())) {
                //权重除不尽的差值加到第一位上
                audit.setWeight(audit.getWeight().add(itemAvgWeight.getAvgAddDiff()));
                continue;
            }
            audit.setWeight(audit.getWeight().add(itemAvgWeight.getAvgWeight()));
        }
        List<EvalAudit> audits = notSubmitedAudits();
        if (CollUtil.isEmpty(audits)) {
            return;
        }
        for (EvalAudit notSubmitedAudit : audits) {
            notSubmitedAudit.setWeight(BigDecimal.ZERO);
        }
    }

    public void reComputeRaterWeight() {
        //audit权重已经重新发配，这里人重新分配audit里面的权重即可
        for (EvalAudit audit : appointAudits) {
            if (audit.isOrMode()) {
                audit.orModeRaterWeight();
                continue;
            }
            //这个audit没有打过分的，它的权重被瓜分了
            if (audit.notSumitedScore()) {
                audit.submitZero();
                continue;
            }
            //其他audit 合并过当前的audit，把在其他角色里面的人还原到本次audit，获取权重
            List<EvalScoreResult> containAuditIdRss = getContainAuditIdRss(audit.getId());
            Pecent avgRaterWeight = new Pecent(audit.getWeight(), audit.passCnt() + containAuditIdRss.size());
            audit.andModeRaterWeight(avgRaterWeight, containAuditIdRss);
        }
        //最后把每个rs里面的带合并权重加到实际的评分中
        mergeWeightIfNeed();
    }

    private void mergeWeightIfNeed() {
        for (EvalAudit appointAudit : appointAudits) {
            if (BigDecimal.ZERO.equals(appointAudit.getWeight())) {
                continue;
            }
            for (EvalScoreResult subNode : appointAudit.getSubNodes()) {
                if (BigDecimal.ZERO.equals(subNode.getWaitMergeWeight())) {
                    continue;
                }
                subNode.setScoreWeight(subNode.getScoreWeight().add(subNode.getWaitMergeWeight()));
            }
        }
    }

    private List<EvalScoreResult> getContainAuditIdRss(String auditId) {
        List<EvalScoreResult> rss = new ArrayList<>();
        for (EvalAudit audit : appointAudits) {
            if (audit.getId().equals(auditId)) {
                continue;
            }
            if (BigDecimal.ZERO.equals(audit.getWeight())) {
                continue;
            }
            for (EvalScoreResult subNode : audit.getSubNodes()) {
                if (!subNode.isPassed()) {
                    continue;
                }
                if (BigDecimal.ZERO.equals(subNode.getScoreWeight())) {
                    continue;
                }
                if (CollUtil.isEmpty(subNode.getMergeRsInfos())) {
                    continue;
                }
                List<MergeRsInfo> mergeRsInfos = subNode.getMergeRsInfos().stream().filter(info -> info.getAuditId().equals(auditId)).collect(Collectors.toList());
                if (CollUtil.isEmpty(mergeRsInfos)) {
                    continue;
                }
                rss.add(subNode);
            }
        }
        return rss;
    }

    private List<EvalAudit> hasSubmitedAudits() {
        List<String> auditIds = getMergedAudits();
        return appointAudits.stream().filter(audit -> (audit.hasSubmited() || auditIds.contains(audit.getId()))).collect(Collectors.toList());
    }

    private List<EvalAudit> notSubmitedAudits() {
        List<String> auditIds = getMergedAudits();
        return appointAudits.stream().filter(audit -> (audit.subNodesNotPass() && !auditIds.contains(audit.getId()))).collect(Collectors.toList());
    }

    private BigDecimal notSubmitedItemWeight() {
        BigDecimal totalWeight = BigDecimal.ZERO;
        List<String> audits = getMergedAudits();
        for (EvalAudit audit : appointAudits) {
            if (!audit.subNodesNotPass()) {
                continue;
            }
            if (audits.contains(audit.getId())) {
                continue;
            }
            totalWeight = totalWeight.add(audit.getWeight());
        }
        //这里需要处理一个特殊场景，就是发起评分的时候，合并权重，对应的audit被删了，但是权重只给了rs，这里要重新
        //分配audit的权重。 一个指标指定评分不管有几个层级，不管或签会签，audit权重之和是100，如果小于100则说明，有audit被合调了，需要拿回差权
        BigDecimal itemTotalWeight = new BigDecimal(100);
        if (!itemTotalWeight.equals(auditTotalWeight())) {
            BigDecimal diffWeight = diff(new BigDecimal(100), auditTotalWeight());
            totalWeight = totalWeight.add(diffWeight);
        }
        return totalWeight;
    }

    //差值
    private BigDecimal diff(BigDecimal itemTotalWeight, BigDecimal auditTotalWeight) {
        return itemTotalWeight.subtract(auditTotalWeight);
    }

    private BigDecimal auditTotalWeight() {
        BigDecimal weight = BigDecimal.ZERO;
        for (EvalAudit appointAudit : appointAudits) {
            weight = weight.add(appointAudit.getWeight());
        }
        return weight;
    }

    public boolean notAnyRaterSubmited() {
        List<String> mergedAudits = getMergedAudits();
        for (EvalAudit audit : appointAudits) {
            List<EvalScoreResult> passSubNodes = audit.getSubNodes().stream().filter(sub -> (sub.isPassed() || mergedAudits.contains(audit))).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(passSubNodes)) {
                return false;
            }
        }
        return true;
    }

    public boolean itemScored() {
        for (EvalAudit audit : appointAudits) {
            if (!audit.subNodesPass()) {
                return false;
            }
        }
        return true;
    }

    public void submitScoreZero() {
        for (EvalAudit appointAudit : appointAudits) {
            List<EvalScoreResult> subNodes = appointAudit.getSubNodes();
            for (EvalScoreResult subNode : subNodes) {
                //给指标打分
                subNode.passed(BigDecimal.ZERO, "");
            }
        }
    }

    public List<String> getAppointRaters() {
        List<String> raters = new ArrayList<>();
        for (EvalAudit appointAudit : appointAudits) {
            raters.addAll(appointAudit.getSubNodes()
                    .stream()
                    .filter(sub -> !sub.isPassed())
                    .map(sub -> sub.getScorerId())
                    .collect(Collectors.toList())
            );
        }
        return raters;
    }

    public List<EvalScoreResult> getSubNodes() {
        List<EvalScoreResult> subNodes = new ArrayList<>();
        for (EvalAudit appointAudit : appointAudits) {
            subNodes.addAll(appointAudit.getSubNodes());
        }
        return subNodes;
    }

    public void matchAudits(List<EvalAudit> appointAudits) {
        for (EvalAudit appointAudit : appointAudits) {
            if (!kpiItemId.equals(appointAudit.getKpiItemId())) {
                continue;
            }
            this.appointAudits.add(appointAudit);
        }
    }


    private List<String> getMergedAudits() {
        List<String> audits = new ArrayList<>();
        for (EvalAudit audit : appointAudits) {
            for (EvalScoreResult subNode : audit.getSubNodes()) {
                if (subNode.mergedWeight() && subNode.isPassed()) {
                    audits.addAll(subNode.getMergeRsAudits());
                }
            }
        }
        return audits;
    }

    @Override
    public void acceptOkrValue(OkrAction action) {
        this.okrScore = action.getScore();
        this.itemFinishValue = action.getFinishValue();
        this.finishValue = action.getFinishValue();
        this.actionValueSet = action.getActionValueSet();
        this.krSelfScore = action.getSelfScore();
        this.krSelfContent = action.getSelfContent();
        this.krSuperiorScore = action.getSuperiorScore();
        this.krSuperiorContent = action.getSuperiorContent();
        this.okrActionUpdates = action.getOkrActionUpdates();
        this.targetTags = action.getTargetTags();
    }

    @Override
    public boolean refOkr() {
        return Boolean.valueOf(okrRefFlag);
    }

    @Override
    public void setItemRefKRList(List<OkrAction> okrActions2) {

    }

    public void computeFinalOkrScore(boolean fullScoreRange, Boolean typeWeightOpen) {
        if (!openOkrScore()) {
            return;
        }
        if (null == okrScore) {
            this.itemAutoScore = new BigDecimal(0);
            return;
        }
        if (typeWeightOpen) {
            this.itemAutoScore = fullScoreRange ?
                    okrScore.multiply(itemWeight).divide(new BigDecimal(100)).multiply(kpiTypeWeight).divide(new BigDecimal(100)) :
                    okrScore.multiply(kpiTypeWeight).divide(new BigDecimal(100));
            return;
        }
        if (fullScoreRange) {
            this.itemAutoScore = okrScore.multiply(itemWeight).divide(new BigDecimal(100));
            return;
        }
        this.itemAutoScore = okrScore;
    }

    public void appendInputRaters(List<KpiEmp> inputEmps) {
        if (CollUtil.isEmpty(inputEmps)) {
            return;
        }
        StringBuilder inputIds = new StringBuilder();
        StringBuilder inputNames = new StringBuilder();
        for (KpiEmp inputEmp : inputEmps) {
            inputIds.append(inputEmp.getEmpId()).append(",");
            inputNames.append(inputEmp.getEmpName()).append(",");
        }
        this.resultInputEmpId = inputIds.substring(0, inputIds.length() - 1);
        this.resultInputEmpName = inputNames.substring(0, inputNames.length() - 1);
    }

    public EvalItemScoreRule updateMutualRater(String scene, List<Rater> raters) {
        boolean isPeerScore = AuditEnum.PEER_SCORE.getScene().equals(scene);
        EvalItemScoreRule beforeScoreRule = itemScoreRule.clone();
        if (isPeerScore) {
            beforeScoreRule.getPeerRater().setRaters(raters);
        } else {
            beforeScoreRule.getSubRater().setRaters(raters);
        }
        this.itemScoreRule = beforeScoreRule;
        return beforeScoreRule;
    }

    public Collection<String> parseInputEmp(String selfEmpId, LeaderManger leaderManger) {
        if (StrUtil.isNotBlank(resultInputEmpId)) {
            return Arrays.asList(getResultInputEmpId().split(","));
        }
        if (ifSelfInput()) {
            this.resultInputEmpId = selfEmpId;
            return Arrays.asList(selfEmpId);
        }
        if (needLeaderInput()) {
            leaderManger.load(companyId, new EmpId(selfEmpId));
            List<String> leaderIds = leaderManger.getLeaderIds(Integer.valueOf(managerLevel));
            this.resultInputEmpId = StrUtil.join(",", leaderIds);
            return leaderIds;
        }
        return Collections.emptyList();
    }

    private boolean needLeaderInput() {
        return BusinessConstant.APPROVER_TYPE_MANAGER.equals(getResultInputType());
    }

    public boolean itemWeightIsZeroNotCompute() {
        //当指标权重为空或0的时候，指标得分不参与计算
        if (null == itemWeight || itemWeight.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        return false;
    }

    public boolean filterWaitScore(boolean keepAllItem, Map<String, KpiEmp> empMap, String opEmpId,
                                   String reScoreNode, boolean isOpenAvgWeightCompute) {
        if (isEvalClosed()) {
            return true;
        }
        buildAlreadyNodes(empMap, new EmpId(opEmpId), reScoreNode, isOpenAvgWeightCompute);
        //分开成已评分和未评分两组
        Stream<EvalScoreResult> itemRsStream = waitScoresOld.stream().filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId));
        if (StrUtil.isNotBlank(reScoreNode)) {//重新评分
            waitScoresOld = itemRsStream.filter(itemRs -> StrUtil.equals(itemRs.getScorerType(), reScoreNode)).collect(Collectors.toList());
        } else {
            waitScoresOld = itemRsStream.filter(itemRs -> !itemRs.isPassed()).collect(Collectors.toList());
        }
        return keepAllItem || CollUtil.isNotEmpty(waitScoresOld);
    }

    public boolean filterWaitScoreV3(boolean keepAllItem,String opEmpId, String reScoreNode) {
        if (isEvalClosed()) {
            return true;
        }
        buildAlreadyNodesV3(new EmpId(opEmpId),reScoreNode);
        //分开成已评分和未评分两组
        Stream<EvalScorerNodeKpiItem> itemRsStream = waitScores.stream().filter(typeRs -> StrUtil.equals(typeRs.getScorerId(), opEmpId));
        if (StrUtil.isNotBlank(reScoreNode)) {//重新评分
            waitScores = itemRsStream
                    .filter(itemRs -> StrUtil.equals(itemRs.getScorerType(), reScoreNode))
                    .filter(EvalScorerNodeScoreItemBase::isCanCompute) //待提交的需要是可以计算的指标
                    .collect(Collectors.toList());
        } else {
            waitScores = itemRsStream
                    .filter(itemRs -> !itemRs.isPassed())
                    .filter(EvalScorerNodeScoreItemBase::isCanCompute) //待提交的需要是可以计算的指标
                    .collect(Collectors.toList());
        }
        return keepAllItem || CollUtil.isNotEmpty(waitScores);
    }

    public void buildAlreadyNodes(Map<String, KpiEmp> empMap, EmpId opEmpId, String reSubmitNode, boolean isOpenAvgWeightCompute) {
        List<EvalScoreResult> alreadys = waitScoresOld.stream().filter(EvalScoreResult::isPassed).collect(Collectors.toList());
        waitScoresOld.removeIf(itemRs -> itemRs.isWaitDispatch());
        if (StrUtil.isNotBlank(reSubmitNode)) {
            waitScoresOld.removeIf(itemRs -> itemRs.isPassed() && !itemRs.matchBelong(reSubmitNode, opEmpId));
        } else {
            if (CollUtil.isNotEmpty(alreadys)) {
                List<String> resIds = alreadys.stream().map(s -> s.getId()).collect(Collectors.toList());
                waitScoresOld = waitScoresOld.stream().filter(s -> !resIds.contains(s.getId())).collect(Collectors.toList());
            }
        }
        ListWrap<EvalScoreResult> nodeGroup = new ListWrap<>(alreadys).groupBy(scr -> scr.getScorerType());
        nodeGroup.getGroups().forEach((node, results1) -> {
            Set<String> seen = new HashSet<>();
            if (CollUtil.isNotEmpty(results1)) {
                if (EvaluateAuditSceneEnum.isPeerScore(node) || EvaluateAuditSceneEnum.isSubScore(node)) {
                    results1 = results1.stream()
                            .filter(s -> seen.add(s.getKpiItemId() + s.getScorerId() + node + s.getScoreWeight()))
                            .collect(Collectors.toList());
                }
            }
            NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
            Set<String> scoreIds = new HashSet<>();
            for (EvalScoreResult srs : results1) {
                KpiEmp kpiEmp = empMap.get(srs.getScorerId());
                NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(kpiEmp.getEmpName(), kpiEmp.getAvatar(), srs.getScorerId(), srs.getVetoFlag(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                ofItem.scoreWeight = srs.getScoreWeight();
                ofItem.level = srs.getScoreLevel();
                ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                ofItem.acceptScore(srs.getScore(), srs.getPlusScore(), srs.getSubtractScore());
                ofItem.acceptScoreOption(srs.getScoreOption());
                //加减分项分数统计和正常指标得分不是一个字段里面
                nodeOfItemScore.addItem(ofItem, srs.matchScore());
                //添加环节权重
                nodeOfItemScore.addNodeWeight(node, this.itemScoreRule);
                if (null != srs.getScoreWeight() && srs.getScoreWeight().compareTo(BigDecimal.ZERO) != 0) {
                    scoreIds.add(srs.getScorerId());
                }
            }
            buildNodeOfItemScore(nodeOfItemScore, isOpenAvgWeightCompute, scoreIds);//互评计算平均分
            this.alreadyNodes.add(nodeOfItemScore);
        });
    }

    public void buildAlreadyNodesV3(EmpId opEmpId, String reSubmitNode) {
        if (CollUtil.isEmpty(waitScores)){
            return;
        }
        List<EvalScorerNodeKpiItem> alreadys = waitScores.stream().filter(EvalScorerNodeKpiItem::isPassed).collect(Collectors.toList());
        waitScores.removeIf(EvalScorerNodeKpiItem::isWaitDispatch);
        if (StrUtil.isNotBlank(reSubmitNode)) {
            waitScores.removeIf(rs -> rs.isPassed() && rs.isCanCompute()  && !rs.matchBelong(reSubmitNode, opEmpId));
        } else {
            if (CollUtil.isNotEmpty(alreadys)){
                List<String> resIds = alreadys.stream().map(EvalScorerNodeKpiItem::getId).collect(Collectors.toList());
                waitScores = waitScores.stream()
                        .filter(s -> !resIds.contains(s.getId()))
                        .filter(EvalScorerNodeScoreItemBase::isCanCompute) //待提交的需要是可以计算的指标
                        .collect(Collectors.toList());
            }
        }
        ListWrap<EvalScorerNodeKpiItem> nodeGroup = new ListWrap<>(alreadys).groupBy(EvalScorerNodeKpiItem::getScorerType);
        nodeGroup.getGroups().forEach((node, results1) -> {
            NodeOfItemScore nodeOfItemScore = new NodeOfItemScore(node);
            for (EvalScorerNodeKpiItem srs : results1) {
                if (!srs.isShow()){
                    continue; //排除不可以展示的kpiItem
                }
                NodeOfItemScore.ScoreResultOfItem ofItem = new NodeOfItemScore.ScoreResultOfItem(srs.getScorerName(),
                        srs.getScorerAvatar(), srs.getScorerId(), srs.getVetoFlag(), Objects.isNull(srs.getUpdatedTime()) ? srs.getCreatedTime() : srs.getUpdatedTime());
                ofItem.scoreWeight = srs.getScoreWeight();
                ofItem.level = srs.getScoreLevel();
                ofItem.auditStatus = srs.getAuditStatus();
                ofItem.acceptComment(srs.getScoreAttUrl(), srs.getScoreComment());
                ofItem.acceptScore(srs.getScore());
                ofItem.acceptScoreOption(srs.getScoreOption());
                //加减分项分数统计和正常指标得分不是一个字段里面
                nodeOfItemScore.addItemV3(ofItem, this);
                //添加环节权重
                nodeOfItemScore.addNodeWeight(node, this.itemScoreRule);
            }
            this.alreadyNodes.add(nodeOfItemScore);
        });
        this.waitScores = filterRepeat(waitScores);//去重
    }

    public List<EvalScorerNodeKpiItem> filterRepeat(List<EvalScorerNodeKpiItem>  waitScores){
        if (CollUtil.isEmpty(waitScores)) {
            return new ArrayList<>();
        }

        //waitScores  asScorerIdAndOrderKey 去重
        Set<String> uniqueKeys = new HashSet<>();
        return waitScores.stream()
                .filter(e -> uniqueKeys.add(e.asScorerIdAndOrderKey()))
                .collect(Collectors.toList());
    }
    private NodeOfItemScore buildNodeOfItemScore(NodeOfItemScore nodeOfItemScore, boolean isOpenAvgWeightCompute, Set<String> scoreIds) {
        if (isOpenAvgWeightCompute || CollUtil.isEmpty(scoreIds)
                || !EvaluateAuditSceneEnum.mutualScoreTypes().contains(nodeOfItemScore.getNode())) {
            return nodeOfItemScore;
        }

        int scoreNum = scoreIds.size();//互评计算平均分
        MutualScoreResultCompute compute = new MutualScoreResultCompute(nodeOfItemScore.getScore(), scoreNum);
        nodeOfItemScore.setScore(compute.computeAvgScore()); //计算互评平均分
        log.info(" buildNodeOfItemScore.nodeOfItemScore:{},scoreNum:{}", JSONUtil.toJsonStr(nodeOfItemScore), scoreNum);
        return nodeOfItemScore;
    }

    public IItemScoreRule itemScoreRule() {
        return itemScoreRule;
    }

    //执行继承维度|全局流程操作
    private void extendsRaterRuleOpt(EvalItemScoreRule parentRule) {
        if (!needExtendsRaterRule()) {
            return;
        }
        this.itemScoreRule = new EvalItemScoreRule();
        BeanUtils.copyProperties(parentRule, itemScoreRule);
        this.itemScoreRule.setKpiItemId(this.getKpiItemId());
        this.itemScoreRule.setKpiTypeId(this.getKpiTypeId());
        this.itemScoreRule.asRaterConf(itemScoreRule.getSelfRater(), itemScoreRule.getPeerRater(), itemScoreRule.getSubRater(), itemScoreRule.getSuperRater(), itemScoreRule.getAppointRater());
    }

    //1 执行继承维度| 2 尝试继承全局流程
    public void extendsRaterRuleOpt(EvalItemScoreRule globalRule, EvalItemScoreRule typeRule) {
        if (isAutoItem()) {
            return;
        }
        if (!needExtendsRaterRule()) {
            return;
        }
        if (typeRule != null) {//优先继承维度
            this.extendsRaterRuleOpt(typeRule);
            return;
        }
        this.extendsRaterRuleOpt(globalRule);
    }

    //执行继承维度等级操作
    public void extendsTypeLevelOpt(IndLevelGroup levelGroup) {
        if (super.needExtendsTypeLevel()) {
            this.indLevelGroup = levelGroup;
        }
    }

    //已有应用的考核流程不使用默认的
    private boolean needExtendsRaterRule() {
        if (isDirectional()) {
            return false;
        }
        //已有应用的考核流程不使用默认的
        if (itemScoreRule != null) {
            return false;
        }
        if (super.isEvalClosed()) {//无需评分|评级
            return false;
        }
        return true;
    }

    public void needUpdateLevel(EvalScoreResult submited) {
        if (!this.isEvalLevel()) {
            submited.setUpdateTypeLevel(false);
            return;
        }
        List<EvalScoreResult> alreadys = waitScoresOld.stream().filter(typeRs -> typeRs.isPassed()).collect(Collectors.toList());
        boolean containSup = new ListWrap<>(alreadys).mapTo(EvalScoreResult::getScorerType).contains(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        if (!containSup || (containSup && submited.isSupper())) {
            submited.setUpdateTypeLevel(true);
            return;
        }
    }

    public void needUpdateLevelV3(EvalScoreResult submited) {
        if (!this.isEvalLevel()) {
            submited.setUpdateTypeLevel(false);
            return;
        }
        List<EvalScorerNodeKpiItem> alreadys = waitScores.stream().filter(EvalScorerNodeKpiItem::isPassed).collect(Collectors.toList());
        boolean containSup = new ListWrap<>(alreadys).mapTo(EvalScorerNodeKpiItem::getScorerType).contains(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        if (!containSup || submited.isSupper()) {
            submited.setUpdateTypeLevel(true);
        }
    }

    public boolean matchAnySuperScore(String scorerId) {
        for (EvalScoreResult waitScore : waitScoresOld) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }

    public boolean matchAnySuperScoreV3(String scorerId) {
        if(CollUtil.isEmpty(waitScores)){
            return false;
        }
        for (EvalScorerNodeKpiItem waitScore : waitScores) {
            if (waitScore.matchBelong(SubScoreNodeEnum.SUPERIOR_SCORE.getScene(), new EmpId(scorerId))) {
                return true;
            }
        }
        return false;
    }

    private BigDecimal getWeightScore(FinalWeightSumScore totalOfSum, EvalScoreResult result) {
        if (totalOfSum.isPeerScoreScene(result.getScorerType()) || totalOfSum.isSubScoreScene(result.getScorerType())) {
            return itemWeightIsZeroNotCompute() ? BigDecimal.ZERO : totalOfSum.getGetMutualScoreFunc().apply(result);
        } else {
            return itemWeightIsZeroNotCompute() ? BigDecimal.ZERO : totalOfSum.getGetScoreFunc().apply(result);
        }
    }

    public BigDecimal getNodeWeight(SubScoreNodeEnum scene) {
        return this.isDirectional() ? Pecent.ONE : itemScoreRule.nodePecWeight(scene);
    }

    public void computeV3NormalAutoFinalScore(TotalFinalNodeScoreV3 totalOfSum, TypeFinalNodeScoreV3 typeScore) {
        //计算过环节权重的自动计算指标分
        if (Objects.isNull(this.itemAutoScore) || !isAutoItem()) {
            return;
        }
        totalOfSum.computeAutoFinalWeightScore(this.itemAutoScore, typeScore);
    }

    public void setItemNodeScore() {
        log.info("==setItemNodeScore 计算指标最终得分：v3FinalWeightItemScore:{}",JSONUtil.toJsonStr(v3FinalWeightItemScore));
        this.itemFinalSelfScore = v3FinalWeightItemScore.getFinalSelfScore();
        this.itemFinalPeerScore = v3FinalWeightItemScore.getFinalPeerScore();
        this.itemFinalSubScore = v3FinalWeightItemScore.getFinalSubScore();
        this.itemFinalSuperiorScore = v3FinalWeightItemScore.getFinalSuperiorScore();
        this.itemFinalAppointScore = v3FinalWeightItemScore.getFinalAppointScore();

        this.itemSelfScore = v3FinalWeightItemScore.getSelfScore();
        this.itemPeerScore = v3FinalWeightItemScore.getPeerScore();
        this.itemSubScore = v3FinalWeightItemScore.getSubScore();
        this.itemSuperiorScore = v3FinalWeightItemScore.getSuperiorScore();
        //计算指标指定评分人 与指定评分共用一个字段，不会同时存在
        this.itemItemScore = v3FinalWeightItemScore.getFinalItemScore();
        this.itemFinalItemScore = v3FinalWeightItemScore.getItemItemScore();
        this.itemAppointScore = v3FinalWeightItemScore.getAppointScore();
      //  this.v3FinalWeightItemScore.sum();
        this.itemFinalScore = v3FinalWeightItemScore.getItemSum();
        if (Objects.nonNull(itemFinalScore)) {
            itemFinalScore = itemFinalScore.setScale(2, RoundingMode.HALF_UP);
        }
        log.info("==setItemNodeScore计算指标得分：{}", this.itemFinalScore);
        this.itemFinalScore = null == this.itemFinalScore ? BigDecimal.ZERO : this.itemFinalScore;
        this.itemFinalOriginalScore = this.itemFinalScore;
        this.itemScore = v3FinalWeightItemScore.getItemScore();
        this.itemOriginalScore =  this.itemScore;
        log.info("==setItemNodeScore计算指标得分如果为null，设置默认值,itemFinalScore:{},itemScore:{}",itemFinalScore, itemScore);
    }
    public BigDecimal computeNormal(FinalWeightSumScore totalOfSum, boolean isOpenAvgWeightCompute) {
        //   BigDecimal sum = BigDecimal.ZERO;
        if (isOpenAvgWeightCompute) {
            return computeNormalOpenAvgWeight(totalOfSum);
        }
        return computeNormalOpenAvgScore(totalOfSum);
    }


    public BigDecimal computeNormalOpenAvgWeight(FinalWeightSumScore totalOfSum) {
        BigDecimal sum = BigDecimal.ZERO;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            BigDecimal weightScore = itemWeightIsZeroNotCompute() ? BigDecimal.ZERO : totalOfSum.getGetScoreFunc().apply(result);
            totalOfSum.addByType(result.getScorerType(), weightScore);
            sum = sum.add(weightScore == null ? BigDecimal.ZERO : weightScore);
        }
        return sum;
    }

    public BigDecimal computeNormalOpenAvgScore(FinalWeightSumScore totalOfSum) {
        BigDecimal sum = BigDecimal.ZERO;
        Set<String> peerScorers = new HashSet<>();
        Set<String> subScorers = new HashSet<>();
        BigDecimal peerScore = BigDecimal.ZERO;
        BigDecimal subScore = BigDecimal.ZERO;
        List<EvalScoreResult> waitScoreResults = this.getWaitScoresOld();

        List<EvalScoreResult> waitScoreResultsFinal = new ArrayList<>();
        if (CollUtil.isNotEmpty(waitScoreResults)) {
            Map<String, List<EvalScoreResult>> scoreTypeResultMap = waitScoreResults.stream().collect(Collectors.groupingBy(EvalScoreResult::getScorerType));
            for (String scoreType : scoreTypeResultMap.keySet()) {
                if (EvaluateAuditSceneEnum.isPeerScore(scoreType) || EvaluateAuditSceneEnum.isSubScore(scoreType)) {
                    Set<String> seen = new HashSet<>();
                    List<EvalScoreResult> res = scoreTypeResultMap.get(scoreType).stream()
                            .filter(s -> seen.add(s.getKpiItemId() + s.getScorerId() + s.getScorerType() + s.getScoreWeight())).collect(Collectors.toList());
                    waitScoreResultsFinal.addAll(res);
                } else {
                    waitScoreResultsFinal.addAll(scoreTypeResultMap.get(scoreType));
                }
            }
        }
        for (EvalScoreResult result : waitScoreResultsFinal) {
            BigDecimal weightScore = getWeightScore(totalOfSum, result);
            log.info(" ===computeNormal.weightScore:{} ", weightScore);
            if (totalOfSum.isPeerScoreScene(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    peerScorers.add(result.getScorerId());
                    peerScore = FinalWeightSumScore.addNullZero(peerScore, weightScore);
                }
                continue;
            }
            if (totalOfSum.isSubScoreScene(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    subScorers.add(result.getScorerId());
                    subScore = FinalWeightSumScore.addNullZero(subScore, weightScore);
                }
                continue;
            }
            totalOfSum.addByType(result.getScorerType(), weightScore);
            sum = sum.add(weightScore == null ? BigDecimal.ZERO : weightScore);
        }

        log.info("===计算互评平均分===peerScore:{}, peerScorers.size:{},subScore:{}, peerScorers.size:{}"
                , peerScore, peerScorers.size(), subScore, subScorers.size());
        peerScore = totalOfSum.computeAvgScoreByMutualScene(peerScore, peerScorers.size());
        subScore = totalOfSum.computeAvgScoreByMutualScene(subScore, subScorers.size());
        if (peerScore.compareTo(BigDecimal.ZERO) > 0) {
            totalOfSum.setPeerScore(FinalWeightSumScore.addNullZero(totalOfSum.getPeerScore(), peerScore));
        }
        if (subScore.compareTo(BigDecimal.ZERO) > 0) {
            totalOfSum.setSubScore(FinalWeightSumScore.addNullZero(totalOfSum.getSubScore(), subScore));
        }
        log.info("===计算互评平均分结束===peerScore:{},subScore:{}", peerScore, subScore);
        sum = sum.add(peerScore);
        sum = sum.add(subScore);
        log.info("===计算正常考核总分===sum：{}", sum);
        return sum;
    }


    //加减分项合并的分类
    public BigDecimal computePlusS1(boolean isOpenAvgWeightCompute) {
        if (isOpenAvgWeightCompute) {
            return computePlusS1OpenAvgWeight();
        }
        return computePlusS1OpenAvgScore();
    }

    public BigDecimal computePlusS1OpenAvgWeight() {
        BigDecimal kpiPlusSum = null;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            kpiPlusSum = FinalWeightSumScore.addNullZero(kpiPlusSum, result.getFinalWeightPlusScore());
        }
        return kpiPlusSum;
    }

    public BigDecimal computePlusS1OpenAvgScore() {
        BigDecimal kpiPlusSum = BigDecimal.ZERO;
        Set<String> peerScorers = new HashSet<>();
        Set<String> subScorers = new HashSet<>();
        BigDecimal peerScore = BigDecimal.ZERO;
        BigDecimal subScore = BigDecimal.ZERO;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            if ("peer_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    peerScorers.add(result.getScorerId());
                    peerScore = FinalWeightSumScore.addNullZero(peerScore, result.getFinalWeightPlusScore());
                }
                continue;
            }

            if ("sub_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    subScorers.add(result.getScorerId());
                    subScore = FinalWeightSumScore.addNullZero(subScore, result.getFinalWeightPlusScore());
                }
                continue;
            }
            kpiPlusSum = FinalWeightSumScore.addNullZero(kpiPlusSum, result.getFinalWeightPlusScore());
        }

        MutualScoreResultCompute compute = new MutualScoreResultCompute(peerScore, peerScorers.size());
        kpiPlusSum = kpiPlusSum.add(compute.computeAvgScore()); //计算互评平均分
        MutualScoreResultCompute compute2 = new MutualScoreResultCompute(subScore, subScorers.size());
        kpiPlusSum = kpiPlusSum.add(compute2.computeAvgScore()); //计算互评平均分
        return kpiPlusSum;
    }







    //加减分项合并的分类
    public BigDecimal computeSubtract2(boolean isOpenAvgWeightCompute) {
        if (isOpenAvgWeightCompute) {
            return computeSubtract2OpenAvgWeight();
        }
        return computeSubtract2OpenAvgScore();
    }

    public BigDecimal computeSubtract2OpenAvgWeight() {
        BigDecimal typeSubtractSum = null;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            typeSubtractSum = FinalWeightSumScore.addNullZero(typeSubtractSum, result.getFinalWeightSubtractScore());
        }
        return typeSubtractSum;
    }
    public BigDecimal computeSubtract2OpenAvgScore() {
        BigDecimal typeSubtractSum = BigDecimal.ZERO;
        Set<String> peerScorers = new HashSet<>();
        Set<String> subScorers = new HashSet<>();
        BigDecimal peerScore = BigDecimal.ZERO;
        BigDecimal subScore = BigDecimal.ZERO;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            if ("peer_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    peerScorers.add(result.getScorerId());
                    peerScore = FinalWeightSumScore.addNullZero(peerScore, result.getFinalWeightSubtractScore());
                }
                continue;
            }

            if ("sub_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    subScorers.add(result.getScorerId());
                    subScore = FinalWeightSumScore.addNullZero(subScore, result.getFinalWeightSubtractScore());
                }
                continue;
            }
            typeSubtractSum = FinalWeightSumScore.addNullZero(typeSubtractSum, result.getFinalWeightSubtractScore());
        }

        MutualScoreResultCompute compute = new MutualScoreResultCompute(peerScore, peerScorers.size());
        typeSubtractSum = typeSubtractSum.add(compute.computeAvgScore()); //计算互评平均分
        MutualScoreResultCompute compute2 = new MutualScoreResultCompute(subScore, subScorers.size());
        typeSubtractSum = typeSubtractSum.add(compute2.computeAvgScore()); //计算互评平均分
        return typeSubtractSum;
    }

    //计算加分项
    public BigDecimal computePlus(boolean isOpenAvgWeightCompute) {
        if (isOpenAvgWeightCompute) {
            return computePlusOpenAvgWeight();
        }
        return computePlusOpenAvgScore();
    }


    public BigDecimal computePlusOpenAvgWeight() {
        BigDecimal kpiSum = null;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            kpiSum = FinalWeightSumScore.addNullZero(kpiSum, result.getFinalWeightPlusScore());
        }
        return kpiSum;
    }

    public BigDecimal computePlusOpenAvgScore() {
        BigDecimal kpiSum = BigDecimal.ZERO;
        Set<String> peerScorers = new HashSet<>();
        Set<String> subScorers = new HashSet<>();
        BigDecimal peerScore = BigDecimal.ZERO;
        BigDecimal subScore = BigDecimal.ZERO;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            if ("peer_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    peerScorers.add(result.getScorerId());
                    peerScore = FinalWeightSumScore.addNullZero(peerScore, result.getFinalWeightPlusScore());
                }
                continue;
            }

            if ("sub_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    subScorers.add(result.getScorerId());
                    subScore = FinalWeightSumScore.addNullZero(subScore, result.getFinalWeightPlusScore());
                }
                continue;
            }
            kpiSum = FinalWeightSumScore.addNullZero(kpiSum, result.getFinalWeightPlusScore());
        }

        MutualScoreResultCompute compute = new MutualScoreResultCompute(peerScore, peerScorers.size());
        kpiSum = kpiSum.add(compute.computeAvgScore()); //计算互评平均分
        MutualScoreResultCompute compute2 = new MutualScoreResultCompute(subScore, subScorers.size());
        kpiSum = kpiSum.add(compute2.computeAvgScore()); //计算互评平均分
        return kpiSum;
    }


    //计算减分分项
    public BigDecimal computeSubtract(boolean isOpenAvgWeightCompute) {
        if (isOpenAvgWeightCompute) {
            return computeSubtractOpenAvgWeight();
        }
        return computeSubtractOpenAvgScore();

    }

    public BigDecimal computeSubtractOpenAvgWeight() {
        BigDecimal kpiSum = null;
        for (EvalScoreResult result : getWaitScoresOld()) {
            kpiSum = FinalWeightSumScore.addNullZero(kpiSum, result.getFinalWeightSubtractScore());
        }
        return kpiSum;
    }

    public BigDecimal computeSubtractOpenAvgScore() {
        BigDecimal kpiSubtract = BigDecimal.ZERO;
        Set<String> peerScorers = new HashSet<>();
        Set<String> subScorers = new HashSet<>();
        BigDecimal peerScore = BigDecimal.ZERO;
        BigDecimal subScore = BigDecimal.ZERO;
        for (EvalScoreResult result : this.getWaitScoresOld()) {
            if ("peer_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    peerScorers.add(result.getScorerId());
                    peerScore = FinalWeightSumScore.addNullZero(peerScore, result.getFinalWeightSubtractScore());
                }
                continue;
            }

            if ("sub_score".equals(result.getScorerType())) {
                if (result.getScoreWeight().compareTo(BigDecimal.ZERO) != 0 && "pass".equals(result.getAuditStatus())) {
                    subScorers.add(result.getScorerId());
                    subScore = FinalWeightSumScore.addNullZero(subScore, result.getFinalWeightSubtractScore());
                }
                continue;
            }
            kpiSubtract = FinalWeightSumScore.addNullZero(kpiSubtract, result.getFinalWeightSubtractScore());
        }

        MutualScoreResultCompute compute = new MutualScoreResultCompute(peerScore, peerScorers.size());
        kpiSubtract = kpiSubtract.add(compute.computeAvgScore()); //计算互评平均分
        MutualScoreResultCompute compute2 = new MutualScoreResultCompute(subScore, subScorers.size());
        kpiSubtract = kpiSubtract.add(compute2.computeAvgScore()); //计算互评平均分
        return kpiSubtract;
    }


    //计算okr分项
    public BigDecimal computeOkr(FinalWeightSumScore totalOfSum, boolean isOpenAvgWeightCompute) {
        if (this.openOkrScore()) {
            totalOfSum.addToOkrSum(this.getItemAutoScore());
            return this.getItemAutoScore();
        } else {
            return computeNormal(totalOfSum, isOpenAvgWeightCompute);
        }
    }
    public boolean isMustResultInput() {
        return Objects.equals(this.mustResultInput, 1);
    }

    public void setTarget(String targetId, String targetName) {
        this.targetId = targetId;
        this.targetName = targetName;
    }

    public void asDisplayFlow(AsDisplayFlowRs nodes) {
        if (isDirectional()) {//定向
            List<Rater> raters = getScorers(null, null).stream().map(empStaff -> new Rater(empStaff.getEmpId(), empStaff.getEmpName())).collect(Collectors.toList());
            nodes.addNodeOpt(SubScoreNodeEnum.ITEM_SCORE, 1, raters, waitScoresOld);
            return;
        }
        if (getItemScoreRule() == null) {
            return;
        }
        itemScoreRule.asDisplayFlow(nodes, empId, waitScoresOld);
    }

    public void initNodeRater(Map<String, RaterNode> nodeMap) {
        if (isAutoItem()) {
            return;
        }
        //定向评分
        initImteSocreRater(nodeMap);
        if (itemScoreRule == null) {
            return;
        }
        itemScoreRule.initNodeRater(nodeMap);
    }

    public void initImteSocreRater(Map<String, RaterNode> nodeMap) {
        if (isDirectional()) {//定向
            List<Rater> raters = getScorers(null, null).stream().map(empStaff -> new Rater(empStaff.getEmpId(), empStaff.getEmpName())).collect(Collectors.toList());
            if (raters.isEmpty()) {
                return;
            }
            if (!nodeMap.containsKey("item")) {
                RaterNode node = new RaterNode("item", raters);
                nodeMap.put("item", node);
            } else {
                nodeMap.get("item").addAllRater(raters);
            }
        }
    }

    public void builderKpiItemUsedFields(List<KpiItemUsedField> usedFieldDos) {
        if (CollUtil.isNotEmpty(usedFieldDos)) {
            Map<String, List<KpiItemUsedField>> groupMap = usedFieldDos.stream()
                    .collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));
            if (groupMap.get(this.kpiItemId) != null) {
                List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
                List<KpiItemUsedField> itemUsedFields = groupMap.get(this.kpiItemId);
                if (itemUsedFields != null && itemUsedFields.size() > 0) {
                    itemUsedFields.forEach(item -> {
                        ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                        BeanUtils.copyProperties(item, fieldValue);
                        fieldValue.setFieldName(item.getName());
                        fieldValue.setFieldValue(item.getValue());
                        fieldValue.setFieldStatus(item.getStatus());
                        fieldValue.setId(item.getFieldId());
                        fieldValue.setIsReq(item.getReq());
                        fieldValueList.add(fieldValue);
                    });
                    this.fieldValueList = fieldValueList;
                }
            }
        }
    }

    public void compareInputEmp(ResultInputEmpPackage resultInputEmpPackage, EvalKpi evalKpi) {
        if (!Objects.equals(this.resultInputEmpId, evalKpi.getResultInputEmpId())) {
            //resetFinishInputValue();//录入人变更不清空录入的完成值
            log.info("重置最终提交标识1，kpiItemId:{},resultInputType：{},getResultInputType:{}", kpiItemId, resultInputType, evalKpi.getResultInputType());
            resetFinalSubmitFinishValue();
            resultInputEmpPackage.addAll(inputEmp(evalKpi.getEmpId()), evalKpi.inputEmp(evalKpi.getEmpId()));
            return;
        }
        //录入人相同，如果录入人类型变化也记录
        if (!Objects.equals(this.resultInputType, evalKpi.getResultInputType())) {
            //resetFinishInputValue();//录入人变更不清空录入的完成值
            log.info("重置最终提交标识2，kpiItemId:{},resultInputType：{},getResultInputType:{}", kpiItemId, resultInputType, evalKpi.getResultInputType());
            resetFinalSubmitFinishValue();
            resultInputEmpPackage.addAll(inputEmp(evalKpi.getEmpId()), evalKpi.inputEmp(evalKpi.getEmpId()));
            return;
        }
    }

    public void compareInputEmp(ResultInputEmpPackage resultInputEmpPackage, String empId) {
        //resetFinishInputValue();//录入人变更不清空录入的完成值
        Collection<String> inputEmpIds = inputEmp(empId);
        if (CollUtil.isNotEmpty(inputEmpIds)) {
            log.info("重置最终提交标识3，kpiItemId:{},inputEmpIds：{}", kpiItemId, inputEmpIds);
            resetFinalSubmitFinishValue();
        }
        resultInputEmpPackage.addAll(inputEmpIds);
    }

    public void resetFinalSubmitFinishValue() {
        this.finalSubmitFinishValue = 0;
    }

    public void resetFinishInputValue() {
        this.finalSubmitFinishValue = 0;
        this.itemFinishValue = null;
        this.itemFinishValueText = null;
    }

    public void transferScoringIf(String scoreType, String fromEmpId, KpiEmp toEmp, Boolean raterWasExist, Boolean wasCustom) {
        if (SubScoreNodeEnum.isItemScore(scoreType)) {//定向评分转交
            if (scorerObjId == null) {
                return;
            }
            for (StaffConfItem staffConfItem : scorerObjId) {
                for (ObjItem objItem : staffConfItem.getObjItems()) {
                    if (StrUtil.equals(objItem.getObjId(), fromEmpId)) {
                        objItem.setObjId(toEmp.getEmpId());
                        objItem.setObjName(toEmp.getEmpName());
                    }
                }
            }
        } else {
            if (itemScoreRule != null) {
                itemScoreRule.transferScoringIf(scoreType, fromEmpId, toEmp, raterWasExist, wasCustom);
            }
        }

    }


    public void skipRaterIf(String scoreType, String fromEmpId) {
        if (itemScoreRule != null) {
            itemScoreRule.skipRaterIf(scoreType, fromEmpId);
        }
    }

    //是否未开启此环节
    public boolean isOpenNode(SubScoreNodeEnum node, int nodeOrder) {
        if (isAutoItem() || openOkrScore() || isEvalClosed()) {
            return false;
        }
        if (isDirectional()) {//定向指标,只能开启定向环节,不会有itemScoreRule
            return SubScoreNodeEnum.ITEM_SCORE == node;
        }
        if (!itemScoreRule.isOpenRaterRule(node)) {
            return false;
        }
        INodeConf nodeConf = itemScoreRule.getNodeConf(node, nodeOrder, empId);
        return nodeConf != null;
    }

    public boolean isKr() {
        return Objects.equals(this.okrRefFlag, "true");
    }

    public void loadInputEmps(ListWrap<KpiEmp> inpuEmpMap) {
        if (StrUtil.isBlank(this.resultInputEmpId)) {
            return;
        }
        this.inputEmps = CollUtil.map(StrUtil.splitTrim(this.resultInputEmpId, ","), empId -> inpuEmpMap.mapGet(empId), true);
    }

    public void updateFinishValue(String value) {
        //完成值
        if ("non-measurable".equals(getItemType()) && "text".equals(getInputFormat())) {
            setItemFinishValueText(value);
        } else if (BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(getKpiTypeClassify()) && "text".equals(getInputFormat())) {
            setWorkItemFinishValue(value);
        } else {
            if (null == value || StrUtil.isBlank(value)) {
                setItemFinishValue(new BigDecimal(0));
            } else {
                setItemFinishValue(new BigDecimal(value));
            }
        }
    }

    public FinishValue convertFinishValue(ExcelFinishValue excelFinishValue) {
        if (Objects.isNull(excelFinishValue)) {
            return null;
        }
        FinishValue finishValue = new FinishValue();
        //完成值
        if ("non-measurable".equals(getItemType()) && "text".equals(getInputFormat())) {
            finishValue.setItemFinishValueText(excelFinishValue.getFinishValue());
        } else if (BusinessConstant.KPI_TYPE_CLASSIFY_WORK_ITEM.equals(getKpiTypeClassify()) && "text".equals(getInputFormat())) {
            finishValue.setWorkItemFinishValue(excelFinishValue.getFinishValue());
        } else {
            if (null == excelFinishValue.getFinishValue() || StrUtil.isBlank(excelFinishValue.getFinishValue())) {
                finishValue.setItemFinishValue(new BigDecimal(0));
            } else {
                finishValue.setItemFinishValue(new BigDecimal(excelFinishValue.getFinishValue()));
            }
        }
        finishValue.setKpiTypeId(this.getKpiTypeId());
        finishValue.setKpiItemId(this.kpiItemId);
        finishValue.setId(excelFinishValue.getId());
        return finishValue;
    }

    public boolean isIndLevel() {
        return Objects.equals(this.indLevelGroup.getType(), 0);
    }

    private static List<String> filterCorpIds() {
        // ding522a66d1de4e7aeaa39a90f97fcb1e09: 北京德皓国际会计师事务所
        // ding8b3d719c932b311dacaaa37764f94726: A+绩效演示公司
        // ding1ea98a74651c5ec2f2c783f7214b6d69: 绩效测试公司
        return Arrays.asList("ding522a66d1de4e7aeaa39a90f97fcb1e09", "ding8b3d719c932b311dacaaa37764f94726", "ding1ea98a74651c5ec2f2c783f7214b6d69");
    }

    public void builderItemWaitScore(String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig,
                                     IndLevelGroup indLevelGroup,
                                     ScoreValueConf scoreValueConf) {
        if (CollUtil.isEmpty(this.waitScoresOld)) {
            return;
        }
        if (Objects.nonNull(indLevelGroup)) {
            this.indLevelGroup = indLevelGroup;
        }
        boolean isEvalLevel = Objects.nonNull(this.indLevelGroup);
        boolean isPlusSubScoreType = isPlusSubType() || isPlusType() || isSubtractType();
        for (EvalScoreResult waitScore : this.waitScoresOld) {
            waitScore.markSkip(defaultCustomScoreConfig, defaultPlusSubScoreConfig, isPlusSubScoreType);
            if (isPlusSubScoreType) {
                if (isPlusSubType() || isPlusType()) {
                    waitScore.setPlusScore(BigDecimal.ZERO);
                }
                if (isSubtractType()) {
                    waitScore.setSubtractScore(BigDecimal.ZERO);
                }
                continue;
            }
            if (isOneVoteVetoType()) {
                waitScore.setVetoFlag(Boolean.FALSE.toString());
            }
            ItemScoreValue scoreValue = JSONUtil.toBean(itemScoreValue, ItemScoreValue.class);
            if (isEvalLevel && !scoreValue.isSystemOption()) {
                if (isIndLevel()) {
                    waitScore.setScoreLevel(this.indLevelGroup.getLevels().get(0).getName());
                    continue;
                }
                waitScore.setScore(this.indLevelGroup.getLevels().get(0).getScore());
                continue;
            }
            //对公司 dingCorpId ： ='ding522a66d1de4e7aeaa39a90f97fcb1e09', //北京德皓国际会计师事务所 特殊处理，不管配置什么，自动提交都分都置为0
            if (Objects.nonNull(dingCorpId) && filterCorpIds().contains(dingCorpId)) {
                waitScore.setScore(BigDecimal.ZERO);
                continue;
            }
            if (Objects.nonNull(scoreValueConf)) {
                if (scoreValueConf.submitWithWeight()) {
                    waitScore.setScore((scoreValueConf.getCustomFullScore().multiply(this.itemWeight.divide(new BigDecimal(100)))));
                    continue;
                }
            }
            waitScore.setScore(scoreValueConf.getCustomFullScore());
        }
    }


    public void builderItemWaitScoreV3(String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig,
                                     IndLevelGroup indLevelGroup,
                                     ScoreValueConf scoreValueConf) {
        if (CollUtil.isEmpty(this.waitScores)) {
            return;
        }
        if (Objects.nonNull(indLevelGroup)) {
            this.indLevelGroup = indLevelGroup;
        }
        boolean isEvalLevel = Objects.nonNull(this.indLevelGroup);
        boolean isPlusSubScoreType = isPlusSubType() || isPlusType() || isSubtractType();
        for (EvalScorerNodeKpiItem waitScore : this.waitScores) {
            waitScore.markSkip(defaultCustomScoreConfig, defaultPlusSubScoreConfig, isPlusSubScoreType);
            if (isPlusSubScoreType) {
                waitScore.setScore(BigDecimal.ZERO);
                continue;
            }
            if (isOneVoteVetoType()) {
                waitScore.setVetoFlag(Boolean.FALSE.toString());
            }
            ItemScoreValue scoreValue = JSONUtil.toBean(itemScoreValue, ItemScoreValue.class);
            if (isEvalLevel && !scoreValue.isSystemOption()) {
                if (isIndLevel()) {
                    waitScore.setScoreLevel(this.indLevelGroup.getLevels().get(0).getName());
                    continue;
                }
                waitScore.setScore(this.indLevelGroup.getLevels().get(0).getScore());
                continue;
            }
            //对公司 dingCorpId ： ='ding522a66d1de4e7aeaa39a90f97fcb1e09', //北京德皓国际会计师事务所 特殊处理，不管配置什么，自动提交都分都置为0
            if (Objects.nonNull(dingCorpId) && filterCorpIds().contains(dingCorpId)) {
                waitScore.setScore(BigDecimal.ZERO);
                continue;
            }
            if (Objects.nonNull(scoreValueConf)) {
                if (scoreValueConf.submitWithWeight()) {
                    waitScore.setScore((scoreValueConf.getCustomFullScore().multiply(this.itemWeight.divide(new BigDecimal(100)))));
                    continue;
                }
            }
            waitScore.setScore(scoreValueConf.getCustomFullScore());
        }
    }
    @JSONField(serialize = false)
    public boolean isBusinessItem() {
        return Objects.equals(this.itemType, "business-item");
    }

    public List<EvalScoreResult> listScoreRs(SubScoreNodeEnum node, String scorerId) {
        return waitScoresOld.stream().filter(rs -> SubScoreNodeEnum.fromStr(rs.getScorerType()) == node)
                .filter(rs -> StrUtil.equals(rs.getScorerId(), scorerId))
                .collect(Collectors.toList());
    }

    public void initOkrGoalId(Map<String, BusinessPlanItem> planItemMap) {
        BusinessPlanItem planItem = planItemMap.get(this.kpiItemId);
        if (Objects.nonNull(planItem)) {
            if (planItem.getInfoList().size() == 1) {
                this.okrGoalId = planItem.getInfoList().get(0).getObjectId();
                this.itemTargetValue = planItem.getInfoList().get(0).getTargetValue();
                this.itemFinishValue = planItem.getInfoList().get(0).getFinishValue();
            }
        }
    }


    public void refreshOkrGoalId(Map<String, BusinessPlanItem> planItemMap) {
        this.okrGoalId = null;
        this.itemTargetValue = null;
        this.itemFinishValue = null;
        BusinessPlanItem planItem = planItemMap.get(this.kpiItemId);
        if (Objects.nonNull(planItem)) {
            if (planItem.getInfoList().size() == 1) {
                this.okrGoalId = planItem.getInfoList().get(0).getObjectId();
                this.itemTargetValue = planItem.getInfoList().get(0).getTargetValue();
                this.itemFinishValue = planItem.getInfoList().get(0).getFinishValue();
            }
        }
    }

    public String getSuperiorScoreWay() {
        if (Objects.isNull(this.itemScoreRule)) {
            return null;
        }
        if (!this.itemScoreRule.getSuperRater().isOpen()) {
            return null;
        }
        return this.itemScoreRule.getSuperRater().getSuperiorScoreOrder();
    }

    public void accCategory(String categoryId, String categoryName) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
    }

    @JSONField(serialize = false)
    public void setAskItem(String taskUserId, String kpiTypeId, String askTempName, String askTempDesc, BigDecimal ask360EvalScore) {
        this.taskUserId = taskUserId;
        this.kpiTypeId = kpiTypeId;
        this.kpiItemId = kpiTypeId;
        this.kpiItemName = askTempName;
        this.itemRule = askTempDesc;
        this.score = ask360EvalScore;
        this.kpiTypeClassify = "ask360";
    }

    @JSONField(serialize = false)
    public boolean isAskType() {
        return Objects.equals(this.kpiTypeClassify, "ask360");
    }

    //当维度开启目标值时, 指标是否能开启目标值
    public int canOpenTargetOnTypeOpen() {
        if (Objects.isNull(itemType)) {
            return 0;
        }
        if (itemType.equals(KpiItemTypeEnum.MEASURABLE.getType())) {
            return 1;
        }
        if (itemType.equals(KpiItemTypeEnum.NON_MEASURABLE.getType())) {
            return Boolean.valueOf(showTargetValue) ? 1 : 0;
        }
        return 0;
    }

    public void initSignatureFlagIfOpt(boolean custom, RaterNodeConf s3SelfRater, MutualNodeConf s3PeerRater, MutualNodeConf s3SubRater, S3SuperRaterConf s3SuperRater, S3RaterBaseConf s3AppointRater) {
        if (CollUtil.isEmpty(this.waitScoresOld)) {
            return;
        }
        for (EvalScoreResult waitScore : this.waitScoresOld) {
            if (!custom) {
                waitScore.initSimpleSignatureFlag(s3SelfRater, s3PeerRater, s3SubRater, s3SuperRater, s3AppointRater);
                continue;
            }
            waitScore.installSignatureFlag(this.itemScoreRule);
        }
    }

    /**
     * 初始化指标自定义字段配置
     */
    public void initKpiItemUsedFields(List<KpiItemUsedField> itemUsedFields) {
        /**初始化指标自定义字段配置*/
        if (CollUtil.isEmpty(itemUsedFields)) {
            return;
        }

        List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
        itemUsedFields.forEach(item -> {
            ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
            BeanUtils.copyProperties(item, fieldValue);
            fieldValue.setFieldName(item.getName());
            fieldValue.setFieldValue(item.getValue());
            fieldValue.setFieldStatus(item.getStatus());
            fieldValue.setId(item.getFieldId());
            fieldValue.setIsReq(item.getReq());
            fieldValueList.add(fieldValue);
        });
        this.fieldValueList = fieldValueList;
    }

    /**
     * 根据指标配置，校验字段必填
     *
     * @param finishValues
     * @return
     */
    public boolean allItemsCheckPassedByKpiItem(List<FinishValue> finishValues) {
        if (otherReqField == null) {
            return true;
        }
        return checkRequiredField(finishValues, otherReqField.getInputFinishValue(), otherReqField::isFinishValueProvided) &&
                checkRequiredField(finishValues, otherReqField.getAttachment(), otherReqField::isAttachmentProvided) &&
                checkRequiredField(finishValues, otherReqField.getComment(), otherReqField::isCommentProvided);
    }

    /**
     * 根据指标配置，校验字段必填
     *
     * @param finishValues
     * @return
     */
    public boolean allItemsCheckPassedByKpiTypeReg(List<FinishValue> finishValues, KpiSyncReqConf reqConf) {
        if (Objects.isNull(reqConf)) {
            return true;
        }
        return checkRequiredField(finishValues, reqConf.getReqItems().getInputFinishValue(), reqConf.getReqItems()::isFinishValueProvided) &&
                checkRequiredField(finishValues, reqConf.getReqItems().getAttachment(), reqConf.getReqItems()::isAttachmentProvided) &&
                checkRequiredField(finishValues, reqConf.getReqItems().getComment(), reqConf.getReqItems()::isCommentProvided);
    }


    /**
     * 根据指标配置，校验字段必填
     *
     * @return boolean
     */
    public boolean checkPassedByKpiItemFinishValue() {
        if (otherReqField == null) {
            return true;
        }
        return checkFinishValueRequiredField(otherReqField.getInputFinishValue()) &&
                checkAttachmentRequiredField(otherReqField.getAttachment()) &&
                checkCommentRequiredField(otherReqField.getComment());
    }

    /**
     * 根据维度必填配置，校验字段必填
     *
     * @return boolean
     */
    public boolean checkPassedByTypeReq(KpiSyncReqConf kpiSyncReqConf) {
        if (kpiSyncReqConf == null || Objects.isNull(kpiSyncReqConf.getReqItems())) {
            return true;
        }
        return checkFinishValueRequiredField(kpiSyncReqConf.getReqItems().getInputFinishValue()) &&
                checkAttachmentRequiredField(kpiSyncReqConf.getReqItems().getAttachment()) &&
                checkCommentRequiredField(kpiSyncReqConf.getReqItems().getComment());
    }

    private boolean isFinishValueProvided() {
        return this.getItemFinishValue() != null ||
                StrUtil.isNotBlank(this.getWorkItemFinishValue()) ||
                StrUtil.isNotBlank(this.getItemFinishValueText());
    }

    public boolean isFinishValueFinishd() {
        return this.getItemFinishValue() != null ||
                StrUtil.isNotBlank(this.getWorkItemFinishValue()) ||
                StrUtil.isNotBlank(this.getItemFinishValueText()) || Objects.equals(this.finalSubmitFinishValue, 1);
    }

    private boolean checkFinishValueRequiredField(RequiredFieldItem requiredField) {
        if (Objects.isNull(requiredField)) {
            return true;
        }
        return requiredField.getIsReq() != 1 || isFinishValueProvided();
    }

    private boolean checkAttachmentRequiredField(RequiredFieldItem requiredField) {
        if (Objects.isNull(requiredField)) {
            return true;
        }
        return requiredField.getIsReq() != 1 || (StrUtil.isNotBlank(files) && !"[]".equals(files));
    }

    private boolean checkCommentRequiredField(RequiredFieldItem requiredField) {
        if (Objects.isNull(requiredField)) {
            return true;
        }
        return requiredField.getIsReq() != 1 || StrUtil.equals("text", this.inputFormat) || StrUtil.isNotEmpty(finishValueComment);
    }

    private boolean checkRequiredField(List<FinishValue> finishValues, RequiredFieldItem requiredField, Predicate<FinishValue> checkFunction) {
        if (Objects.isNull(requiredField)) {
            return true;
        }
        return requiredField.getIsReq() != 1 || StrUtil.equals("text", this.inputFormat) || finishValues.stream().allMatch(checkFunction);
    }

    public boolean finishValueReqValidate(List<FinishValue> finishValues) {
        RequiredFieldItem requiredInputFinishValue = otherReqField.getInputFinishValue();
        if (requiredInputFinishValue.getIsReq() == 1) { // 完成值必填
            if (!finishValues.stream().allMatch(item -> otherReqField.isFinishValueProvided(item))) {
                return false;
            }
        }
        return true;
    }

    public boolean attachmentReqValidate(List<FinishValue> finishValues) {
        RequiredFieldItem requiredInputFinishValue = otherReqField.getInputFinishValue();
        if (requiredInputFinishValue.getIsReq() == 1) { // 完成值必填
            if (!finishValues.stream().allMatch(item -> otherReqField.isAttachmentProvided(item))) {
                return false;
            }
        }
        return true;
    }

    public boolean commentReqValidate(List<FinishValue> finishValues) {
        RequiredFieldItem requiredInputFinishValue = otherReqField.getInputFinishValue();
        if (requiredInputFinishValue.getIsReq() == 1) { // 完成值必填
            if (!finishValues.stream().allMatch(item -> otherReqField.isCommentProvided(item))) {
                return false;
            }
        }
        return true;
    }


    @JSONField
    public boolean isInputFinishChanged(FinishValue finishValue) {
        if (Objects.isNull(finishValue)) {
            return false;
        }
        if (Objects.nonNull(finishValue.getItemFinishValue())
                && (Objects.isNull(this.itemFinishValue) ? BigDecimal.ZERO : this.itemFinishValue).compareTo(finishValue.getItemFinishValue()) != 0) {
            return true;
        }
        if (!Objects.equals(this.itemFinishValueText, finishValue.getItemFinishValueText())) {
            return true;
        }
        if (!Objects.equals(this.workItemFinishValue, finishValue.getWorkItemFinishValue())) {
            return true;
        }
        if (CollUtil.isEmpty(finishValue.getFiles())) {
            log.info(" 完成值附件无改变！！！");
            return false;
        }
        List<PerfEvaluateTaskFile> taskFiles = JSONUtil.toList(this.files, PerfEvaluateTaskFile.class);
        if (CollUtil.isNotEmpty(taskFiles) && finishValue.getFiles().size() != taskFiles.size()) {
            return true;
        }
        Map<String, PerfEvaluateTaskFile> taskFileMap = CollUtil.toMap(taskFiles, new HashMap<>(), PerfEvaluateTaskFile::getFileUrl);
        for (FinishValue.ItemFile file : finishValue.getFiles()) {
            if (MapUtils.isNotEmpty(taskFileMap) && Objects.isNull(taskFileMap.get(file.getUrl()))) {
                return true;
            }
        }
        return false;
    }


    public String getPercent(BigDecimal finishValue) {
        if (Objects.isNull(this.itemTargetValue)) {
            return "-";
        }
        if (Objects.isNull(finishValue)) {
            return "0";
        }
        //非量化指标，录多少是多少
        if ("non-measurable".equals(this.itemType)) {
            return finishValue.toString();
        }
        //为null时表示历史数据，历史数据只有正向
        boolean forward = (Objects.isNull(finishValueType) || finishValueType == 1);
        if (this.itemTargetValue.compareTo(BigDecimal.ZERO) == 0) {
            if (finishValue.compareTo(BigDecimal.ZERO) < 0) {
                return forward ? "0" : "100";
            } else if (finishValue.compareTo(BigDecimal.ZERO) == 0) {
                return "100";
            }
            return forward ? "100" : "0";
        }
        String progress = null;
        //判断是否需要四舍五入
        boolean isHalfUp = (this.itemTargetValue.subtract(finishValue)).scale() > 0;
        //是否需要四舍五入计算
        BigDecimal compute = isHalfUp
                ? (new BigDecimal(1).add((this.itemTargetValue.subtract(finishValue)).divide(this.itemTargetValue, RoundingMode.HALF_UP)))
                : (new BigDecimal(1).add((this.itemTargetValue.subtract(finishValue)).divide(this.itemTargetValue)));
        if (this.itemTargetValue.compareTo(BigDecimal.ZERO) < 0) {   //目标值为负数
            progress = forward
                    ? compute.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString()
                    : finishValue.divide(this.itemTargetValue, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        } else
            progress = forward
                    ? finishValue.divide(this.itemTargetValue, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP).toString()
                    : compute.multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        return progress;
    }

    public void parseScoreRater(EvalUser user, EmpId opEmpId) {
        this.itemScoreRule.getPeerRater().parseMutualNodeConfRaters(user, opEmpId);
        this.itemScoreRule.getSubRater().parseMutualNodeConfRaters(user, opEmpId);
        this.itemScoreRule.getSuperRater().skpiRepeatSuperScore(user, opEmpId);
        this.itemScoreRule.getAppointRater().parseNoSkipRaters(user, opEmpId);
    }


    public void parseScoreChangeRater(String empId, String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        //解析定向评分人
        if (isDirectional()) {
            dmSvc.parseStaffConfTypeScorer(empId, evalOrgId, orgId, this.scorerObjId, infos);
        }
        if (Objects.isNull(this.itemScoreRule)) {
            return;
        }
        this.itemScoreRule.getPeerRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        this.itemScoreRule.getSubRater().parseMutualNodeConfChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
        this.itemScoreRule.getSuperRater().skpiRepeatChangeSuperScore(empId, evalOrgId, orgId, infos, dmSvc);
        this.itemScoreRule.getAppointRater().parseNoSkipChangeRaters(empId, evalOrgId, orgId, infos, dmSvc);
    }

    public void parseInputFinishValueRaters(String evalOrgId, String orgId, Set<ChangeInfo> infos, EvalChangeDmSvc dmSvc) {
        //解析完成值录入
        if (this.isSpecifyUser() || this.ifSelfInput()) {
            //1、先判断缓存信息中是否存在，若有则直接取，若没有则调用解析逻辑
            List<Rater> raters = dmSvc.parseUserOrEmpRaters(infos, this.inputEmps, this.ifSelfInput() ? "taskEmp" : "user");
            this.setResultInputEmpId(StrUtil.join(",", CollUtil.map(raters, p -> p.getEmpId(), true)));
            return;
        }
        List<Rater> oldRaters = CollUtil.isEmpty(this.inputEmps) ? new ArrayList<>() : this.inputEmps.stream().map(kpiEmp -> {
            return new Rater(kpiEmp.getEmpId(), kpiEmp.getEmpName());
        }).collect(Collectors.toList());
        //角色
        if (this.isSpecifyRole()) {
            List<Rater> newRaters = new ArrayList<>();
            List<ObjItem> parsedRaters = new ArrayList<>();
            for (ObjItem objItem : this.getInputRole()) {
                List<Rater> raters = dmSvc.parseBaseChangeRaters(this.empId, evalOrgId, orgId, infos, oldRaters, this.getResultInputType(), objItem.getRoleId());
                for (Rater rater : raters) {
                    ObjItem o = new ObjItem(rater.getEmpId(), rater.getEmpName(), objItem.getRoleId(), objItem.getRoleName());
                    parsedRaters.add(o);
                }
                newRaters.addAll(raters);
            }
            this.setInputRole(CollUtil.distinct(parsedRaters));
            this.setResultInputEmpId(StrUtil.join(",", CollUtil.map(parsedRaters, p -> p.getObjId(), true)));
            return;
        }
        if (this.isSpecifyManager()) {
            //1、先判断缓存信息中是否存在，若有则直接取，若没有则调用解析逻辑
            List<Rater> raters = dmSvc.parseBaseChangeRaters(this.empId, evalOrgId, orgId, infos, oldRaters, this.getResultInputType(), this.getManagerLevel());
            this.setResultInputEmpId(StrUtil.join(",", CollUtil.map(raters, p -> p.getEmpId(), true)));
        }
    }


    public List<Rater> inputRaters() {
        List<Rater> raters = new ArrayList<>();
        for (String inputRaterId : getInputRaterIds()) {
            raters.add(new Rater(inputRaterId));
        }
        return raters;
    }


    public void changeInput(EvalKpi mapGet) {
        if (Objects.isNull(mapGet)) {
            return;
        }
        this.inputRole = mapGet.getInputRole();
        this.resultInputEmpId = mapGet.getResultInputEmpId();
    }

    public void changeInputAudit(EvalKpi mapGet) {
        if (Objects.isNull(mapGet)) {
            return;
        }
        this.finishValueAudit = mapGet.getFinishValueAudit();
    }

    public void changeItemScore(EvalKpi mapGet) {
        this.scorerObjId = mapGet.getScorerObjId();
        this.itemScoreRule = mapGet.getItemScoreRule();
    }

    public boolean hasChangePlanItemValue() {
        if (!this.isAutoItem()) {
            return true;
        }
        if (this.isAutoItem() && TalentStatus.valueOf(this.taskStatus).before(TalentStatus.SCORING)) {
            return true;
        }
        return false;
    }

    public void initFinishValueSource() {
        List<ExtDataItemFieldCorr> fieldCorr = this.getItemFieldCorr();
        if (CollUtil.isNotEmpty(fieldCorr)) {
            List<ExtDataItemFieldCorr> finishValue = fieldCorr.stream()
                    .filter(s -> StrUtil.equals("finishValue", s.getKpiItemFieldId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(finishValue)) {
                this.setFinishValueSource(2);
            }
        }
    }

    public void buildInputRecord(List<ItemDynamicLog> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        ListWrap<ItemDynamicLog> itemGroups = new ListWrap<>(list).groupBy(ItemDynamicLog::getKpiItemId);
        List<ItemDynamicLog> logs = itemGroups.groupGet(this.getKpiItemId());
        if (CollUtil.isNotEmpty(logs)) {
            this.inputChangeRecord = logs;
            this.finishValueComment = this.inputChangeRecord.get(0).getComment();
        }
    }

    public void buildCacheFinishValue(InputFinishValCache valCache, InputFinishValCache importCache) {
        this.finishValCache = valCache;
        if (!Objects.isNull(valCache) && !Objects.isNull(importCache)) {
            this.itemFinishValue = importCache.notNullItemFinishValue(valCache.getItemFinishValue());
            this.itemFinishValueText = importCache.notNullitemFinishValueText(valCache.getItemFinishValueText());
            this.workItemFinishValue = importCache.notNullWorkItemFinishValue(valCache.getWorkItemFinishValue());
            this.finishValueComment = importCache.notNullComment(valCache.getFinishValueComment());
            this.files = JSONUtil.toJsonStr(importCache.notNullFiles(valCache.getFiles()));
        } else if (!Objects.isNull(importCache)) {
            this.itemFinishValue = importCache.getItemFinishValue();
            this.itemFinishValueText = importCache.getItemFinishValueText();
            this.workItemFinishValue = importCache.getWorkItemFinishValue();
            this.finishValueComment = importCache.getFinishValueComment();
            this.files = JSONUtil.toJsonStr(importCache.getFiles());
        } else if (!Objects.isNull(valCache)) {
            this.itemFinishValue = valCache.getItemFinishValue();
            this.itemFinishValueText = valCache.getItemFinishValueText();
            this.workItemFinishValue = valCache.getWorkItemFinishValue();
            this.finishValueComment = valCache.getFinishValueComment();
            this.files = JSONUtil.toJsonStr(valCache.getFiles());
        }
    }

    public void buildImportCacheFinishValue(ImportFinishValCache valCache) {
        this.finishValCache = new ToDataBuilder<>(valCache, InputFinishValCache.class).data();
        if (Objects.isNull(valCache)) {
            return;
        }
        this.itemFinishValue = valCache.getItemFinishValue();
        this.itemFinishValueText = valCache.getItemFinishValueText();
        this.workItemFinishValue = valCache.getWorkItemFinishValue();
        this.finishValueComment = valCache.getFinishValueComment();
        this.files = JSONUtil.toJsonStr(valCache.getFiles());
    }

    public String getFinishVal() {
        return Objects.nonNull(this.itemFinishValue) ? StrUtil.toString(this.itemFinishValue)
                : (StrUtil.isNotBlank(this.itemFinishValueText) ? this.itemFinishValueText : this.workItemFinishValue);
    }

    public String getItemIdJoinName() {
        StringBuilder sb = new StringBuilder();
        sb.append(this.kpiItemName).append(System.lineSeparator()).append("(指标ID:").append(this.kpiItemId).append(")");
        return sb.toString();
    }
}