package com.polaris.kpi.eval.domain.migration.repository;

import com.polaris.kpi.eval.domain.task.entity.MigrationProgress;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据迁移进度仓储接口
 * 负责迁移进度的持久化操作
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
public interface MigrationProgressRepository {

    /**
     * 保存迁移进度
     * 
     * @param progress 迁移进度
     * @return 保存后的进度对象
     */
    MigrationProgress save(MigrationProgress progress);

    /**
     * 根据会话ID查找迁移进度
     * 
     * @param sessionId 会话ID
     * @return 迁移进度，如果不存在返回null
     */
    MigrationProgress findBySessionId(String sessionId);

    /**
     * 根据会话ID删除迁移进度
     * 
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    boolean deleteBySessionId(String sessionId);

    /**
     * 查找所有活跃的迁移任务
     * 活跃任务包括：PENDING, RUNNING, PAUSED 状态的任务
     * 
     * @return 活跃任务列表
     */
    List<MigrationProgress> findActiveMigrations();

    /**
     * 查找指定状态的迁移任务
     * 
     * @param status 迁移状态
     * @return 迁移任务列表
     */
    List<MigrationProgress> findByStatus(MigrationProgress.MigrationStatus status);

    /**
     * 查找指定类型的迁移任务
     * 
     * @param migrationType 迁移类型
     * @return 迁移任务列表
     */
    List<MigrationProgress> findByMigrationType(String migrationType);

    /**
     * 查找指定时间范围内的迁移任务
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 迁移任务列表
     */
    List<MigrationProgress> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除指定时间之前已完成的迁移记录
     * 
     * @param cutoffTime 截止时间
     * @return 删除的记录数
     */
    int deleteCompletedBefore(LocalDateTime cutoffTime);

    /**
     * 统计各状态的迁移任务数量
     * 
     * @return 状态统计Map，key为状态，value为数量
     */
    java.util.Map<MigrationProgress.MigrationStatus, Long> countByStatus();

    /**
     * 查找长时间运行的迁移任务
     * 
     * @param hours 运行小时数阈值
     * @return 长时间运行的任务列表
     */
    List<MigrationProgress> findLongRunningMigrations(int hours);

    /**
     * 查找失败的迁移任务
     * 
     * @param limit 限制数量
     * @return 失败任务列表
     */
    List<MigrationProgress> findFailedMigrations(int limit);

    /**
     * 更新迁移进度的状态
     * 
     * @param sessionId 会话ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateStatus(String sessionId, MigrationProgress.MigrationStatus status);

    /**
     * 更新迁移进度的错误信息
     * 
     * @param sessionId 会话ID
     * @param errorMessage 错误信息
     * @return 是否更新成功
     */
    boolean updateErrorMessage(String sessionId, String errorMessage);

    /**
     * 批量更新迁移进度
     * 
     * @param progressList 进度列表
     * @return 更新成功的数量
     */
    int batchUpdate(List<MigrationProgress> progressList);

    /**
     * 检查会话ID是否存在
     * 
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean existsBySessionId(String sessionId);

    /**
     * 获取迁移进度总数
     * 
     * @return 总数
     */
    long count();

    /**
     * 获取指定类型的迁移进度总数
     * 
     * @param migrationType 迁移类型
     * @return 总数
     */
    long countByMigrationType(String migrationType);

    /**
     * 查找最近的迁移任务
     * 
     * @param limit 限制数量
     * @return 最近的任务列表
     */
    List<MigrationProgress> findRecentMigrations(int limit);

    /**
     * 查找指定公司的迁移任务
     * 
     * @param companyId 公司ID
     * @return 迁移任务列表
     */
    List<MigrationProgress> findByCompanyId(String companyId);

    /**
     * 保存进度到文件系统（备份）
     * 
     * @param progress 迁移进度
     * @param filePath 文件路径
     * @return 是否保存成功
     */
    boolean saveToFile(MigrationProgress progress, String filePath);

    /**
     * 从文件系统加载进度（恢复）
     * 
     * @param filePath 文件路径
     * @return 迁移进度，如果加载失败返回null
     */
    MigrationProgress loadFromFile(String filePath);

    /**
     * 获取迁移统计信息
     * 
     * @return 统计信息
     */
    MigrationStatistics getStatistics();

    /**
     * 迁移统计信息内部类
     */
    class MigrationStatistics {
        private long totalMigrations;
        private long activeMigrations;
        private long completedMigrations;
        private long failedMigrations;
        private double averageSuccessRate;
        private long totalProcessedRecords;
        private LocalDateTime oldestActiveMigration;
        private LocalDateTime newestMigration;

        // Getters and Setters
        public long getTotalMigrations() { return totalMigrations; }
        public void setTotalMigrations(long totalMigrations) { this.totalMigrations = totalMigrations; }

        public long getActiveMigrations() { return activeMigrations; }
        public void setActiveMigrations(long activeMigrations) { this.activeMigrations = activeMigrations; }

        public long getCompletedMigrations() { return completedMigrations; }
        public void setCompletedMigrations(long completedMigrations) { this.completedMigrations = completedMigrations; }

        public long getFailedMigrations() { return failedMigrations; }
        public void setFailedMigrations(long failedMigrations) { this.failedMigrations = failedMigrations; }

        public double getAverageSuccessRate() { return averageSuccessRate; }
        public void setAverageSuccessRate(double averageSuccessRate) { this.averageSuccessRate = averageSuccessRate; }

        public long getTotalProcessedRecords() { return totalProcessedRecords; }
        public void setTotalProcessedRecords(long totalProcessedRecords) { this.totalProcessedRecords = totalProcessedRecords; }

        public LocalDateTime getOldestActiveMigration() { return oldestActiveMigration; }
        public void setOldestActiveMigration(LocalDateTime oldestActiveMigration) { this.oldestActiveMigration = oldestActiveMigration; }

        public LocalDateTime getNewestMigration() { return newestMigration; }
        public void setNewestMigration(LocalDateTime newestMigration) { this.newestMigration = newestMigration; }

        @Override
        public String toString() {
            return String.format("MigrationStatistics{total=%d, active=%d, completed=%d, failed=%d, successRate=%.2f%%, records=%d}",
                    totalMigrations, activeMigrations, completedMigrations, failedMigrations, 
                    averageSuccessRate, totalProcessedRecords);
        }
    }
}
