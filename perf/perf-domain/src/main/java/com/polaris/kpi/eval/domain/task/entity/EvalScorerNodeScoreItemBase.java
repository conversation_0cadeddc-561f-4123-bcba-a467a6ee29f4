package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.ScoreAttUrl;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.common.DelableDomain;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.INodeConf;
import com.polaris.kpi.eval.domain.task.entity.grade.Level;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonAryColumn;
import org.apache.ibatis.annotations.JsonColumn;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Setter
@Getter
@NoArgsConstructor
public class EvalScorerNodeScoreItemBase extends DelableDomain {
    protected String id;//
    @JSONField(serialize = false)
    protected TenantId companyId;//公司id
    protected String taskId;//考核任务id
    protected String taskUserId;//员工任务id
    protected String empId;//被考核人id
    protected String scorerScoreNodeId;//关联empEvalScorerNode.id
    protected String scorerType;//环节
    protected String scorerId;//评分人id
    protected String scorerName;//评分人姓名
    protected String scorerAvatar;//评分人头像
    protected Integer approvalOrder;//审批顺序（从1开始递增）
    protected String multiType;//多个审核人时会签还是或签
    protected Integer status;//0-待分发,1-待提交,2-已提交,3- 驳回
    protected BigDecimal scoreWeight;//权重
    protected BigDecimal score;//评分分数
    protected BigDecimal finalScore;//指标维度权重得分(scoreWeightScore*指标权重,*维度权重)  == 原来EvalScoreResult de  finalScore
    protected BigDecimal finalWeightScore;//最终加权后的分数
    protected String scoreComment;//评分评语
    @JsonAryColumn(ScoreAttUrl.class)
    protected List<ScoreAttUrl> scoreAttUrl;//评分附件url
    protected String scoreLevel;//打分人提交的绩效等级
    @JsonColumn
    protected Level scoreOption;        //评分的指标选项
    @JSONField(serialize = false)
    private boolean signatureFlag = false;      //签名开关

    private boolean maySkip = false;    //是否可以跳过

    protected String auditStatus;//审核状态 仅用于评价详情页面显示，不做逻辑流程处理
    @JSONField(serialize = false)
    protected boolean canShowNode;//仅用于是否用作显示，逻辑字段 true-需显示，false-不显示
    @JSONField(serialize = false)
    protected boolean canCompute;//仅用于是否可以参与计算，逻辑字段



    public EvalScorerNodeScoreItemBase(String companyId, String opEmpId, String taskId, String taskUserId, String empId,
                                       Rater rater, INodeConf iNodeConf) {
        this.companyId = new TenantId(companyId);
        this.taskId = taskId;
        this.taskUserId = taskUserId;
        this.empId = empId;
        this.scoreWeight = rater.getWeight();
        this.scorerId = rater.getEmpId();
        this.scorerName = rater.getEmpName();
        this.scorerAvatar = rater.getAvatar();
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.approvalOrder = iNodeConf.order();
        this.multiType = iNodeConf.multiType();
        this.scorerType = iNodeConf.node().getScene();
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.version = 0;
        this.status = 0;
    }
    /**
     * @param defaultCustomScoreConfig  普通指标默认评分分值
     * @param defaultPlusSubScoreConfig 加减分默认评分分值
     * @param isPlusSubScoreType        是否加分/减分/加减分
     * @return
     */
    public void markSkip(List<String> defaultCustomScoreConfig,
                         List<String> defaultPlusSubScoreConfig,
                         boolean isPlusSubScoreType) {

        //普通指标
        if (!isPlusSubScoreType) {
            if (defaultCustomScoreConfig.contains(this.scorerType)) {
                this.maySkip = true;
                return;
            }
        }
        //加减分指标
        if (defaultPlusSubScoreConfig.contains(this.scorerType)) {
            this.maySkip = true;
        }

    }
    @JSONField(serialize = false)
    public boolean isOrVisa() {
        return Objects.equals(this.multiType, "or");
    }
    @JSONField(serialize = false)
    public boolean isScored(){
        return null != this.score|| StrUtil.isNotEmpty(this.scoreLevel);
    }
    public boolean isShow() {
        if ("pass".equals(this.auditStatus)) {
            return true;
        }
        return "skip".equals(this.auditStatus) && this.canShowNode;
    }
    public String asScorerIdAndOrderKey() {
        return String.format("%s&%s&%s", scorerId,scorerType, approvalOrder);
    }
 
    public boolean matchBelong(String node, EmpId opEmpId) {
        return scorerType.equals(node) && scorerId.equals(opEmpId.getId());
    }
    public void installSignatureFlag(EvalItemScoreRule typeRule) {
        if (Objects.isNull(typeRule)) {
            return;
        }
        if (SubScoreNodeEnum.isSelfScore(scorerType)) {
            this.setSignatureFlag(typeRule.getSelfRater().isSignatureFlag());
        }
        if (isSupper()) {
            this.setSignatureFlag(typeRule.getSuperRater().isSignatureFlag());
        }
        if (SubScoreNodeEnum.isSubNode(scorerType)) {
            this.setSignatureFlag(typeRule.getSubRater().isSignatureFlag());
        }
        if (SubScoreNodeEnum.isAppointScore(scorerType)) {
            this.setSignatureFlag(typeRule.getAppointRater().isSignatureFlag());
        }
        if (SubScoreNodeEnum.isPeerNode(scorerType)) {
            this.setSignatureFlag(typeRule.getPeerRater().isSignatureFlag());
        }
    }

    public void accpItemScore(EvalScoreResult scoreResult) {
        //历史数据，接收评分数据
        this.score = scoreResult.getScore();
        this.scoreComment = scoreResult.getScoreComment();
        this.scoreAttUrl = scoreResult.getScoreAttUrl();
        this.scoreLevel = scoreResult.getScoreLevel();
        this.scoreOption = scoreResult.getScoreOption();
    }

    public void accScorerInfo(String scorerId,String scorerName,String scorerAvatar) {
        this.scorerId = scorerId;
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
    }
    public void accItemScorerTypeAndOrder(String scorerId,
                                          String scorerType,
                                          Integer approvalOrder,
                                          Integer status,
                                          String multiType,
                                          String transferType,
                                          boolean canCompute,
                                          boolean canShowNode) {
        this.scorerId = scorerId;
        this.scorerType = scorerType;
        this.approvalOrder = approvalOrder;
        this.status = status;
        this.multiType = multiType;
        this.canCompute = canCompute;
        this.canShowNode = canShowNode;
        if (isPassed() && "skip".equals(transferType)) {
            this.auditStatus = "skip";
            return;
        }
        if (isPassed()) {
            this.auditStatus = "pass";
        }
    }

    @JSONField(serialize = false)
    public boolean isOrMode() {
        return "or".equals(multiType);
    }
    @JSONField(serialize = false)
    public boolean isWaitDispatch() {
        return 0 == this.status;
    }

    public String orderStr(){
        return approvalOrder + "";
    }

    public boolean isSupper() {
        return SubScoreNodeEnum.isSuperioNode(scorerType);
    }
    public boolean isPassed() {
        return this.status == 2;
    }

    public boolean isSkip() {
        return "skip".equals(this.auditStatus);
    }
    public void upStatus(Integer status,Date handlderTime) {
        this.status = status;
        if (Objects.nonNull(handlderTime)) {
            this.updatedTime = handlderTime; //更新处理时间
        }
    }

    public void accpNewScorerWeight(BigDecimal newScoreWeight) {
        this.scoreWeight = newScoreWeight;
    }

    //同一结点多人情况下的, 评分人之间的权重
    public BigDecimal percentRaterWeight() {
        return scoreWeight == null ? BigDecimal.ONE : scoreWeight.divide(Pecent.ONE_HUNDRED);
    }

    /**
     * @description: 类*指*人
     * @author: suxiaoqiu
     * @date: 2022/5/16 14:32
     * @param: [typePecWgt, itemPecWgt]
     * @return: void
     **/
    public void computeScoreWeightScore(BigDecimal typePecWgt, BigDecimal itemPecWgt, BigDecimal nodeWeight) {
        if (score == null) {
            return;
        }
        BigDecimal raterWeight = percentRaterWeight();
        BigDecimal initScore = score;
        this.finalScore = initScore.multiply(typePecWgt).multiply(itemPecWgt).multiply(raterWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
        this.finalWeightScore = this.finalScore.multiply(nodeWeight).setScale(5, RoundingMode.DOWN).setScale(4, RoundingMode.HALF_UP);
    }
}
