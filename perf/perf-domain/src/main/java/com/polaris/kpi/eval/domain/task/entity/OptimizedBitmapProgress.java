package com.polaris.kpi.eval.domain.migration.entity;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.BitSet;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 优化的位图进度管理器
 * 解决断点恢复和内存膨胀问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@Slf4j
public class OptimizedBitmapProgress implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 迁移类型
     */
    private String migrationType;
    
    /**
     * 位图存储路径
     */
    private String bitmapFilePath;
    
    /**
     * 进度元数据存储路径
     */
    private String metadataFilePath;
    
    // ============ 断点恢复信息 ============
    /**
     * 当前处理位置 - 公司页码
     */
    private volatile int currentCompanyPage = 1;
    
    /**
     * 当前处理位置 - 公司ID
     */
    private volatile String currentCompanyId;
    
    /**
     * 当前处理位置 - 用户页码
     */
    private volatile int currentUserPage = 1;
    
    /**
     * 最后处理的记录索引
     */
    private volatile long lastProcessedIndex = -1;
    
    // ============ 统计信息 ============
    private final AtomicLong totalRecords = new AtomicLong(0);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);

    /**
     * 失败记录详情（用户ID -> 失败信息）
     */
    private final ConcurrentHashMap<String, FailureRecord> failureRecords = new ConcurrentHashMap<>();

    /**
     * 失败记录索引映射（全局索引 -> 用户ID）
     */
    private final ConcurrentHashMap<Long, String> failureIndexMapping = new ConcurrentHashMap<>();

    /**
     * 状态变化记录（用户ID -> 状态变化信息）
     */
    private final ConcurrentHashMap<String, StatusChangeRecord> statusChangeRecords = new ConcurrentHashMap<>();

    /**
     * 增量检测配置
     */
    private boolean enableIncrementalDetection = false;
    private LocalDateTime lastIncrementalCheckTime;
    private long incrementalCheckIntervalMinutes = 30; // 默认30分钟检查一次

    /**
     * 任务分离配置
     */
    private boolean enableTaskSeparation = false;
    private String relatedTaskSessionId; // 关联任务的会话ID（FINISHED任务关联NO_FINISHED任务）
    private TaskType taskType = TaskType.MIXED; // 任务类型

    /**
     * 动态扩展配置
     */
    private boolean enableDynamicExpansion = false; // 是否启用动态扩展
    private final ConcurrentHashMap<String, DynamicUserRecord> dynamicUsers = new ConcurrentHashMap<>(); // 动态新增的用户
    private LocalDateTime lastDynamicCheckTime; // 最后一次动态检查时间
    private long dynamicCheckIntervalMinutes = 15; // 动态检查间隔（分钟）
    
    // ============ 状态管理 ============
    private volatile MigrationStatus status = MigrationStatus.PENDING;
    private volatile LocalDateTime startTime;
    private volatile LocalDateTime lastUpdateTime;
    private volatile LocalDateTime lastSaveTime;
    
    // ============ 内存中的位图（分段加载） ============
    private transient BitSet currentSegmentBitmap;
    private transient long currentSegmentStartIndex = 0;
    private transient int segmentSize = 100000; // 每段10万条记录
    
    public enum MigrationStatus {
        PENDING, RUNNING, PAUSED, COMPLETED, FAILED
    }

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        MIXED("混合任务"),           // 传统的混合处理方式
        FINISHED_ONLY("仅已完成"),   // 仅处理已完成状态的用户
        NO_FINISHED_ONLY("仅未完成"), // 仅处理未完成状态的用户
        INCREMENTAL("增量处理");     // 增量处理任务

        private final String description;

        TaskType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 失败记录信息
     */
    public static class FailureRecord implements Serializable {
        private static final long serialVersionUID = 1L;

        private String userId;
        private String companyId;
        private long globalIndex;
        private String errorMessage;
        private String errorType;
        private LocalDateTime failureTime;
        private int retryCount = 0;
        private LocalDateTime lastRetryTime;

        public FailureRecord() {}

        public FailureRecord(String userId, String companyId, long globalIndex, String errorMessage, String errorType) {
            this.userId = userId;
            this.companyId = companyId;
            this.globalIndex = globalIndex;
            this.errorMessage = errorMessage;
            this.errorType = errorType;
            this.failureTime = LocalDateTime.now();
        }

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }

        public long getGlobalIndex() { return globalIndex; }
        public void setGlobalIndex(long globalIndex) { this.globalIndex = globalIndex; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getErrorType() { return errorType; }
        public void setErrorType(String errorType) { this.errorType = errorType; }

        public LocalDateTime getFailureTime() { return failureTime; }
        public void setFailureTime(LocalDateTime failureTime) { this.failureTime = failureTime; }

        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }

        public LocalDateTime getLastRetryTime() { return lastRetryTime; }
        public void setLastRetryTime(LocalDateTime lastRetryTime) { this.lastRetryTime = lastRetryTime; }

        public void incrementRetryCount() {
            this.retryCount++;
            this.lastRetryTime = LocalDateTime.now();
        }

        @Override
        public String toString() {
            return String.format("FailureRecord{userId='%s', companyId='%s', globalIndex=%d, errorType='%s', retryCount=%d}",
                    userId, companyId, globalIndex, errorType, retryCount);
        }
    }

    /**
     * 状态变化记录信息
     */
    public static class StatusChangeRecord implements Serializable {
        private static final long serialVersionUID = 1L;

        private String userId;
        private String companyId;
        private String originalStatus;    // 原始状态（FINISHED/NO_FINISHED）
        private String currentStatus;     // 当前状态
        private LocalDateTime changeDetectedTime;
        private LocalDateTime originalProcessTime;
        private boolean needsReprocessing = true;
        private int reprocessCount = 0;

        public StatusChangeRecord() {}

        public StatusChangeRecord(String userId, String companyId, String originalStatus, String currentStatus) {
            this.userId = userId;
            this.companyId = companyId;
            this.originalStatus = originalStatus;
            this.currentStatus = currentStatus;
            this.changeDetectedTime = LocalDateTime.now();
        }

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }

        public String getOriginalStatus() { return originalStatus; }
        public void setOriginalStatus(String originalStatus) { this.originalStatus = originalStatus; }

        public String getCurrentStatus() { return currentStatus; }
        public void setCurrentStatus(String currentStatus) { this.currentStatus = currentStatus; }

        public LocalDateTime getChangeDetectedTime() { return changeDetectedTime; }
        public void setChangeDetectedTime(LocalDateTime changeDetectedTime) { this.changeDetectedTime = changeDetectedTime; }

        public LocalDateTime getOriginalProcessTime() { return originalProcessTime; }
        public void setOriginalProcessTime(LocalDateTime originalProcessTime) { this.originalProcessTime = originalProcessTime; }

        public boolean isNeedsReprocessing() { return needsReprocessing; }
        public void setNeedsReprocessing(boolean needsReprocessing) { this.needsReprocessing = needsReprocessing; }

        public int getReprocessCount() { return reprocessCount; }
        public void setReprocessCount(int reprocessCount) { this.reprocessCount = reprocessCount; }

        public void incrementReprocessCount() {
            this.reprocessCount++;
        }

        public void markAsReprocessed() {
            this.needsReprocessing = false;
        }

        @Override
        public String toString() {
            return String.format("StatusChangeRecord{userId='%s', companyId='%s', %s->%s, detected=%s, needsReprocessing=%s}",
                    userId, companyId, originalStatus, currentStatus, changeDetectedTime, needsReprocessing);
        }
    }

    /**
     * 动态新增用户记录
     */
    public static class DynamicUserRecord implements Serializable {
        private static final long serialVersionUID = 1L;

        private String userId;
        private String companyId;
        private String userStatus;           // 用户状态（FINISHED/NO_FINISHED）
        private LocalDateTime addedTime;     // 添加时间
        private LocalDateTime detectedTime;  // 检测到的时间
        private boolean needsProcessing = true; // 是否需要处理
        private boolean processed = false;   // 是否已处理
        private LocalDateTime processedTime; // 处理时间
        private String addedBy;             // 添加来源（MANUAL/AUTO_DETECTION/BUSINESS_EVENT）
        private long globalIndex = -1;      // 全局索引（分配后设置）

        public DynamicUserRecord() {}

        public DynamicUserRecord(String userId, String companyId, String userStatus, String addedBy) {
            this.userId = userId;
            this.companyId = companyId;
            this.userStatus = userStatus;
            this.addedBy = addedBy;
            this.addedTime = LocalDateTime.now();
            this.detectedTime = LocalDateTime.now();
        }

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }

        public String getUserStatus() { return userStatus; }
        public void setUserStatus(String userStatus) { this.userStatus = userStatus; }

        public LocalDateTime getAddedTime() { return addedTime; }
        public void setAddedTime(LocalDateTime addedTime) { this.addedTime = addedTime; }

        public LocalDateTime getDetectedTime() { return detectedTime; }
        public void setDetectedTime(LocalDateTime detectedTime) { this.detectedTime = detectedTime; }

        public boolean isNeedsProcessing() { return needsProcessing; }
        public void setNeedsProcessing(boolean needsProcessing) { this.needsProcessing = needsProcessing; }

        public boolean isProcessed() { return processed; }
        public void setProcessed(boolean processed) { this.processed = processed; }

        public LocalDateTime getProcessedTime() { return processedTime; }
        public void setProcessedTime(LocalDateTime processedTime) { this.processedTime = processedTime; }

        public String getAddedBy() { return addedBy; }
        public void setAddedBy(String addedBy) { this.addedBy = addedBy; }

        public long getGlobalIndex() { return globalIndex; }
        public void setGlobalIndex(long globalIndex) { this.globalIndex = globalIndex; }

        public void markAsProcessed() {
            this.processed = true;
            this.needsProcessing = false;
            this.processedTime = LocalDateTime.now();
        }

        @Override
        public String toString() {
            return String.format("DynamicUserRecord{userId='%s', companyId='%s', status='%s', addedBy='%s', processed=%s}",
                    userId, companyId, userStatus, addedBy, processed);
        }
    }
    
    public OptimizedBitmapProgress(String sessionId, String migrationType) {
        this.sessionId = sessionId;
        this.migrationType = migrationType;
        this.lastUpdateTime = LocalDateTime.now();
        
        // 初始化文件路径
        String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
        this.bitmapFilePath = baseDir + "/" + sessionId + ".bitmap";
        this.metadataFilePath = baseDir + "/" + sessionId + ".metadata";
        
        // 确保目录存在
        try {
            Files.createDirectories(Paths.get(baseDir));
        } catch (IOException e) {
            log.error("Failed to create migration data directory: {}", baseDir, e);
        }
    }
    
    /**
     * 检查记录是否已处理
     * 
     * @param recordIndex 记录索引
     * @return 是否已处理
     */
    public boolean isProcessed(long recordIndex) {
        loadSegmentIfNeeded(recordIndex);
        if (currentSegmentBitmap == null) {
            return false;
        }
        
        int localIndex = (int) (recordIndex - currentSegmentStartIndex);
        return localIndex >= 0 && localIndex < segmentSize && 
               currentSegmentBitmap.get(localIndex);
    }
    
    /**
     * 标记记录为已处理
     * 
     * @param recordIndex 记录索引
     * @param success 是否成功
     */
    public void markProcessed(long recordIndex, boolean success) {
        loadSegmentIfNeeded(recordIndex);
        if (currentSegmentBitmap == null) {
            currentSegmentBitmap = new BitSet(segmentSize);
        }
        
        int localIndex = (int) (recordIndex - currentSegmentStartIndex);
        if (localIndex >= 0 && localIndex < segmentSize) {
            currentSegmentBitmap.set(localIndex);
            
            // 更新统计
            processedCount.incrementAndGet();
            if (success) {
                successCount.incrementAndGet();
            } else {
                failureCount.incrementAndGet();
            }
            
            // 更新最后处理索引
            if (recordIndex > lastProcessedIndex) {
                lastProcessedIndex = recordIndex;
            }
            
            lastUpdateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 更新当前处理位置
     * 
     * @param companyId 公司ID
     * @param companyPage 公司页码
     * @param userPage 用户页码
     */
    public void updatePosition(String companyId, int companyPage, int userPage) {
        this.currentCompanyId = companyId;
        this.currentCompanyPage = companyPage;
        this.currentUserPage = userPage;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 根据需要加载位图段
     * 
     * @param recordIndex 记录索引
     */
    private void loadSegmentIfNeeded(long recordIndex) {
        long segmentStart = (recordIndex / segmentSize) * segmentSize;
        
        if (currentSegmentBitmap == null || currentSegmentStartIndex != segmentStart) {
            // 保存当前段
            if (currentSegmentBitmap != null) {
                saveCurrentSegment();
            }
            
            // 加载新段
            currentSegmentStartIndex = segmentStart;
            currentSegmentBitmap = loadSegment(segmentStart);
        }
    }
    
    /**
     * 加载指定段的位图
     * 
     * @param segmentStart 段起始索引
     * @return 位图段
     */
    private BitSet loadSegment(long segmentStart) {
        String segmentFile = bitmapFilePath + ".seg" + (segmentStart / segmentSize);
        Path segmentPath = Paths.get(segmentFile);
        
        if (Files.exists(segmentPath)) {
            try (ObjectInputStream ois = new ObjectInputStream(
                    new BufferedInputStream(Files.newInputStream(segmentPath)))) {
                return (BitSet) ois.readObject();
            } catch (Exception e) {
                log.warn("Failed to load bitmap segment: {}", segmentFile, e);
            }
        }
        
        return new BitSet(segmentSize);
    }
    
    /**
     * 保存当前位图段
     */
    private void saveCurrentSegment() {
        if (currentSegmentBitmap == null) {
            return;
        }
        
        String segmentFile = bitmapFilePath + ".seg" + (currentSegmentStartIndex / segmentSize);
        try (ObjectOutputStream oos = new ObjectOutputStream(
                new BufferedOutputStream(Files.newOutputStream(Paths.get(segmentFile))))) {
            oos.writeObject(currentSegmentBitmap);
            log.debug("Saved bitmap segment: {}", segmentFile);
        } catch (IOException e) {
            log.error("Failed to save bitmap segment: {}", segmentFile, e);
        }
    }
    
    /**
     * 保存进度元数据
     */
    public void saveMetadata() {
        try (ObjectOutputStream oos = new ObjectOutputStream(
                new BufferedOutputStream(Files.newOutputStream(Paths.get(metadataFilePath))))) {
            
            // 先保存当前位图段
            saveCurrentSegment();
            
            // 保存元数据
            oos.writeObject(this);
            lastSaveTime = LocalDateTime.now();
            
            log.debug("Saved migration metadata: {}", metadataFilePath);
        } catch (IOException e) {
            log.error("Failed to save migration metadata: {}", metadataFilePath, e);
        }
    }
    
    /**
     * 加载进度元数据
     * 
     * @param sessionId 会话ID
     * @return 进度对象
     */
    public static OptimizedBitmapProgress loadMetadata(String sessionId) {
        String baseDir = System.getProperty("migration.data.dir", "/tmp/migration");
        String metadataFile = baseDir + "/" + sessionId + ".metadata";
        Path metadataPath = Paths.get(metadataFile);
        
        if (Files.exists(metadataPath)) {
            try (ObjectInputStream ois = new ObjectInputStream(
                    new BufferedInputStream(Files.newInputStream(metadataPath)))) {
                OptimizedBitmapProgress progress = (OptimizedBitmapProgress) ois.readObject();
                log.info("Loaded migration metadata: {}", metadataFile);
                return progress;
            } catch (Exception e) {
                log.error("Failed to load migration metadata: {}", metadataFile, e);
            }
        }
        
        return null;
    }
    
    /**
     * 清理所有文件
     */
    public void cleanup() {
        try {
            // 删除元数据文件
            Files.deleteIfExists(Paths.get(metadataFilePath));
            
            // 删除所有位图段文件
            long totalSegments = (totalRecords.get() + segmentSize - 1) / segmentSize;
            for (long i = 0; i < totalSegments; i++) {
                String segmentFile = bitmapFilePath + ".seg" + i;
                Files.deleteIfExists(Paths.get(segmentFile));
            }
            
            log.info("Cleaned up migration files for session: {}", sessionId);
        } catch (IOException e) {
            log.error("Failed to cleanup migration files for session: {}", sessionId, e);
        }
    }
    
    /**
     * 获取进度百分比
     * 
     * @return 进度百分比
     */
    public double getProgressPercentage() {
        long total = totalRecords.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) processedCount.get() / total * 100.0;
    }
    
    /**
     * 检查是否可以恢复
     * 
     * @return 是否可以恢复
     */
    public boolean canResume() {
        return status == MigrationStatus.PAUSED || status == MigrationStatus.FAILED;
    }
    
    /**
     * 开始迁移
     */
    public void start() {
        this.status = MigrationStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 暂停迁移
     */
    public void pause() {
        this.status = MigrationStatus.PAUSED;
        this.lastUpdateTime = LocalDateTime.now();
        saveMetadata(); // 暂停时保存进度
    }
    
    /**
     * 完成迁移
     */
    public void complete() {
        this.status = MigrationStatus.COMPLETED;
        this.lastUpdateTime = LocalDateTime.now();
        saveMetadata(); // 完成时保存进度
    }
    
    /**
     * 获取统计信息
     *
     * @return 统计信息字符串
     */
    public String getStatistics() {
        return String.format(
            "Session: %s, Status: %s, Progress: %.2f%%, " +
            "Total: %d, Processed: %d, Success: %d, Failure: %d, " +
            "Position: Company[%s] Page[%d/%d], LastIndex: %d",
            sessionId, status, getProgressPercentage(),
            totalRecords.get(), processedCount.get(), successCount.get(), failureCount.get(),
            currentCompanyId, currentCompanyPage, currentUserPage, lastProcessedIndex
        );
    }

    // ==================== 失败记录管理方法 ====================

    /**
     * 记录失败信息
     *
     * @param userId 用户ID
     * @param companyId 公司ID
     * @param globalIndex 全局索引
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     */
    public void recordFailure(String userId, String companyId, long globalIndex, String errorMessage, String errorType) {
        FailureRecord failureRecord = new FailureRecord(userId, companyId, globalIndex, errorMessage, errorType);
        failureRecords.put(userId, failureRecord);
        failureIndexMapping.put(globalIndex, userId);

        log.warn("Recorded failure for user: userId={}, companyId={}, globalIndex={}, errorType={}, errorMessage={}",
                userId, companyId, globalIndex, errorType, errorMessage);
    }

    /**
     * 获取失败记录
     *
     * @param userId 用户ID
     * @return 失败记录
     */
    public FailureRecord getFailureRecord(String userId) {
        return failureRecords.get(userId);
    }

    /**
     * 获取所有失败记录
     *
     * @return 失败记录列表
     */
    public List<FailureRecord> getAllFailureRecords() {
        return new ArrayList<>(failureRecords.values());
    }

    /**
     * 获取失败的用户ID列表
     *
     * @return 失败用户ID列表
     */
    public List<String> getFailedUserIds() {
        return new ArrayList<>(failureRecords.keySet());
    }

    /**
     * 根据公司ID获取失败记录
     *
     * @param companyId 公司ID
     * @return 该公司的失败记录列表
     */
    public List<FailureRecord> getFailureRecordsByCompany(String companyId) {
        return failureRecords.values().stream()
                .filter(record -> companyId.equals(record.getCompanyId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据错误类型获取失败记录
     *
     * @param errorType 错误类型
     * @return 该错误类型的失败记录列表
     */
    public List<FailureRecord> getFailureRecordsByErrorType(String errorType) {
        return failureRecords.values().stream()
                .filter(record -> errorType.equals(record.getErrorType()))
                .collect(Collectors.toList());
    }

    /**
     * 移除失败记录（重试成功后调用）
     *
     * @param userId 用户ID
     */
    public void removeFailureRecord(String userId) {
        FailureRecord removed = failureRecords.remove(userId);
        if (removed != null) {
            failureIndexMapping.remove(removed.getGlobalIndex());
            log.info("Removed failure record for user: {}", userId);
        }
    }

    /**
     * 更新失败记录的重试次数
     *
     * @param userId 用户ID
     */
    public void incrementRetryCount(String userId) {
        FailureRecord record = failureRecords.get(userId);
        if (record != null) {
            record.incrementRetryCount();
            log.debug("Incremented retry count for user: {}, retryCount={}", userId, record.getRetryCount());
        }
    }

    /**
     * 清理所有失败记录
     */
    public void clearFailureRecords() {
        int count = failureRecords.size();
        failureRecords.clear();
        failureIndexMapping.clear();
        log.info("Cleared {} failure records", count);
    }

    /**
     * 获取失败记录统计信息
     *
     * @return 失败统计信息
     */
    public String getFailureStatistics() {
        if (failureRecords.isEmpty()) {
            return "无失败记录";
        }

        Map<String, Long> errorTypeCount = failureRecords.values().stream()
                .collect(Collectors.groupingBy(FailureRecord::getErrorType, Collectors.counting()));

        Map<String, Long> companyFailureCount = failureRecords.values().stream()
                .collect(Collectors.groupingBy(FailureRecord::getCompanyId, Collectors.counting()));

        StringBuilder sb = new StringBuilder();
        sb.append("失败记录统计:\n");
        sb.append("总失败数: ").append(failureRecords.size()).append("\n");
        sb.append("按错误类型分组: ").append(errorTypeCount).append("\n");
        sb.append("按公司分组: ").append(companyFailureCount).append("\n");

        return sb.toString();
    }

    // ==================== 增量变化检测和处理方法 ====================

    /**
     * 启用增量检测
     *
     * @param checkIntervalMinutes 检查间隔（分钟）
     */
    public void enableIncrementalDetection(long checkIntervalMinutes) {
        this.enableIncrementalDetection = true;
        this.incrementalCheckIntervalMinutes = checkIntervalMinutes;
        this.lastIncrementalCheckTime = LocalDateTime.now();
        log.info("Enabled incremental detection for session: {}, checkInterval: {} minutes", sessionId, checkIntervalMinutes);
    }

    /**
     * 禁用增量检测
     */
    public void disableIncrementalDetection() {
        this.enableIncrementalDetection = false;
        log.info("Disabled incremental detection for session: {}", sessionId);
    }

    /**
     * 检查是否需要进行增量检测
     *
     * @return 是否需要检测
     */
    public boolean shouldPerformIncrementalCheck() {
        if (!enableIncrementalDetection || lastIncrementalCheckTime == null) {
            return false;
        }

        LocalDateTime nextCheckTime = lastIncrementalCheckTime.plusMinutes(incrementalCheckIntervalMinutes);
        return LocalDateTime.now().isAfter(nextCheckTime);
    }

    /**
     * 更新最后检查时间
     */
    public void updateLastIncrementalCheckTime() {
        this.lastIncrementalCheckTime = LocalDateTime.now();
    }

    /**
     * 记录状态变化
     *
     * @param userId 用户ID
     * @param companyId 公司ID
     * @param originalStatus 原始状态
     * @param currentStatus 当前状态
     */
    public void recordStatusChange(String userId, String companyId, String originalStatus, String currentStatus) {
        if (!originalStatus.equals(currentStatus)) {
            StatusChangeRecord changeRecord = new StatusChangeRecord(userId, companyId, originalStatus, currentStatus);
            statusChangeRecords.put(userId, changeRecord);

            log.info("Recorded status change: userId={}, companyId={}, {}->{}",
                    userId, companyId, originalStatus, currentStatus);
        }
    }

    /**
     * 获取状态变化记录
     *
     * @param userId 用户ID
     * @return 状态变化记录
     */
    public StatusChangeRecord getStatusChangeRecord(String userId) {
        return statusChangeRecords.get(userId);
    }

    /**
     * 获取所有状态变化记录
     *
     * @return 状态变化记录列表
     */
    public List<StatusChangeRecord> getAllStatusChangeRecords() {
        return new ArrayList<>(statusChangeRecords.values());
    }

    /**
     * 获取需要重新处理的状态变化记录
     *
     * @return 需要重新处理的记录列表
     */
    public List<StatusChangeRecord> getStatusChangesNeedingReprocessing() {
        return statusChangeRecords.values().stream()
                .filter(StatusChangeRecord::isNeedsReprocessing)
                .collect(Collectors.toList());
    }

    /**
     * 根据状态变化类型获取记录
     *
     * @param fromStatus 原始状态
     * @param toStatus 目标状态
     * @return 符合条件的状态变化记录列表
     */
    public List<StatusChangeRecord> getStatusChangesByType(String fromStatus, String toStatus) {
        return statusChangeRecords.values().stream()
                .filter(record -> fromStatus.equals(record.getOriginalStatus()) && toStatus.equals(record.getCurrentStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 标记状态变化记录为已重新处理
     *
     * @param userId 用户ID
     */
    public void markStatusChangeAsReprocessed(String userId) {
        StatusChangeRecord record = statusChangeRecords.get(userId);
        if (record != null) {
            record.markAsReprocessed();
            log.info("Marked status change as reprocessed: userId={}", userId);
        }
    }

    /**
     * 移除状态变化记录
     *
     * @param userId 用户ID
     */
    public void removeStatusChangeRecord(String userId) {
        StatusChangeRecord removed = statusChangeRecords.remove(userId);
        if (removed != null) {
            log.info("Removed status change record: userId={}", userId);
        }
    }

    /**
     * 清理所有状态变化记录
     */
    public void clearStatusChangeRecords() {
        int count = statusChangeRecords.size();
        statusChangeRecords.clear();
        log.info("Cleared {} status change records", count);
    }

    /**
     * 获取状态变化统计信息
     *
     * @return 状态变化统计信息
     */
    public String getStatusChangeStatistics() {
        if (statusChangeRecords.isEmpty()) {
            return "无状态变化记录";
        }

        Map<String, Long> changeTypeCount = statusChangeRecords.values().stream()
                .collect(Collectors.groupingBy(
                        record -> record.getOriginalStatus() + "->" + record.getCurrentStatus(),
                        Collectors.counting()));

        long needsReprocessingCount = statusChangeRecords.values().stream()
                .mapToLong(record -> record.isNeedsReprocessing() ? 1 : 0)
                .sum();

        StringBuilder sb = new StringBuilder();
        sb.append("状态变化统计:\n");
        sb.append("总变化数: ").append(statusChangeRecords.size()).append("\n");
        sb.append("待重新处理: ").append(needsReprocessingCount).append("\n");
        sb.append("按变化类型分组: ").append(changeTypeCount).append("\n");

        return sb.toString();
    }

    // ==================== 任务分离相关方法 ====================

    /**
     * 启用任务分离模式
     *
     * @param taskType 任务类型
     * @param relatedSessionId 关联任务的会话ID
     */
    public void enableTaskSeparation(TaskType taskType, String relatedSessionId) {
        this.enableTaskSeparation = true;
        this.taskType = taskType;
        this.relatedTaskSessionId = relatedSessionId;
        log.info("Enabled task separation for session: {}, taskType: {}, relatedSession: {}",
                sessionId, taskType, relatedSessionId);
    }

    /**
     * 禁用任务分离模式
     */
    public void disableTaskSeparation() {
        this.enableTaskSeparation = false;
        this.taskType = TaskType.MIXED;
        this.relatedTaskSessionId = null;
        log.info("Disabled task separation for session: {}", sessionId);
    }

    /**
     * 检查是否应该检测特定类型的状态变化
     *
     * @param fromStatus 原始状态
     * @param toStatus 目标状态
     * @return 是否应该检测此类型的变化
     */
    public boolean shouldDetectStatusChange(String fromStatus, String toStatus) {
        if (!enableTaskSeparation) {
            return true; // 混合模式检测所有变化
        }

        // 任务分离模式下，只检测跨任务的状态变化
        if (taskType == TaskType.FINISHED_ONLY) {
            // FINISHED任务只关心变为NO_FINISHED的数据
            return "FINISHED".equals(fromStatus) && "NO_FINISHED".equals(toStatus);
        } else if (taskType == TaskType.NO_FINISHED_ONLY) {
            // NO_FINISHED任务只关心变为FINISHED的数据
            return "NO_FINISHED".equals(fromStatus) && "FINISHED".equals(toStatus);
        }

        return false;
    }

    /**
     * 获取目标任务类型（状态变化后应该由哪个任务处理）
     *
     * @param currentStatus 当前状态
     * @return 目标任务类型
     */
    public TaskType getTargetTaskType(String currentStatus) {
        if ("FINISHED".equals(currentStatus)) {
            return TaskType.FINISHED_ONLY;
        } else if ("NO_FINISHED".equals(currentStatus)) {
            return TaskType.NO_FINISHED_ONLY;
        }
        return TaskType.MIXED;
    }

    /**
     * 检查状态变化是否需要跨任务处理
     *
     * @param changeRecord 状态变化记录
     * @return 是否需要跨任务处理
     */
    public boolean needsCrossTaskProcessing(StatusChangeRecord changeRecord) {
        if (!enableTaskSeparation) {
            return false;
        }

        TaskType targetTaskType = getTargetTaskType(changeRecord.getCurrentStatus());
        return targetTaskType != this.taskType;
    }

    /**
     * 获取跨任务状态变化记录
     *
     * @return 需要跨任务处理的状态变化记录
     */
    public List<StatusChangeRecord> getCrossTaskStatusChanges() {
        return statusChangeRecords.values().stream()
                .filter(this::needsCrossTaskProcessing)
                .filter(StatusChangeRecord::isNeedsReprocessing)
                .collect(Collectors.toList());
    }

    /**
     * 获取本任务内的状态变化记录
     *
     * @return 本任务内的状态变化记录
     */
    public List<StatusChangeRecord> getInTaskStatusChanges() {
        return statusChangeRecords.values().stream()
                .filter(record -> !needsCrossTaskProcessing(record))
                .filter(StatusChangeRecord::isNeedsReprocessing)
                .collect(Collectors.toList());
    }

    /**
     * 标记跨任务状态变化为已转移
     *
     * @param userId 用户ID
     */
    public void markCrossTaskChangeAsTransferred(String userId) {
        StatusChangeRecord record = statusChangeRecords.get(userId);
        if (record != null && needsCrossTaskProcessing(record)) {
            record.markAsReprocessed();
            log.info("Marked cross-task change as transferred: userId={}, {}->{}",
                    userId, record.getOriginalStatus(), record.getCurrentStatus());
        }
    }

    // Getters and Setters for incremental detection
    public boolean isEnableIncrementalDetection() { return enableIncrementalDetection; }
    public void setEnableIncrementalDetection(boolean enableIncrementalDetection) { this.enableIncrementalDetection = enableIncrementalDetection; }

    public LocalDateTime getLastIncrementalCheckTime() { return lastIncrementalCheckTime; }
    public void setLastIncrementalCheckTime(LocalDateTime lastIncrementalCheckTime) { this.lastIncrementalCheckTime = lastIncrementalCheckTime; }

    public long getIncrementalCheckIntervalMinutes() { return incrementalCheckIntervalMinutes; }
    public void setIncrementalCheckIntervalMinutes(long incrementalCheckIntervalMinutes) { this.incrementalCheckIntervalMinutes = incrementalCheckIntervalMinutes; }

    // Getters and Setters for task separation
    public boolean isEnableTaskSeparation() { return enableTaskSeparation; }
    public void setEnableTaskSeparation(boolean enableTaskSeparation) { this.enableTaskSeparation = enableTaskSeparation; }

    public String getRelatedTaskSessionId() { return relatedTaskSessionId; }
    public void setRelatedTaskSessionId(String relatedTaskSessionId) { this.relatedTaskSessionId = relatedTaskSessionId; }

    public TaskType getTaskType() { return taskType; }
    public void setTaskType(TaskType taskType) { this.taskType = taskType; }

    // ==================== 动态扩展相关方法 ====================

    /**
     * 启用动态扩展功能
     *
     * @param checkIntervalMinutes 动态检查间隔（分钟）
     */
    public void enableDynamicExpansion(long checkIntervalMinutes) {
        this.enableDynamicExpansion = true;
        this.dynamicCheckIntervalMinutes = checkIntervalMinutes;
        this.lastDynamicCheckTime = LocalDateTime.now();
        log.info("Enabled dynamic expansion for session: {}, checkInterval: {} minutes", sessionId, checkIntervalMinutes);
    }

    /**
     * 禁用动态扩展功能
     */
    public void disableDynamicExpansion() {
        this.enableDynamicExpansion = false;
        log.info("Disabled dynamic expansion for session: {}", sessionId);
    }

    /**
     * 检查是否需要进行动态检查
     *
     * @return 是否需要检查
     */
    public boolean shouldPerformDynamicCheck() {
        if (!enableDynamicExpansion || lastDynamicCheckTime == null) {
            return false;
        }

        LocalDateTime nextCheckTime = lastDynamicCheckTime.plusMinutes(dynamicCheckIntervalMinutes);
        return LocalDateTime.now().isAfter(nextCheckTime);
    }

    /**
     * 更新最后动态检查时间
     */
    public void updateLastDynamicCheckTime() {
        this.lastDynamicCheckTime = LocalDateTime.now();
    }

    /**
     * 手动添加动态用户
     *
     * @param userId 用户ID
     * @param companyId 公司ID
     * @param userStatus 用户状态
     * @return 是否添加成功
     */
    public boolean addDynamicUser(String userId, String companyId, String userStatus) {
        return addDynamicUser(userId, companyId, userStatus, "MANUAL");
    }

    /**
     * 添加动态用户
     *
     * @param userId 用户ID
     * @param companyId 公司ID
     * @param userStatus 用户状态
     * @param addedBy 添加来源
     * @return 是否添加成功
     */
    public boolean addDynamicUser(String userId, String companyId, String userStatus, String addedBy) {
        if (dynamicUsers.containsKey(userId)) {
            log.warn("Dynamic user already exists: userId={}", userId);
            return false;
        }

        // 检查是否与当前任务类型匹配
        if (enableTaskSeparation && !isUserStatusMatchTask(userStatus)) {
            log.warn("User status {} does not match task type {}: userId={}", userStatus, taskType, userId);
            return false;
        }

        DynamicUserRecord record = new DynamicUserRecord(userId, companyId, userStatus, addedBy);
        dynamicUsers.put(userId, record);

        log.info("Added dynamic user: userId={}, companyId={}, status={}, addedBy={}",
                userId, companyId, userStatus, addedBy);
        return true;
    }

    /**
     * 批量添加动态用户
     *
     * @param users 用户列表（userId -> {companyId, userStatus}）
     * @param addedBy 添加来源
     * @return 成功添加的用户数量
     */
    public int addDynamicUsers(Map<String, Map<String, String>> users, String addedBy) {
        int successCount = 0;
        for (Map.Entry<String, Map<String, String>> entry : users.entrySet()) {
            String userId = entry.getKey();
            Map<String, String> userInfo = entry.getValue();
            String companyId = userInfo.get("companyId");
            String userStatus = userInfo.get("userStatus");

            if (addDynamicUser(userId, companyId, userStatus, addedBy)) {
                successCount++;
            }
        }

        log.info("Batch added dynamic users: total={}, success={}, addedBy={}",
                users.size(), successCount, addedBy);
        return successCount;
    }

    /**
     * 获取需要处理的动态用户
     *
     * @return 需要处理的动态用户列表
     */
    public List<DynamicUserRecord> getDynamicUsersNeedingProcessing() {
        return dynamicUsers.values().stream()
                .filter(DynamicUserRecord::isNeedsProcessing)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有动态用户
     *
     * @return 所有动态用户列表
     */
    public List<DynamicUserRecord> getAllDynamicUsers() {
        return new ArrayList<>(dynamicUsers.values());
    }

    /**
     * 根据公司ID获取动态用户
     *
     * @param companyId 公司ID
     * @return 该公司的动态用户列表
     */
    public List<DynamicUserRecord> getDynamicUsersByCompany(String companyId) {
        return dynamicUsers.values().stream()
                .filter(record -> companyId.equals(record.getCompanyId()))
                .collect(Collectors.toList());
    }

    /**
     * 标记动态用户为已处理
     *
     * @param userId 用户ID
     * @param globalIndex 分配的全局索引
     */
    public void markDynamicUserAsProcessed(String userId, long globalIndex) {
        DynamicUserRecord record = dynamicUsers.get(userId);
        if (record != null) {
            record.setGlobalIndex(globalIndex);
            record.markAsProcessed();
            log.info("Marked dynamic user as processed: userId={}, globalIndex={}", userId, globalIndex);
        }
    }

    /**
     * 移除动态用户记录
     *
     * @param userId 用户ID
     */
    public void removeDynamicUser(String userId) {
        DynamicUserRecord removed = dynamicUsers.remove(userId);
        if (removed != null) {
            log.info("Removed dynamic user: userId={}", userId);
        }
    }

    /**
     * 清理已处理的动态用户记录
     *
     * @return 清理的记录数量
     */
    public int cleanupProcessedDynamicUsers() {
        List<String> processedUserIds = dynamicUsers.entrySet().stream()
                .filter(entry -> entry.getValue().isProcessed())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        processedUserIds.forEach(dynamicUsers::remove);

        log.info("Cleaned up processed dynamic users: count={}", processedUserIds.size());
        return processedUserIds.size();
    }

    /**
     * 检查用户状态是否与当前任务类型匹配
     *
     * @param userStatus 用户状态
     * @return 是否匹配
     */
    private boolean isUserStatusMatchTask(String userStatus) {
        if (taskType == TaskType.FINISHED_ONLY) {
            return "FINISHED".equals(userStatus);
        } else if (taskType == TaskType.NO_FINISHED_ONLY) {
            return "NO_FINISHED".equals(userStatus);
        }
        return true; // MIXED类型接受所有状态
    }

    /**
     * 获取动态扩展统计信息
     *
     * @return 动态扩展统计信息
     */
    public String getDynamicExpansionStatistics() {
        if (dynamicUsers.isEmpty()) {
            return "无动态新增用户";
        }

        long needsProcessingCount = dynamicUsers.values().stream()
                .mapToLong(record -> record.isNeedsProcessing() ? 1 : 0)
                .sum();

        long processedCount = dynamicUsers.values().stream()
                .mapToLong(record -> record.isProcessed() ? 1 : 0)
                .sum();

        Map<String, Long> addedByCount = dynamicUsers.values().stream()
                .collect(Collectors.groupingBy(
                        DynamicUserRecord::getAddedBy,
                        Collectors.counting()));

        StringBuilder sb = new StringBuilder();
        sb.append("动态扩展统计:\n");
        sb.append("总动态用户数: ").append(dynamicUsers.size()).append("\n");
        sb.append("待处理: ").append(needsProcessingCount).append("\n");
        sb.append("已处理: ").append(processedCount).append("\n");
        sb.append("按来源分组: ").append(addedByCount).append("\n");

        return sb.toString();
    }

    // Getters and Setters for dynamic expansion
    public boolean isEnableDynamicExpansion() { return enableDynamicExpansion; }
    public void setEnableDynamicExpansion(boolean enableDynamicExpansion) { this.enableDynamicExpansion = enableDynamicExpansion; }

    public LocalDateTime getLastDynamicCheckTime() { return lastDynamicCheckTime; }
    public void setLastDynamicCheckTime(LocalDateTime lastDynamicCheckTime) { this.lastDynamicCheckTime = lastDynamicCheckTime; }

    public long getDynamicCheckIntervalMinutes() { return dynamicCheckIntervalMinutes; }
    public void setDynamicCheckIntervalMinutes(long dynamicCheckIntervalMinutes) { this.dynamicCheckIntervalMinutes = dynamicCheckIntervalMinutes; }
}
