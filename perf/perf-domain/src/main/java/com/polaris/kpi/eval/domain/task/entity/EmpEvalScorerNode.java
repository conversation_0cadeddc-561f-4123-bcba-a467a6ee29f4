package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.*;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.perf.www.common.constant.BusinessConstant;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreValueConf;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonAryColumn;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
public class EmpEvalScorerNode extends EvalScorerNodeBase {
    private String id;//
    @JSONField(serialize = false)
    protected TenantId companyId;//公司id
    private String taskId;//考核任务id
    private String taskUserId;//员工任务id
    private String empId;//被考核人id
    private String evalScorerId;//关联empEvalScorer.id
    // private List<EvalScorerNodeKpiItem> scorerNodeKpiItems = new ArrayList<>();//评分人评分环节指标
    @JsonAryColumn(EvalScorerNodeKpiType.class)
    private List<EvalScorerNodeKpiType> scorerNodeKpiTypes = new ArrayList<>();//评分人评分环节维度+指标
    private BigDecimal scorerNodeScore;//评分人环节分数
    private String approverType;//审批人指定人类型（管理员/指定人员）
    private String vacancyApproverType;//审批人空缺时指定人类型（管理员/指定人员）
    private String vacancyApproverInfo;//审批人空缺时指定人id
    private String transferType;//转交类型（null|transfer|skip）：null： 无转交， transfer:正常转交，skip:跳过转交  ,默认是null
    private List<String> transferTo;//转入者empId  会存在多个接收者 [跳过转交层级多个人]
    private String transferFrom;//转出者empId

    // todo 转交逻辑还是按照，指定人转交
    // todo 跳过逻辑 采用复制节点规则，处理方式： 环节内： 多个人【会签】 ，其他人也复制一份，或签 直接标注跳过【2】
    // todo 环节内： 1个人,将该评价人的node，复制给除开（评价人所属层级）之外的其他节点的所有评价人 各一份 【评价人的node】
    //todo 计算环节分，指标分，总分，还是按照现有计算方式


    public EmpEvalScorerNode(TenantId tenantId, String opEmpId, String taskId, String empId,
                             String taskUserId, String scorerType, Rater rater) {
        this.companyId = tenantId;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();

        this.taskId = taskId;
        this.empId = empId;
        this.taskUserId = taskUserId;
        this.scorerId = rater.isTaskEmp() ? empId : rater.getEmpId();
        this.scorerName = rater.getEmpName();
        this.scorerType = scorerType;
        this.nodeName = SubScoreNodeEnum.fromStr(scorerType).getDesc();
        this.approvalOrder = 1;
        this.status = 0;//0-待分发
    }

    @JSONField(serialize = false)
    public boolean isSelfScoreRejected() {
        return 3 == status && "self_score".equals(scorerType);
    }

    /**
     * 判断是否为计算节点
     * 计算节点用于计算评分结果
     *
     * @return 是否为计算节点
     */
    public boolean isComputeNode() {
        return this.isSkipType()
                || this.isTransferType()
                || this.noHavDoTransfer();
    }

    public boolean isFlowNode() {
        return this.isSkipHiddenType()
                || this.isTransferType()
                || this.noHavDoTransfer();
    }

    public void builderItemWaitScore(String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig,
                                     ScoreValueConf scoreValueConf,
                                     List<EmpEvalKpiType> kpiTypes){
        ListWrap<EmpEvalKpiType> wrap = new ListWrap<>(kpiTypes).asMap(EmpEvalKpiType::getKpiTypeId);
        for (EvalScorerNodeKpiType scorerNodeKpiType : scorerNodeKpiTypes) {
            EmpEvalKpiType kpiType = wrap.mapGet(scorerNodeKpiType.getKpiTypeId());
            if (kpiType == null){
                continue;
            }
            scorerNodeKpiType.builderItemWaitScore(dingCorpId, defaultCustomScoreConfig, defaultPlusSubScoreConfig, kpiType.getIndLevelGroup(), scoreValueConf,kpiType.getItems());
        }
    }

    public void acceptTransferData(String transferType, String transferFrom, List<String> transferTo){
        this.transferType = transferType;
        this.transferFrom = transferFrom;
        this.transferTo = transferTo;
    }
    public List<EvalScorerNodeKpiType> listNeedTypeEval() {
        List<EvalScorerNodeKpiType> list = new ArrayList<>();
        scorerNodeKpiTypes.stream().filter(EvalScorerNodeKpiType::needTypeAndItemEval).forEach(scorerNodeKpiType -> {
            scorerNodeKpiType.accItemScorerTypeAndOrder(scorerId, scorerType, approvalOrder, status, multiType, transferType, this.isComputeNode(),this.canShowNode());
            list.add(scorerNodeKpiType);
        });
        return list;
    }

    /**
     * 重置评分节点为待分发状态
     * 清空所有评分数据
     */
    public void resetScoreNodeForWaitDispatch() {
        this.status = 0;
        this.scorerNodeScore = null;
        this.scoreLevel = null;
        this.totalComment = null;
        this.totalScore = null;
        this.scoreAttUrl = null;
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            return;
        }
        scorerNodeKpiTypes.forEach(EvalScorerNodeKpiType::resetScoreNodeForWaitDispatch);
    }

    /**
     * 判断是否未进行转交
     *
     * @return 是否未进行转交
     */
    public boolean noHavDoTransfer() {
        return null == transferType || StrUtil.equals("", transferType);
    }

    /**
     * 判断是否为正常转交类型
     * 用于计算与流程校验
     *
     * @return 是否为正常转交类型
     */
    public boolean isTransferType() {
        return StrUtil.equals("transfer", transferType) && CollUtil.isEmpty(transferTo) && StrUtil.isNotBlank(transferFrom);//用作计算与流程校验
    }
    /**
     * 判断是否为正常转交类型
     * 【转出者，节点隐藏】
     *
     * @return 是否为正常转交类型
     */
    public boolean isHiddenTransferType() {
        return StrUtil.equals("transfer", transferType) && CollUtil.isNotEmpty(transferTo) && StrUtil.isEmpty(transferFrom);//用作隐藏不显示
    }

    /**
     * 判断是否为跳过类型
     * 用于计算与流程校验
     *
     * @return 是否为跳过类型
     */
    public boolean isSkipType() {
        return isSkipTransferType() && CollUtil.isEmpty(transferTo) && StrUtil.isNotBlank(transferFrom);//用作计算与流程校验
    }

    /**
     * 判断是否为跳过转交类型
     * 用于流程校验
     *
     * @return 是否为跳过转交类型
     */
    public boolean isSkipTransferType() {
        return StrUtil.equals("skip", transferType);//用作流程校验
    }

    /**
     * 判断是否为跳过隐藏类型
     * 用于流程校验和显示
     *
     * @return 是否为跳过隐藏类型
     */
    public boolean isSkipHiddenType() {//跳过者类型
        return isSkipTransferType() && CollUtil.isNotEmpty(transferTo) && StrUtil.isBlank(transferFrom);//用作流程校验和显示
    }

    /**
     * 判断是否需要排除节点[计算时]
     *
     * @return 计算时需要需要排除节点
     */
    public boolean needExcludeNode() {
        if (this.isSkipHiddenType() || isHiddenTransferType()) {
            return true;
        }
        return CollUtil.isNotEmpty(transferTo) && StrUtil.isNotEmpty(transferFrom);//需要排除的
    }

    /**
     * 是否无效节点 【可以进行重置个人、重置评分中使用】
     * @return true-无效，false有效
     */
    public boolean noValidNode() {
        if (isHiddenTransferType()) {
            return true;
        }
        return CollUtil.isNotEmpty(transferTo) && StrUtil.isNotEmpty(transferFrom);//需要排除的
    }

    public boolean canShowNode(){
        return this.noHavDoTransfer() || this.isTransferType() || this.isSkipHiddenType();
    }

    /**
     * 跳过转出者行为
     * 将节点标记为跳过转交状态
     */
    // 跳过转出者行为
    // transferToIf    |  tryTransfer
    public void markAsSkipTransfer() {
        this.transferType = "skip";//2:跳过转交【from-2】
        this.updatedTime = new Date();
    }

    /**
     * 正常转交转出者行为
     * 将节点标记为正常转交状态
     *
     * @param transferTo 转入者ID
     */
    // 正常转交 转出者行为
    // transferToIf    |  tryTransfer
    public void markAsNormalTransfer(String transferTo) {
        this.transferTo = Collections.singletonList(transferTo);
        if (StrUtil.isBlank(this.transferType)) {//如果不为空，则是复制的数据内部进行转移，类型无需变化
            this.transferType = "transfer";//| 1:正常转交
        }
        this.updatedTime = new Date();
    }

    // 转入者行为
    public void receiveFromForSkip(String fromEmpId) {
        this.transferFrom = fromEmpId;
        this.transferType = "skip";//| 1:跳过转交
        this.updatedTime = new Date();
    }

    /**
     * 转出者设置跳过转交接收者
     *
     * @param transferTos 接收者ID列表
     */
    // 转出者行为
    public void setSkipTransferReceivers(List<String> transferTos) {
        this.transferTo = transferTos;
        this.transferType = "skip";//| 1:跳过转交
        this.updatedTime = new Date();
    }

    // 转入者行为
    public void receiveFromForTransfer(String fromEmpId) {
        this.transferFrom = fromEmpId;
        if (StrUtil.isBlank(this.transferType)) {//如果不为空，则是复制的数据内部进行转移，类型无需变化
            this.transferType = "transfer";//| 1:正常转交
        }
        this.updatedTime = new Date();
    }

    @JSONField(serialize = false)
    public boolean isFinish() {
        return status == 2;
    }

    @JSONField(serialize = false)
    public boolean noFinish() {
        return status != 2;
    }

    public String order() {
        return approvalOrder + "";
    }

    public boolean nodesIsTotalScore() {
        return StrUtil.equals("total", rateMode);
    }

    public void autoSubmitInitZero() {
        for (EvalScorerNodeKpiType kpiType : this.scorerNodeKpiTypes) {
            kpiType.autoSubmitInitZero();
        }
    }

    public void accpOtherScoreNodeData(EmpEvalScorerNode node) {
        this.status = node.getStatus();
        this.handlerTime = node.getHandlerTime();
    }

    public void initIdNull() {
        this.id = null;
        this.evalScorerId = null;
        this.scorerNodeKpiTypes.forEach(EvalScorerNodeKpiType::initIdNull);
    }

    @JSONField(serialize = false)
    public boolean isExistsNoFinish(SubScoreNodeEnum node, int nodeOrder) {
        return this.match(node, nodeOrder) && !this.isFinish();
    }

    public EmpEvalScorerNode(String companyId, String opEmpId, String taskId, String empId, String taskUserId
            , String scorerType, Integer order, String multiType, Rater rater, boolean signatureFlag, String rateMode) {
        this.companyId = new TenantId(companyId);
        this.taskId = taskId;
        this.empId = empId;
        this.taskUserId = taskUserId;
        this.scorerId = rater.isTaskEmp() ? empId : rater.getEmpId();
        this.scorerType = scorerType;
        this.nodeName = SubScoreNodeEnum.fromStr(scorerType).getDesc();
        this.approvalOrder = order == null ? 1 : order;
        this.scorerName = rater.getEmpName();
        this.scorerAvatar = rater.getAvatar();
        this.multiType = multiType;
        this.approverType = rater.isTaskEmp() ? "taskEmp" : "user";//评分人审批类型：直属主管、指定人员、被考核人
        this.vacancyApproverType = BusinessConstant.VACANCY_APPROVER_TYPE_ADMIN; //审批人空缺时指定人类型（管理员/指定人员）
        this.vacancyApproverInfo = opEmpId; //审批人空缺时指定人id
        this.signatureFlag = signatureFlag;
        this.scoreWeight = rater.getWeight();
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.status = 0;//0-待分发
        this.rateMode = rateMode;
    }

    public List<EvalScorerNodeKpiItem> getAllKpiItems() {
        return scorerNodeKpiTypes.stream().map(EvalScorerNodeKpiType::getScorerNodeKpiItems).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public String itemLogNames(Map<String, EvalKpi> kpiMap) {
        List<EvalScorerNodeKpiItem> kpis = getAllKpiItems();
        if (CollUtil.isEmpty(kpis)) {
            return "";
        }
        List<String> itemNames;
        List<String> itemIds = kpis.stream().map(EvalScorerNodeKpiItem::asKpiItemKey).collect(Collectors.toList());
        itemNames = itemIds.stream().filter(itemId -> kpiMap.containsKey(itemId) && Objects.nonNull(kpiMap.get(itemId)))
                .map(itemId -> kpiMap.get(itemId).getKpiItemName()).collect(Collectors.toList());
        return CollUtil.join(itemNames, ",");
    }

    public boolean isActive() {
        return this.totalScore != null;
    }

    public void submitScoreNs(V3SubmitedEvalNodeScore submitNodeScore) {
        this.scoreLevel = submitNodeScore.getScoreLevel();
        this.totalComment = submitNodeScore.getTotalComment();
        this.scoreAttUrl = submitNodeScore.getScoreAttUrl();
        this.totalScore = submitNodeScore.getTotalScore();
        this.status = submitNodeScore.status;
        this.scorerNodeScore = submitNodeScore.getScorerNodeScore();
        this.upStatus(this.status);

        if (CollUtil.isEmpty(this.scorerNodeKpiTypes) || CollUtil.isEmpty(submitNodeScore.getScorerNodeKpiTypes())) {
            return;
        }

        ListWrap<EvalScorerNodeKpiType> kpiTypeGroup = new ListWrap<>(submitNodeScore.getScorerNodeKpiTypes()).asMap(EvalScorerNodeKpiType::getKpiTypeId);
        for (EvalScorerNodeKpiType kpiType : this.scorerNodeKpiTypes) {
            EvalScorerNodeKpiType submitType = kpiTypeGroup.mapGet(kpiType.getKpiTypeId());
            if (submitType == null) {
                continue;
            }
            kpiType.copyOtherNodeData(submitType);
        }
    }


    public void copyOtherNodeData(EmpEvalScorerNode otherNode) {
        this.scoreLevel = otherNode.getScoreLevel();
        this.totalComment = otherNode.getTotalComment();
        this.scoreAttUrl = otherNode.getScoreAttUrl();
        this.totalScore = otherNode.getTotalScore();
        if (CollUtil.isEmpty(otherNode.getScorerNodeKpiTypes())) {
            return;
        }
        ListWrap<EvalScorerNodeKpiType> kpiTypeGroup = new ListWrap<>(otherNode.scorerNodeKpiTypes).asMap(EvalScorerNodeKpiType::getKpiTypeId);
        for (EvalScorerNodeKpiType kpiType : this.getScorerNodeKpiTypes()) {
            if (!kpiTypeGroup.contains(kpiType.getKpiTypeId())) {
                continue;
            }
            kpiType.copyOtherNodeData(kpiTypeGroup.mapGet(kpiType.getKpiTypeId()));
        }
    }

    @JSONField(serialize = false)
    public boolean isTotalLevelScored(){
        return StrUtil.isNotEmpty(this.scoreLevel);
    }
    public boolean allSubmitKpiItem() {
        if (this.isSkipHiddenType()) {
            return true;
        }
        //如果是评价总等级，需要校验评价等级是否有值
        if (this.isTotalLevel()) {
            return isTotalLevelScored();
        }

        if (CollUtil.isEmpty(this.getAllKpiItems())) {
            return true;
        }

        // 先检查所有需要评分的类型是否完成
        boolean isFinish = this.scorerNodeKpiTypes.stream()
                .filter(EvalScorerNodeKpiType::onlyTypeEval)
                .allMatch(EvalScorerNodeKpiType::isScored);

        if (!isFinish) {
            return false;
        }

        // 再检查非评分类型下的所有指标项是否完成
        return this.scorerNodeKpiTypes.stream()
                .filter(kpiType -> !kpiType.onlyTypeEval())
                .flatMap(kpiType -> kpiType.getScorerNodeKpiItems().stream())
                .allMatch(EvalScorerNodeKpiItem::isScored);
    }

    public boolean isTotalLevel() {
        return SubScoreNodeEnum.isTotalLevelScore(scorerType);
    }

    public boolean resetScoreTotalLevel() {
        //如果是打总等级则重置为待分发
        if (!SubScoreNodeEnum.isTotalLevelScore(scorerType)) {
            return false;
        }
        this.waitDispatched();
        return true;
    }

    public void submitLevel(Map<String, EmpEvalKpiType> upTypeMap, Map<String, EvalKpi> upKpiMap, Map<String, EmpEvalKpiType> kpiTypeMap) {
        for (EvalScorerNodeKpiType scorerNodeKpiType : this.getScorerNodeKpiTypes()) {
            EmpEvalKpiType type = kpiTypeMap.get(scorerNodeKpiType.getKpiTypeId());
            //维度打等级
            if (isNeedUpdateLevel(type)) {
                scorerNodeKpiType.accpSubmitLevel(type, upKpiMap);
                upTypeMap.put(type.getKpiTypeId(), type);
                continue;
            }
            //指标打等级
            scorerNodeKpiType.submitLevel(type, upKpiMap);
        }
    }

    public boolean isNeedUpdateLevel(EmpEvalKpiType type) {
        if (!type.isEvalLevel()) {
            return false;
        }
        boolean containSup = new ListWrap<>(type.getWaitScores()).mapTo(EvalScorerNodeKpiType::getScorerType).contains(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        return !containSup || this.isSupper();
    }


    public void acceptSignatureFlag(boolean signatureFlag) {
        if (this.signatureFlag) {
            return;
        }
        this.signatureFlag = signatureFlag;
    }

    public boolean needResetScoreNode(ScoreEmp scoreEmp) {
        if (this.scorerType.equals(scoreEmp.getScorerType()) &&
                this.scorerId.equals(scoreEmp.getEmpId()) &&
                this.approvalOrder.equals(scoreEmp.getApprovalOrder())) {
            return true;
        }

        //如果是跳过的也需重置,，跳过复制的节点也需要重置，需重新评价赋值
        return this.isSkipType() && this.scorerType.equals(scoreEmp.getScorerType()) && this.scorerId.equals(scoreEmp.getEmpId());
    }

    /**
     * @description: 计算评分人 评分环节
     * 类*指*人
     * @author: suxiaoqiu
     * @date: 2022/5/16 14:32
     * @param: [typePecWgt, itemPecWgt]
     * @return: void
     **/
    public void computeScorerNodeItemScore(Map<String, EmpEvalKpiType> kpiTypeMap,
                                           boolean submitWithWeigh, BigDecimal itemAutoScore) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes) || MapUtil.isEmpty(kpiTypeMap)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }
        SubScoreNodeEnum nodeEnum = SubScoreNodeEnum.fromStr(scorerType);//评分环节
        if (Objects.isNull(nodeEnum)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }
        for (EvalScorerNodeKpiType item : scorerNodeKpiTypes) {
            item.computeFinalScorerNodeScore(kpiTypeMap, submitWithWeigh, nodeEnum);
        }
        //计算评分人环节分数
        computeScorerNodeScore(itemAutoScore);
    }


    public void reComputeScorerNodeItemScore(Map<String, EmpEvalKpiType> kpiTypeMap, boolean submitWithWeigh) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes) || MapUtil.isEmpty(kpiTypeMap)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }
        SubScoreNodeEnum nodeEnum = SubScoreNodeEnum.fromStr(scorerType);//评分环节
        if (Objects.isNull(nodeEnum)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }
        for (EvalScorerNodeKpiType item : scorerNodeKpiTypes) {
            item.computeFinalScorerNodeScore(kpiTypeMap, submitWithWeigh, nodeEnum);
        }
    }

    public void computeScorerNodeItemScore(KpiListWrap kpiTypes, boolean submitWithWeigh, BigDecimal itemAutoScore) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }

        Map<String, EmpEvalKpiType> kpiTypeMap = new HashMap<>();
        kpiTypes.getDatas().forEach(type -> kpiTypeMap.put(type.getKpiTypeId(), type));
        if (this.isFinish()) {
            //计算评分人环节分数
            computeScorerNodeItemScore(kpiTypeMap, submitWithWeigh, itemAutoScore);
        }
    }

    public void sumTotalWeight(Map<String, EmpEvalKpiType> kpiTypeMap, Boolean submitWithWght) {
        if (!this.isActive()) {
            return;
        }

        BigDecimal sumWgt = BigDecimal.ZERO;
        Map<String, EvalKpi> kpiMap = new HashMap<>();
        List<EvalScorerNodeKpiItem> scorerNodeKpiItems = new ArrayList<>(this.getAllKpiItems());
        for (EvalScorerNodeKpiItem scoreItem : scorerNodeKpiItems) {
            EmpEvalKpiType type = kpiTypeMap.get(scoreItem.getKpiTypeId());
            if (type.isPlusType() || type.isSubtractType() || type.isPlusSubType() || !type.isEvalScore()) {//+评等级
                continue;
            }
            if (type.isVoteType()) {
                continue;
            }
            type.getItems().forEach(kpi -> kpiMap.put(kpi.asKpiItemKey(), kpi));
            EvalKpi kpi = kpiMap.get(scoreItem.asKpiItemKey());

            if (submitWithWght) {
                scoreItem.setTotalSplitWeight(kpi.getItemWeight().multiply(Pecent.ONE_HUNDRED));//带指标权重打分,当维度100%
            } else {
                scoreItem.setTotalSplitWeight(Pecent.ONE_HUNDRED.multiply(Pecent.ONE_HUNDRED));//不带权重打分,当维度与指标都100%
            }
            sumWgt = sumWgt.add(kpi.getItemWeight().multiply(type.getKpiTypeWeight()));
        }

        for (EvalScorerNodeKpiItem scoreItem : scorerNodeKpiItems) {
            if (scoreItem.getTotalSplitWeight() == null) {
                continue;
            }
            scoreItem.submitSrcScore(this, this.getUpdatedUser());
            BigDecimal score = sumWgt.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : scoreItem.getTotalSplitWeight().multiply(this.getTotalScore()).divide(sumWgt, 2, RoundingMode.HALF_UP);
            scoreItem.setScore(score);
        }
    }

    /**
     * @description: 计算评分人 评分环节
     * 类*指*人
     * @author: suxiaoqiu
     * @date: 2022/5/16 14:32
     * @param: [typePecWgt, itemPecWgt]
     * @return: void
     **/
    public void computeScorerNodeScore(BigDecimal itemAutoScore) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            this.scorerNodeScore = BigDecimal.ZERO;
            return;
        }
        BigDecimal finalScorerNodeScore = BigDecimal.ZERO;
        for (EvalScorerNodeKpiType type : scorerNodeKpiTypes) {
            if (Objects.isNull(type.getScorerNodeKpiItems()) || !type.isPassed()) {
                continue;
            }
            finalScorerNodeScore = finalScorerNodeScore.add(type.computeFinalScorerNodeScore());
        }
        if (Objects.nonNull(itemAutoScore)) {
            finalScorerNodeScore = finalScorerNodeScore.add(itemAutoScore);//加上自动计算指标分
        }
        this.scorerNodeScore = finalScorerNodeScore;
    }

    @JSONField(serialize = false)
    public boolean isOrMode() {
        return "or".equals(multiType);
    }

    public String asOrderAndScorerTypeKey() {
        return String.format("%s_%s", this.scorerType, approvalOrder == null ? 1 : approvalOrder);
    }

    public void setScorerInfo(String scorerName, String scorerAvatar) {
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
        setItemScorerInfo();
    }

    public void accScorerInfo(String scorerId, String scorerName, String scorerAvatar) {
        this.scorerId = scorerId;
        this.scorerName = scorerName;
        this.scorerAvatar = scorerAvatar;
        setItemScorerInfo();
    }

    public void setItemScorerInfo() {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            return;
        }
        scorerNodeKpiTypes.forEach(kpiType -> {
            kpiType.accScorerInfo(scorerId, scorerName, scorerAvatar);
            kpiType.getScorerNodeKpiItems().forEach(kpiItem -> kpiItem.accScorerInfo(scorerId, scorerName, scorerAvatar));
        });
    }

    public void accItemScorerTypeAndOrder() {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            return;
        }
        for (EvalScorerNodeKpiType kpiType : scorerNodeKpiTypes) {
            kpiType.accItemScorerTypeAndOrder(scorerId, scorerType, approvalOrder, status, multiType, transferType, this.isComputeNode(),this.canShowNode());
            kpiType.getScorerNodeKpiItems().forEach(kpiItem -> kpiItem.accItemScorerTypeAndOrder(scorerId, scorerType, approvalOrder, status, multiType, transferType, this.isComputeNode(),this.canShowNode()));
        }
    }

    public void passed() {
        this.status = 2;
        this.handlerTime = new Date(); //已完成，更新处理时间
        //更新指标的状态
        this.upItemStatus(status, handlerTime);
    }

    public void passed(Date handleTime) {
        this.status = 2;
        this.handlerTime = handleTime; //已完成，更新处理时间
        //更新指标的状态
        this.upItemStatus(status, handleTime);

    }

    public void upStatus(Integer status) {
        this.status = status;
        if (isPassed()) {
            this.handlerTime = new Date(); //已完成，更新处理时间
        } else {
            this.handlerTime = null; //如果是待分发，待提交，处理时间更新为NULL
        }
        //更新指标的状态
        this.upItemStatus(status, handlerTime);
    }

    public void upItemStatus(Integer status, Date handlderTime) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            return;
        }
        //更新指标的状态
        scorerNodeKpiTypes.forEach(type -> {
            type.upStatus(status, handlderTime);
            if (CollUtil.isEmpty(type.getScorerNodeKpiItems())) {
                return;
            }
            type.getScorerNodeKpiItems().forEach(item -> item.upStatus(status, handlderTime));
        });
    }

    public void upItemAccpNewScorerWeight(BigDecimal scorerWeight) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes)) {
            return;
        }
        //更新指标的评分人权重
        scorerNodeKpiTypes.forEach(type -> {
            if (CollUtil.isEmpty(type.getScorerNodeKpiItems())) {
                return;
            }
            type.getScorerNodeKpiItems().forEach(item -> item.accpNewScorerWeight(scorerWeight));
        });
    }


    @JSONField(serialize = false)
    public boolean isWaitDispatch() {
        return this.status == 0;
    }

    @JSONField(serialize = false)
    public boolean isPassed() {
        return this.status == 2;
    }

    @JSONField(serialize = false)
    public boolean isSelfNode() {
        return SubScoreNodeEnum.isSelfScore(this.scorerType);
    }

    public void historyPassed(Date handleTime) {
        this.status = 2;
        this.handlerTime = handleTime; //已完成，更新处理时间
        //更新指标的状态
        this.upItemStatus(status, handleTime);
    }

    @JSONField(serialize = false)
    public boolean isDispatched() {
        return this.status != 0;
    }

    public void waitDispatched() {
        this.status = 0;
        this.handlerTime = null;
    }

    @JSONField(serialize = false)
    public boolean isDispatch() {
        return this.status == 1;
    }

    @JSONField(serialize = false)
    public boolean isWaitSubmit() {
        return this.status == 1 ||  this.status == 3;
    }

    public void dispatch() {
        this.status = 1;
        //更新指标的状态
        this.upItemStatus(status, null);
    }

    @JSONField(serialize = false)
    public boolean isCanDispatch(String scorerType, Integer order) {
        return StrUtil.equals(this.scorerType, scorerType) && Objects.equals(this.approvalOrder, order);
    }

    public void waitSubmit() {
        this.status = 1;
        //更新指标的状态
        this.upItemStatus(status, null);
    }

    public void upStatusReject() {
        this.status = 3;
        //更新指标的状态
        this.upItemStatus(status, null);
    }

    public void accpScoredData(List<EvalScoreResult> rs) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes) || CollUtil.isEmpty(rs)) {
            return;
        }

        //去重 rs, 取修改时间最大的
        Map<String, EvalScoreResult> scoreNodeItemRsMap = rs.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        EvalScoreResult::asKpiItemKey,
                        item -> item,
                        (oldItem, newItem) -> {
                            Date oldTime = oldItem.getUpdatedTime();
                            Date newTime = newItem.getUpdatedTime();
                            if (newTime == null) return oldItem;
                            if (oldTime == null) return newItem;
                            return newTime.after(oldTime) ? newItem : oldItem;
                        }
                ));

        scorerNodeKpiTypes.forEach(type -> {
            for (EvalScorerNodeKpiItem kpiItem : type.getScorerNodeKpiItems()) {
                if (scoreNodeItemRsMap.containsKey(kpiItem.asKpiItemKey())) {
                    //接收已经评分完成的结果 指标上 // todo 缺少维度的
                    kpiItem.accpItemScore(scoreNodeItemRsMap.get(kpiItem.asKpiItemKey()));
                }
            }
        });
        //校验 rs 是否全部都是 auditStatus ='pass'
        boolean isAllPass = rs.stream().allMatch(item -> "pass".equals(item.getAuditStatus()));
        if (isAllPass) {
            this.passed(rs.get(0).getUpdatedTime()); //已提交完成，且接收审批通过时间
        } else {
            this.waitSubmit();//已分发，待提交
        }
    }

    public void accpScoreRsTotalScore(ListWrap<EvalScoreResult> scoreResultWrap) {
        String scene = getTotalScene();
        if (StrUtil.isBlank(scene) || scoreResultWrap.isEmpty()) {
            return;
        }
        if (scoreResultWrap.contains(scene) && CollUtil.isNotEmpty(scoreResultWrap.groupGet(scene))) {
            List<EvalScoreResult> totals = scoreResultWrap.groupGet(scene);
            this.accpScorerTotalScore(totals.get(0));
        }
    }

    public String getTotalScene() {
        if (SubScoreNodeEnum.isSelfScore(this.scorerType)) {
            return AuditEnum.TOTAL_SELF_SCORE.getScene();
        }
        if (SubScoreNodeEnum.isSuperioNode(this.scorerType)) {
            return AuditEnum.TOTAL_SUPERIOR_SCORE.getScene();
        }
        if (SubScoreNodeEnum.isPeerNode(this.scorerType)) {
            return AuditEnum.TOTAL_PEER_SCORE.getScene();
        }
        if (SubScoreNodeEnum.isSubNode(this.scorerType)) {
            return AuditEnum.TOTAL_SUB_SCORE.getScene();
        }
        if (SubScoreNodeEnum.isSuperioNode(this.scorerType)) {
            return AuditEnum.TOTAL_SUPERIOR_SCORE.getScene();
        }
        return "";
    }

    public void accpTypeScoredData(List<PerfEvalTypeResult> typeScores) {
        if (CollUtil.isEmpty(scorerNodeKpiTypes) || CollUtil.isEmpty(typeScores)) {
            return;
        }

        ListWrap<PerfEvalTypeResult> typeScoreMap = new ListWrap<>(typeScores).asMap(PerfEvalTypeResult::getKpiTypeId);
        scorerNodeKpiTypes.forEach(type -> {
            if (typeScoreMap.contains(type.getKpiTypeId())) {
                type.accpTypeScoreData(typeScoreMap.mapGet(type.getKpiTypeId()));
            }
        });

        //校验 rs 是否全部都是 auditStatus ='pass'
        boolean isAllPass = typeScores.stream().allMatch(item -> "pass".equals(item.getAuditStatus()));
        if (isAllPass) {
            passed(typeScores.get(0).getUpdatedTime()); //已提交完成，且接收审批通过时间
        } else {
            waitSubmit();//已分发，待提交
        }
    }

    public void accpScorerTotalScoreData(BaseScoreResult scoreResult) {
        if (Objects.isNull(scoreResult)) {
            return;
        }

        this.scoreLevel = scoreResult.getScoreLevel();
        this.scoreAttUrl = scoreResult.getScoreAttUrl();
        this.totalComment = scoreResult.getScoreComment();

        if (scoreResult.isPassed()) {
            passed(scoreResult.getUpdatedTime());//已评价完成
        } else {
            waitSubmit();//已分发，待提交
        }
    }


    public void accpScorerTotalScore(EvalScoreResult scoreResult) {
        //打总分
        if (Objects.isNull(scoreResult)) {
            return;
        }

        this.scoreLevel = scoreResult.getScoreLevel();
        this.scoreAttUrl = scoreResult.getScoreAttUrl();
        this.totalComment = scoreResult.getScoreComment();
        this.totalScore = scoreResult.getScore();

    }

    public void accpScorerSummary(EvalScoreSummary summary) {
        if (Objects.isNull(summary)) {
            return;
        }
        this.estimateLevel = summary.getEstimateLevel();
        String jsonArray = summary.getAttUrl();
        this.scoreAttUrl = JSON.parseArray(jsonArray, ScoreAttUrl.class);
        this.totalComment = summary.getSummary();
    }


    public void accpScorerSubmitScoreNodeData(SubmitedEvalScoreV3 submitNode) {
        //打总分
        this.totalScore = submitNode.getTotalScore();
        this.totalComment = submitNode.getTotalComment();

        if (CollUtil.isNotEmpty(submitNode.getScoreSummarys())) {
            EvalScoreSummary summary = submitNode.getScoreSummarys().get(0);
            if (Objects.nonNull(summary)) {
                this.totalComment = summary.getSummary();
                this.estimateLevel = summary.getEstimateLevel();
                String jsonArray = summary.getAttUrl();
                this.scoreAttUrl = JSON.parseArray(jsonArray, ScoreAttUrl.class);
            }
        }

        // 2. 构建维度映射（kpiTypeId -> EvalScorerNodeKpiType）
        ListWrap<EvalScorerNodeKpiType> kpiTypeWrap = new ListWrap<>(this.scorerNodeKpiTypes).asMap(EvalScorerNodeKpiType::getKpiTypeId);
        //维度评价
        if (CollUtil.isNotEmpty(submitNode.getTypeScores())) {
            submitNode.getTypeScores().forEach(submitKpiType -> {
                EvalScorerNodeKpiType waitSubmitType = kpiTypeWrap.mapGet(submitKpiType.getKpiTypeId());
                if (Objects.isNull(waitSubmitType)){
                    return;
                }
                waitSubmitType.accpScorerSubmitTypeScore(submitKpiType);//接收提交的维度评价
            });
        }

        List<EvalScorerNodeKpiItem> allKpiItems = this.getAllKpiItems();
        ListWrap<EvalScorerNodeKpiItem> kpiItemWrap = new ListWrap<>(allKpiItems).groupBy(EvalScorerNodeKpiItem::asKpiItemKey);

        //3.指标评分
        if (CollUtil.isNotEmpty(submitNode.getItemScoreList())) {
            //接收提交的指标评价
            submitNode.getItemScoreList().forEach(submitKpiItem -> {
                List<EvalScorerNodeKpiItem> waitSubmitItems = kpiItemWrap.groupGet(submitKpiItem.asKpiItemKey());
                if (Objects.isNull(waitSubmitItems)){
                    return;
                }
                waitSubmitItems.forEach(waitSubmitType -> waitSubmitType.accpScorerSubmitItemScore(submitKpiItem));
            });
        }
        passed();//标记评价完成
    }

    public void accpScorerSubmitTotalLevelData(EmpEvalScorerNode submitNode) {
        if (Objects.isNull(submitNode)) {
            return;
        }
        this.scoreLevel = submitNode.getScoreLevel();
        this.scoreAttUrl = submitNode.getScoreAttUrl();
        this.totalComment = submitNode.getTotalComment();
        passed();//标记评价完成
    }

//    public void mergeAndRemoveDuplicates(List<EvalScorerNodeKpiItem> newKpiItems) {
//        Set<String> kpiItemIds = new HashSet<>();
//        List<EvalScorerNodeKpiItem> result = new ArrayList<>();
//        for (EvalScorerNodeKpiItem item : this.scorerNodeKpiItems) {
//            if (item != null && !kpiItemIds.contains(item.asKpiItemKey())) {
//                kpiItemIds.add(item.asKpiItemKey());
//                result.add(item);
//            }
//        }
//        for (EvalScorerNodeKpiItem item : newKpiItems) {
//            if (item != null && !kpiItemIds.contains(item.asKpiItemKey())) {
//                kpiItemIds.add(item.asKpiItemKey());
//                result.add(item);
//            }
//        }
//        this.scorerNodeKpiItems = result;
//    }

    /**
     * 合并并去重KPI类型列表
     * 优化后的去重逻辑，确保业务层面的完整去重
     *
     * @param newKpiTypes 新的KPI类型列表
     */
    public void mergeAndRemoveDuplicates(List<EvalScorerNodeKpiType> newKpiTypes) {
        if (CollUtil.isEmpty(newKpiTypes)) {
            return;
        }

        // 使用业务去重键进行完整去重
        Set<String> allBusinessKeys = new HashSet<>();
        Map<String, EvalScorerNodeKpiType> typeMap = new HashMap<>();
        List<EvalScorerNodeKpiType> result = new ArrayList<>();

        // 首先处理现有的KPI类型，建立去重键集合
        for (EvalScorerNodeKpiType existingType : this.scorerNodeKpiTypes) {
            addAllBusinessKeys(existingType.getScorerNodeKpiItems(), allBusinessKeys);
            typeMap.put(existingType.getKpiTypeId(), existingType);
        }

        // 处理新的KPI类型
        for (EvalScorerNodeKpiType newType : newKpiTypes) {
            String kpiTypeId = newType.getKpiTypeId();

            if (typeMap.containsKey(kpiTypeId)) {
                // 如果类型已存在，需要合并KPI项目并去重
                EvalScorerNodeKpiType existingType = typeMap.get(kpiTypeId);
                List<EvalScorerNodeKpiItem> mergedItems = mergeKpiItems(
                    existingType.getScorerNodeKpiItems(),
                    newType.getScorerNodeKpiItems(),
                    allBusinessKeys
                );
                existingType.setScorerNodeKpiItems(mergedItems);
            } else {
                // 新的类型，直接添加但需要去重其内部项目
                List<EvalScorerNodeKpiItem> deduplicatedItems = deduplicateKpiItems(
                    newType.getScorerNodeKpiItems(),
                    allBusinessKeys
                );
                newType.setScorerNodeKpiItems(deduplicatedItems);
                typeMap.put(kpiTypeId, newType);
                addAllBusinessKeys(deduplicatedItems, allBusinessKeys);
            }
        }

        // 构建最终结果，保持原有顺序
        for (EvalScorerNodeKpiType existingType : this.scorerNodeKpiTypes) {
            if (typeMap.containsKey(existingType.getKpiTypeId())) {
                result.add(typeMap.get(existingType.getKpiTypeId()));
                typeMap.remove(existingType.getKpiTypeId());
            }
        }

        // 添加新的类型
        result.addAll(typeMap.values());

        this.scorerNodeKpiTypes = result;
    }

    /**
     * 批量添加业务去重键到集合中
     *
     * @param items KPI项目列表
     * @param allBusinessKeys 业务去重键集合
     */
    private void addAllBusinessKeys(List<EvalScorerNodeKpiItem> items, Set<String> allBusinessKeys) {
        if (CollUtil.isNotEmpty(items)) {
            items.stream()
                    .filter(item -> item != null && item.asBusinessDeduplicationKey() != null)
                    .map(EvalScorerNodeKpiItem::asBusinessDeduplicationKey)
                    .forEach(allBusinessKeys::add);
        }
    }

    /**
     * 合并两个KPI项目列表并去重
     *
     * @param existingItems 现有项目列表
     * @param newItems 新项目列表
     * @param allBusinessKeys 已存在的业务去重键集合
     * @return 合并去重后的项目列表
     */
    private List<EvalScorerNodeKpiItem> mergeKpiItems(List<EvalScorerNodeKpiItem> existingItems,
                                                      List<EvalScorerNodeKpiItem> newItems,
                                                      Set<String> allBusinessKeys) {
        List<EvalScorerNodeKpiItem> result = new ArrayList<>(existingItems);

        if (CollUtil.isNotEmpty(newItems)) {
            for (EvalScorerNodeKpiItem newItem : newItems) {
                if (newItem != null && newItem.asBusinessDeduplicationKey() != null) {
                    String businessKey = newItem.asBusinessDeduplicationKey();
                    if (!allBusinessKeys.contains(businessKey)) {
                        allBusinessKeys.add(businessKey);
                        result.add(newItem);
                    }
                }
            }
        }

        return result;
    }

    /**
     * 对KPI项目列表进行去重
     *
     * @param items 项目列表
     * @param allBusinessKeys 已存在的业务去重键集合
     * @return 去重后的项目列表
     */
    private List<EvalScorerNodeKpiItem> deduplicateKpiItems(List<EvalScorerNodeKpiItem> items,
                                                           Set<String> allBusinessKeys) {
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        List<EvalScorerNodeKpiItem> result = new ArrayList<>();

        for (EvalScorerNodeKpiItem item : items) {
            if (item != null && item.asBusinessDeduplicationKey() != null) {
                String businessKey = item.asBusinessDeduplicationKey();
                if (!allBusinessKeys.contains(businessKey)) {
                    allBusinessKeys.add(businessKey);
                    result.add(item);
                }
            }
        }

        return result;
    }

    // 批量添加 KPI 指标键到集合中 (保留原方法以兼容其他可能的调用)
    private void addAllKpiItemKeys(List<EvalScorerNodeKpiItem> items, Set<String> allKpiItemIds) {
        if (CollUtil.isNotEmpty(items)) {
            items.stream()
                    .filter(item -> item != null && item.asKpiItemKey() != null)
                    .map(EvalScorerNodeKpiItem::asKpiItemKey)
                    .forEach(allKpiItemIds::add);
        }
    }

    @Override
    public EmpEvalScorerNode clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), EmpEvalScorerNode.class);
    }
}
