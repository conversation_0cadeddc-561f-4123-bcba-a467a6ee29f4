package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.eval.ScoreEmp;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.NodeRaterListWrapV3;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.NodeRaterV3;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.sdk.type.ListWrap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @Author: suxiaoqiu
 * @CreateTime: 2025-04-21  20:19
 */
@Setter
@Getter
@Slf4j
public class EvalScorersWrap extends ListWrap<EmpEvalScorer> {

    private String taskUserId;

    /**
     * 默认构造函数
     */
    public EvalScorersWrap() {
        super();
    }

    /**
     * 带参数构造函数
     * @param scorers 评分人列表
     */
    public EvalScorersWrap(List<EmpEvalScorer> scorers) {
        super(scorers);
    }

    /**
     * 添加评分人节点
     * 这里一个指标一个评分评分人的评分节点，和上面汇总时会有重复 加到empEvalScorers 里
     * 需要处理empEvalScorers 已存在的评分人，或者已存在的环节，只需要把指标添加
     * @param scorerNode 评分人节点
     */
    public void addEvalScorer(EmpEvalScorerNode scorerNode) {
        // 手动实现分组逻辑
        Map<Object, List<EmpEvalScorer>> groupedScorers = new HashMap<>();
        for (EmpEvalScorer scorer : this.datas) {
            groupedScorers.computeIfAbsent(scorer.getScorerId(), k -> new ArrayList<>()).add(scorer);
        }
        // 获取指定 scorerId 的评分人列表
        List<EmpEvalScorer> scorers = groupedScorers.get(scorerNode.getScorerId());
        // 检查是否存在该评分人
        Optional<EmpEvalScorer> existingScorer = Optional.ofNullable(scorers)
                .orElse(Collections.emptyList())
                .stream()
                .filter(scorer -> scorer.getScorerId().equals(scorerNode.getScorerId()))
                .findFirst();

        if (existingScorer.isPresent()) { // 评分人存在，则新增环节
            EmpEvalScorer scorer = existingScorer.get();
            scorer.addScorerNode(scorerNode);
        } else {
            doAddScorer(scorerNode); // 新增评分人和新增评分人环节
        }
    }

    /**
     * 执行添加评分人操作
     * @param scorerNode 评分人节点
     */
    private void doAddScorer(EmpEvalScorerNode scorerNode) {
        EmpEvalScorer scorer = addScorerByScorerNode(scorerNode);
        this.datas.add(scorer);
    }

    /**
     * 根据评分人节点创建评分人对象
     * @param scorerNode 评分人节点
     * @return 评分人对象
     */
    public EmpEvalScorer addScorerByScorerNode(EmpEvalScorerNode scorerNode) {
        EmpEvalScorer scorer = new EmpEvalScorer(
                scorerNode.getCompanyId().getId(),
                scorerNode.getCreatedUser(),
                scorerNode.getTaskId(),
                scorerNode.getEmpId(),
                scorerNode.getTaskUserId(),
                scorerNode.getScorerId(),
                scorerNode.getScorerName(),
                scorerNode.getScorerAvatar()
        );
        scorer.addScorerNode(scorerNode);
        return scorer;
    }

    /**
     * 根据评分人ID筛选需跳过指定类型的评分节点
     * @param scoreId 评分人ID
     * @param skipScoreType 跳过的评分类型
     * @return 需要跳过的评分节点列表
     */
    public List<EmpEvalScorerNode> skipScorerNodeByScorerId(String scoreId,String skipScoreType){
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return nodes;
        }

        for (EmpEvalScorer scorer : this.datas) {
            if (StrUtil.equals(scoreId, scorer.getScorerId())) {
                continue;
            }
            if (scorer.isFinished()) {
                continue;
            }
            List<EmpEvalScorerNode> temps = scorer.getScoreNodesByScoreType(skipScoreType);
            if (CollUtil.isEmpty(temps)) {
                continue;
            }
            temps.stream().filter(scorerNode -> !scorerNode.isFinish()).forEach(nodes::add);
        }
        return nodes;
    }
    public List<EmpEvalScorerNode> listWaitSubmitScoreNode(){
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return nodes;
        }
        for (EmpEvalScorer scorer : this.datas) {
            List<EmpEvalScorerNode> temps = scorer.listWaitSubmitScoreNode();
            if (CollUtil.isEmpty(temps)) {
                continue;
            }
            nodes.addAll(temps);
        }
        return nodes;
    }
    public List<EmpEvalScorerNode> listTotalScoreNode(){
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return nodes;
        }
        for (EmpEvalScorer scorer : this.datas) {
            List<EmpEvalScorerNode> temps = scorer.listTotalScoreNode();
            if (CollUtil.isEmpty(temps)) {
                continue;
            }
            nodes.addAll(temps);
        }
        return nodes;
    }

    /**
     * 排除总等级 的所有环节
     *
     * @return 评分人节点列表
     */
    public ListWrap<EmpEvalScorerNode> listSNExcludeTotalLevel() {
        List<EmpEvalScorerNode> nodes = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return new ListWrap<>(nodes).groupBy(EmpEvalScorerNode::getScorerType);
        }
        for (EmpEvalScorer scorer : this.datas) {
            List<EmpEvalScorerNode> temps = scorer.listSNExcludeTotalLevel();
            if (CollUtil.isEmpty(temps)) {
                continue;
            }
            nodes.addAll(temps);
        }
        return new ListWrap<>(nodes).groupBy(EmpEvalScorerNode::getScorerType);
    }

    public boolean openScorerNode(String scorerType, ListWrap<EmpEvalScorerNode> nodeWrap) {
        if (nodeWrap.isEmpty()) {
            return false;
        }

        List<EmpEvalScorerNode> temps = nodeWrap.groupGet(scorerType);
        return CollUtil.isNotEmpty(temps);
    }


    /**
     * 计算评分人节点分数
     * @param evalMerge 评价合并对象
     * @param taskUser 任务用户
     */
    public void computeScorerNodeScore(EmpEvalMerge evalMerge, EvalUser taskUser) {
        for (EmpEvalScorer scorer : this.datas) {
            if (CollUtil.isEmpty(scorer.getScorerNodes())) {
                continue;
            }
            //环节计算
            scorer.getScorerNodes().forEach(scorerNode -> {
                scorerNode.computeScorerNodeItemScore(evalMerge.getKpiTypes(), evalMerge.getScoreValueConf().submitWithWeight(), taskUser.getFinalItemAutoScore());
            });
            scorer.upScorerStatus();//修改状态
        }
    }

    /**
     * 转换评分人结果
     * @param empScoreRs 指标评分结果列表
     * @param typeResultOfUser 维度评价结果列表
     * @param totalRs 打总等级结果列表
     * @param summarys 评价总结列表
     */
    public void corventScorerRs(List<EvalScoreResult> empScoreRs,List<PerfEvalTypeResult> typeResultOfUser,
                                List<BaseScoreResult> totalRs,List<EvalScoreSummary> summarys) {
        //1.先处理打等级的评价的
        convertTotal(totalRs);

        //2.处理维度评价的
        convertType(typeResultOfUser);

        //3.处理指标评价的
        convertEmpScore(empScoreRs);

        //4.处理评价总结
        convertSummary(summarys);
    }

    /**
     * 转换总分结果
     * @param totalRs 总分结果列表
     */
    private void convertTotal(List<BaseScoreResult> totalRs){
        if (CollUtil.isEmpty(totalRs)) {
            return;
        }
        ListWrap<BaseScoreResult> totalWrap = new ListWrap<>(totalRs).asMap(BaseScoreResult::getScorerId);
        //1.先处理打等级的评价的
        for (EmpEvalScorer empEvalScorer : this.datas) {
            BaseScoreResult scoreResult = totalWrap.mapGet(empEvalScorer.getScorerId());
            if (Objects.isNull(scoreResult)) {
                continue;
            }
            empEvalScorer.accpTotalRs(scoreResult);//接收打等级的评价
        }
    }

    /**
     * 转换评价总结
     * @param summarys 评价总结列表
     */
    private void convertSummary(List<EvalScoreSummary> summarys){
        if (CollUtil.isEmpty(summarys)) {
            return;
        }
        ListWrap<EvalScoreSummary> totalWrap = new ListWrap<>(summarys).asMap(EvalScoreSummary::getCreatedUser);
        //处理评价评语
        for (EmpEvalScorer empEvalScorer : this.datas) {
            EvalScoreSummary summary = totalWrap.mapGet(empEvalScorer.getScorerId());
            if (Objects.isNull(summary)) {
                continue;
            }
            empEvalScorer.accpSummary(summary);//接收评价评语
        }
    }

    /**
     * 转换维度评价结果
     * @param typeResultOfUser 维度评价结果列表
     */
    private void convertType(List<PerfEvalTypeResult> typeResultOfUser){
        //评分人，评分环节为空，则退出
        if (CollUtil.isEmpty(typeResultOfUser)) {
            return;
        }
        //2.处理维度评价的
        ListWrap<PerfEvalTypeResult> typeWrap = new ListWrap<>(typeResultOfUser).groupBy(PerfEvalTypeResult::getScorerId);
        for (EmpEvalScorer empEvalScorer : this.datas) {
            List<PerfEvalTypeResult> typeResults = typeWrap.groupGet(empEvalScorer.getScorerId());
            if (CollUtil.isEmpty(typeResults)) {
                continue;
            }
            ListWrap<PerfEvalTypeResult> typeScoreType = new ListWrap<>(typeResults).groupBy(PerfEvalTypeResult::asOrderAndScoreTypeKey);
            empEvalScorer.accpTypeRs(typeScoreType);
        }
    }

    /**
     * 转换员工评分结果
     * @param empScoreRs 员工评分结果列表
     */
    private void convertEmpScore(List<EvalScoreResult> empScoreRs){
        //评分人，评分环节为空，则退出
        if (Objects.isNull(empScoreRs)) {
            return;
        }
        //3.处理指标评价的
        ListWrap<EvalScoreResult> scoresWrap = new ListWrap<>(empScoreRs).groupBy(EvalScoreResult::getScorerId);
        for (EmpEvalScorer empEvalScorer : this.datas) {
            List<EvalScoreResult> scoreResults = scoresWrap.groupGet(empEvalScorer.getScorerId());
            if (CollUtil.isEmpty(scoreResults)) {
                continue;
            }

            ListWrap<EvalScoreResult> empScoreGroup = new ListWrap<>(scoreResults);
            empEvalScorer.accpScoreRs(empScoreGroup);
        }
    }

    /**
     * 分发评分人（通过节点过滤器）
     * @param nodeFilter 节点过滤器
     * @return 分发的评分人列表
     */
    public List<EmpEvalScorer> dispatchScorer(NodeFilter nodeFilter) {
        //分发评分人的评分环节
        return dispatchScorer(nodeFilter.getNode(), nodeFilter.getNodeOrder());
    }

    /**
     * 分发评分人（通过节点和顺序）
     * @param node 评分节点枚举
     * @param order 节点顺序
     * @return 分发的评分人列表
     */
    public List<EmpEvalScorer> dispatchScorer(SubScoreNodeEnum node, int order) {
        List<EmpEvalScorer> dispatchEmpEvalScorers = new ArrayList<>();
        //分发评分人的评分环节
        for (EmpEvalScorer empEvalScorer : this.datas) {
            if (empEvalScorer.match(node, order)) {
                boolean isDispatch = empEvalScorer.dispatchByScoreType(node.getScene(), order);//是被分发才可返回
                if (isDispatch) {
                    dispatchEmpEvalScorers.add(empEvalScorer);
                }
            }
        }
        return dispatchEmpEvalScorers;
    }

    /**
     * 存在自评被驳回
     * @return true - 存在， false- 不存在
     */
    public boolean selfAlreadyRejected(){
        return this.datas.stream()
                .anyMatch(EmpEvalScorer::selfAlreadyRejected);
    }

    /**
     * 存在自评已完成
     * @return true - 完成， false- 未完成
     */
    public boolean selfIsFinished(String empId) {
        EmpEvalScorer selfScorer = this.getCurEvalScorer(empId);
        if (Objects.isNull(selfScorer)) {
            return false;
        }
        return selfScorer.selfFinished();
    }

    public List<EmpEvalScorer> toWaitScoreEmp(String empId, String scoreType) {
        List<EmpEvalScorer> scores = new ArrayList<>();
        EmpEvalScorer curEvalScorer = this.getCurEvalScorer(empId);
        List<EmpEvalScorerNode> curWaitScoreNodes = curEvalScorer.getScoreNodesByScoreType(scoreType);
        boolean isWait = false;
        for (EmpEvalScorerNode scorerNode : curWaitScoreNodes) {
            if (scorerNode.isWaitSubmit()) {
                scorerNode.waitDispatched(); //修改为待分发，驳回后当前上级节点状态改为待分发
                isWait = true;
            }
        }
        if (isWait) {
            scores.add(curEvalScorer);
        }
        return scores;
    }

    /**
     * 判断V3节点是否结束
     * @param node 评分节点枚举
     * @param nodeOrder 节点顺序
     * @return 节点是否结束
     */
    public boolean v3NodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        boolean isEnd = true;
        if (this.datas.isEmpty()) {
            return isEnd;
        }
        for (EmpEvalScorer empEvalScorer : this.datas) {
            if (empEvalScorer.isFinished()) {
                continue;
            }

            boolean nodeIsFinished = empEvalScorer.nodeIsFinished(node, nodeOrder);  // 如果节点匹配且未完成，则返回false
            if (!nodeIsFinished) {
                return false;//存在未完成的
            }
        }
        return isEnd;
    }

    /**
     * 标记或模式评分节点（批量处理）
     * @param submitNodeScores 提交的节点评分列表
     * @return 标记的评分人列表
     */
    public List<EmpEvalScorer> markOrModeScoreNs(List<V3SubmitedEvalNodeScore> submitNodeScores) {
        Map<String,EmpEvalScorer> scorerMap = new HashMap<>();
        if (this.datas.isEmpty() || CollUtil.isEmpty(submitNodeScores)) {
            return new ArrayList<>();
        }
        //处理或签的评分人  //更新同级主结点或签方式子结点
        submitNodeScores.forEach(nodeScore -> {
            if (nodeScore.isOrMode()) {
                List<EmpEvalScorer> tempOrs = this.markOrModeScoreNs(nodeScore);
                if (CollUtil.isNotEmpty(tempOrs)) {
                    tempOrs.forEach(s -> scorerMap.put(s.getScorerId(), s)); //使用Map,避免 重复添加同一个评分人，一个评分人人可以有不同环节
                }
            }
        });

        return new ArrayList<>(scorerMap.values());
    }

    /**
     * 标记或模式评分节点（单个处理）
     * @param submitNode 提交的评分人节点
     * @return 标记的评分人列表
     */
    public List<EmpEvalScorer> markOrModeScoreNs(EmpEvalScorerNode submitNode) {
        List<EmpEvalScorer> orRs = new ArrayList<>();
        if (this.datas.isEmpty() || Objects.isNull(submitNode) || !submitNode.isOrMode() || submitNode.isSkipHiddenType()) {
            return new ArrayList<>();
        }
        //更新同级主结点或签方式子结点
        for (EmpEvalScorer scorer : this.getDatas()) {
            if (scorer.isFinished()) {
                continue;
            }
            //和评分人是同一个人退出【当前提交人已经提交了】
            if (StrUtil.equals(scorer.getScorerId(),submitNode.getScorerId())){
                continue;
            }
            boolean isMarkFinish = scorer.markOrModeScoreNs(submitNode.getScorerType(), submitNode.getApprovalOrder());
            if (isMarkFinish) {
                scorer.upScorerStatus();//如果评分人环节都完成了，那么则更新评分人状态为已完成
                orRs.add(scorer);
            }
        }
        return orRs;
    }

    /**
     * 获取当前评分人
     * @param scorerId 评分人ID
     * @return 评分人对象，如果不存在则返回null
     */
    public EmpEvalScorer getCurEvalScorer(String scorerId) {
        if (this.datas.isEmpty()) {
            return null;
        }
        return this.datas.stream().filter(scorer -> scorer.getScorerId().equals(scorerId)).findFirst().orElse(null);
    }

    /**
     * 根据评分类型获取已完成的评分人
     * @param scoreType 评分类型
     * @return 已完成的评分人列表
     */
    public List<EmpEvalScorer> finishedScorerByScoreType(String scoreType) {
        List<EmpEvalScorer> finishedScorers = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return finishedScorers;
        }

        for (EmpEvalScorer scorer : this.datas) {
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                if (!StrUtil.equals(scoreType, node.getScorerType())) {
                    continue;
                }
                if (node.isPassed()) {
                    finishedScorers.add(scorer);
                    break;
                }
            }
        }
        return finishedScorers;
    }

    /**
     * 根据评分类型获取已完成的评分人节点
     * @param scoreType 评分类型
     * @return 已完成的评分人节点列表
     */
    public List<EmpEvalScorerNode> finishedScorerNodeByScoreType(String scoreType) {
        List<EmpEvalScorerNode> finishedScorerNodes = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return finishedScorerNodes;
        }

        for (EmpEvalScorer scorer : this.datas) {
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                if (!StrUtil.equals(scoreType, node.getScorerType())) {
                    continue;
                }
                if (node.isPassed()) {
                    finishedScorerNodes.add(node);
                }
            }
        }
        return finishedScorerNodes;
    }

    /**
     * 根据评分类型和KPI项目列表获取自定义评分人
     * @param scoreType 评分类型
     * @param otherScorerKpiItems 其他评分人KPI项目列表
     * @return 自定义评分人列表
     */
    public List<EmpEvalScorer> listCustomScorerByScoreType(String scoreType,List<EvalScorerNodeKpiItem> otherScorerKpiItems) {
        List<EmpEvalScorer> scorers = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return scorers;
        }
        for (EmpEvalScorer scorer : this.datas) {
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                if (!StrUtil.equals(scoreType, node.getScorerType())) {
                    continue;
                }

                //指标存在任意一个，就添加到需要同一指标评分的环节评分人
                if (node.getAllKpiItems().stream().anyMatch(item -> otherScorerKpiItems.stream().anyMatch(otherItem -> otherItem.asKpiItemKey().equals(item.asKpiItemKey())))) {
                    scorers.add(scorer);
                    break;
                }
            }
        }
        return scorers;
    }

    /**
     * 根据评分类型获取评分节点列表
     * @param scorerType 评分类型
     * @return 评分节点列表
     */
    public List<EmpEvalScorerNode> listSNByScoreType(String scorerType) {
        List<EmpEvalScorerNode> sn = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return sn;
        }
        for (EmpEvalScorer scorer : this.datas) {
            scorer.getScorerNodes().stream().filter(node -> node.isComputeNode() && node.getScorerType().equals(scorerType))
                    .forEach(sn::add);
        }
        return sn;
    }

    /**
     * 根据评分类型获取评分节点列表
     * @return 评分节点列表
     */
    public List<EmpEvalScorerNode> allScorerNodes() {
        List<EmpEvalScorerNode> sn = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return sn;
        }
        for (EmpEvalScorer scorer : this.datas) {
            scorer.getScorerNodes().stream().filter(EmpEvalScorerNode::isFlowNode).forEach(sn::add);
        }
        return sn;
    }

    /**
     * 获取接收评分的评分人列表（排除指定评分人ID和顺序）
     * @param scorerId 要排除的评分人ID
     * @param scoreType 评分类型
     * @param order 评分顺序
     * @return 符合条件的评分人列表
     */
    public List<EmpEvalScorer> listReceiveExcludeScorerIdAndOrder(String scorerId,String scoreType, Integer order) {
        List<EmpEvalScorer> scorers = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return scorers;
        }

        for (EmpEvalScorer scorer : this.datas) {
            boolean matched = false;
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                if(node.needExcludeNode()){//排除不符合的节点
                    continue;
                }
                if (node.match(scoreType, order)) {
                    if (!StrUtil.equals(scorer.getScorerId(), scorerId)) {
                        matched = true;
                        break;
                    }
                }
            }
            if (matched) {
                scorers.add(scorer);
            }
        }
        return scorers;
    }

    /**
     * 获取接收评分的评分人列表（排除指定评分类型和顺序）
     * @param scoreType 要排除的评分类型
     * @param order 要排除的评分顺序
     * @return 符合条件的评分人列表
     */
    public List<EmpEvalScorer> listReceiveExcludeScoreTypeAndOrder(String scoreType, Integer order) {
        List<EmpEvalScorer> scorers = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return scorers;
        }
        for (EmpEvalScorer scorer : this.datas) {
            for (EmpEvalScorerNode node : scorer.getScorerNodes()) {
                if (node.needExcludeNode()) {//排除不符合的节点
                    continue;
                }
                if (!StrUtil.equals(node.getScorerType(), scoreType)) {
                    continue;
                }
                if (node.getApprovalOrder().compareTo(order) == 0) {
                    continue;
                }
                scorers.add(scorer);
                break;
            }
        }
        return scorers;
    }

    /**
     * 获取评分人节点类型集合
     * @return 评分人节点类型集合
     */
    public Set<String> getEvalScorerNodeTypes() {
        Set<String> scoreTypes;
        if (this.datas.isEmpty()) {
            return new HashSet<>();
        }
        scoreTypes = this.datas.stream().flatMap(empEvalScorer -> empEvalScorer.getScoreTypes().stream()).collect(Collectors.toSet());
        return scoreTypes;
    }

    /**
     * 获取所有员工评价评分人节点
     * @return 所有评分人节点列表
     */
    public List<EmpEvalScorerNode> empEvalScorerNodesAll() {
        if (this.datas.isEmpty()) {
            return new ArrayList<>();
        }
        return this.datas.stream().flatMap(scorer -> scorer.getScorerNodes().stream()).collect(Collectors.toList());
    }

    public List<EvalScorerNodeKpiItem> empEvalScorerNodesKpiItemAll() {
        if (this.datas.isEmpty()) {
            return new ArrayList<>();
        }
        List<EvalScorerNodeKpiItem> allScorerNodeKpiItemAlls = new ArrayList<>();
        for (EmpEvalScorer scorer : this.datas) {
            scorer.getScorerNodes().forEach(scorerNode -> {
                scorerNode.accItemScorerTypeAndOrder();//接收ScoreTypeAndOrder
                allScorerNodeKpiItemAlls.addAll(scorerNode.getAllKpiItems());
            });
        }
        return allScorerNodeKpiItemAlls;
    }
    public List<EmpEvalScorer> resetScoreEmp(List<ScoreEmp> scoreEmps) {
        List<EmpEvalScorer> tempEmpEvalScorers = new ArrayList<>();//评分人
        if (this.datas.isEmpty()) {
            return tempEmpEvalScorers;
        }

        this.datas.stream().<Consumer<? super ScoreEmp>>map(scorer -> scoreEmp -> {
            boolean isReset = scorer.resetScoreNode(scoreEmp);//评分人重置
            if (isReset) {
                tempEmpEvalScorers.add(scorer);
            }
        }).forEach(scoreEmps::forEach);
        return tempEmpEvalScorers;
    }

    public List<EmpEvalScorer> rejectScoreNode(List<ScoreEmp> scoreEmps) {
        List<EmpEvalScorer> tempEmpEvalScorers = new ArrayList<>();//评分人
        if (this.datas.isEmpty()) {
            return tempEmpEvalScorers;
        }

        this.datas.stream().<Consumer<? super ScoreEmp>>map(scorer -> scoreEmp -> {
            boolean isReset = scorer.rejectScoreNode(scoreEmp);//评分人重置驳回
            if (isReset) {
                tempEmpEvalScorers.add(scorer);
            }
        }).forEach(scoreEmps::forEach);
        return tempEmpEvalScorers;
    }


    public List<EmpEvalScorer> resetScoreTotalLevel() {
        List<EmpEvalScorer> tempEmpEvalScorers = new ArrayList<>();//评分人
        if (this.datas.isEmpty()) {
            return tempEmpEvalScorers;
        }

        for (EmpEvalScorer scorer : this.datas) {
            boolean isReset = false;
            for (EmpEvalScorerNode scorerNode : scorer.getScorerNodes()) {
                isReset = scorerNode.resetScoreTotalLevel();
            }
            if (isReset) {
                scorer.upScorerStatus();
                tempEmpEvalScorers.add(scorer);
            }
        }
        return tempEmpEvalScorers;
    }

    public void resetScoreStage() {
        if (this.datas.isEmpty()) {
            return;
        }
        for (EmpEvalScorer scorer : this.datas) {
            scorer.resetScoreNodeForWaitDispatch();
        }
    }



    public List<EmpEvalScorer> dispatchByScoreType(ScoreNode scoreNode) {
        List<EmpEvalScorer> dispatchEmpEvalScorers = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return dispatchEmpEvalScorers;
        }
        //分发评分人的评分环节
        this.datas.stream().filter(empEvalScorer -> empEvalScorer.match(scoreNode.getNode(), scoreNode.getOrder())).forEach(empEvalScorer -> {
            boolean isDispatch = empEvalScorer.dispatchByScoreType(scoreNode.getNode().getScene(), scoreNode.getOrder());
            if (isDispatch) {
                dispatchEmpEvalScorers.add(empEvalScorer);
            }
        });
        return dispatchEmpEvalScorers;
    }

    public ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap() {
        ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap = new ListWrap<>();
        List<EvalScorerNodeKpiItem> scorerNodeKpiItemAlls = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return nodeKpiItemWrap;
        }
        for (EmpEvalScorer scorer : this.datas) {
            scorerNodeKpiItemAlls.addAll(scorer.listAllKpiItem());
        }
        if (CollUtil.isNotEmpty(scorerNodeKpiItemAlls)) {
            nodeKpiItemWrap.setDatas(scorerNodeKpiItemAlls);
            nodeKpiItemWrap.groupBy(EvalScorerNodeKpiItem::asKpiItemKey);
        }
        return nodeKpiItemWrap;
    }

    public ListWrap<EvalScorerNodeKpiType> nodeKpiTypeWrap() {
        ListWrap<EvalScorerNodeKpiType> nodeKpiTypeWrap = new ListWrap<>();
        List<EvalScorerNodeKpiType> scorerNodeKpiTypeAlls = new ArrayList<>();
        if (this.datas.isEmpty()) {
            return nodeKpiTypeWrap;
        }

        for (EmpEvalScorer scorer : this.datas) {
            scorerNodeKpiTypeAlls.addAll(scorer.getTypeScoreNode());
        }
        if (CollUtil.isNotEmpty(scorerNodeKpiTypeAlls)) {
            nodeKpiTypeWrap.setDatas(scorerNodeKpiTypeAlls);
            nodeKpiTypeWrap.groupBy(EvalScorerNodeKpiType::getKpiTypeId);
        }
        return nodeKpiTypeWrap;
    }

    public EmpEvalScorer submitScoreNs(String scorerId, List<V3SubmitedEvalNodeScore> nodeScores, EvalUser evalUser, EmpEvalMerge empEval,
                                       Map<String, EmpEvalKpiType> kpiTypeMap) {
        if (this.datas.isEmpty()) {
            return null;
        }
        EmpEvalScorer scorer = this.getCurEvalScorer(scorerId);
        boolean submitWithWeight = empEval.getScoreValueConf().submitWithWeight();
        BigDecimal askScore = sumAskScore(empEval);//360问卷分
        BigDecimal extraScore = BaseFinalNodeScoreV3.addNullZero(askScore,  evalUser.getFinalItemAutoScore()); //自动计算分+ 360问卷分 评分环节得分需要加上
        //计算评分人环节得分【未乘以环节权重】//计算评分人环节分[需加上自动计算指标分]
        nodeScores.forEach(node -> {
            log.info("=====.EvalOnScoreStage.submitScoreNs.opEmpId:{},scoreType:{}", scorerId, node.getScorerType());
            node.upStatus(2);//变更状态为已提交
            if (node.isCanComputeScore()) { //自评跳过不进行计算得分,及跳过的节点也不参与计算得分
                node.sumTotalWeight(kpiTypeMap, submitWithWeight);//处理打总分的
                node.computeScorerNodeItemScore(kpiTypeMap, submitWithWeight, extraScore);
            }
            List<EmpEvalScorerNode> curScorerNodes = scorer.getCurScoreNode(node.getScorerType(), node.getApprovalOrder());
            if (CollUtil.isEmpty(curScorerNodes)) {
                return;
            }
            ListWrap<EmpEvalScorerNode> groupNode = new ListWrap<>(curScorerNodes).asMap(EmpEvalScorerNode::getId);
            EmpEvalScorerNode curScorerNode = groupNode.mapGet(node.getId());
            if (Objects.isNull(curScorerNode)){
                return;
            }
            curScorerNode.submitScoreNs(node);
        });
        scorer.upScorerStatus();
        this.accEvalScorerNodeKpiItemForItem(kpiTypeMap);
        return scorer;
    }

    public BigDecimal sumAskScore(EmpEvalMerge empEval) {
        List<EmpEvalKpiType> askTypes = empEval.listAsk360Types();
        if (CollUtil.isEmpty(askTypes)) {
            return BigDecimal.ZERO;
        }

        // 使用带初始容量的 Map 提升性能
        Map<String, BigDecimal> askEvalScoreMap = new HashMap<>(askTypes.size());
        for (EmpEvalKpiType askType : askTypes) {
            BigDecimal score = askType.getAsk360EvalScore();
            if (Objects.nonNull(score)) {
                askEvalScoreMap.put(askType.getKpiTypeId(), score);
            }
        }

        if (CollUtil.isEmpty(askEvalScoreMap)) {
            return BigDecimal.ZERO;
        }

        BigDecimal askEvalScore = BigDecimal.ZERO;
        for (EmpEvalKpiType askType : askTypes) {
            BigDecimal score = askEvalScoreMap.get(askType.getKpiTypeId());
            BigDecimal weight = askType.pecWeight();
            if (Objects.nonNull(score) && Objects.nonNull(weight)) {
                askEvalScore = askEvalScore.add(score.multiply(weight));
            }
        }

        // 保留4位小数，使用 HALF_UP 方式舍入
        return askEvalScore.setScale(5, RoundingMode.HALF_UP);
    }

    public void addExtraNodeScore(BigDecimal extraScore) {
        if (this.datas.isEmpty()) {
            return;
        }
        //extraScore包含了360问卷分
        for (EmpEvalScorer scorer : this.datas) {
            scorer.getScorerNodes().stream().filter(EmpEvalScorerNode::isFinish).forEach(scorerNode -> scorerNode.computeScorerNodeScore(extraScore));
        }
    }

    public void reComputeScorerNodeItemScore(Map<String, EmpEvalKpiType> kpiTypeMap, boolean submitWithWeigh) {
        if (this.datas.isEmpty()) {
            return;
        }
        //extraScore包含了360问卷分
        for (EmpEvalScorer scorer : this.datas) {
            scorer.getScorerNodes().stream().filter(EmpEvalScorerNode::isFinish).forEach(scorerNode -> scorerNode.reComputeScorerNodeItemScore(kpiTypeMap,submitWithWeigh));
        }
    }


    //仅所有指标互评人相同才会调用
    public void recomputeUseDelMinMax(SubScoreNodeEnum node) {
        if (this.datas.isEmpty()) {
            return;
        }

        List<EmpEvalScorerNode> scorerNodes = new ArrayList<>();
        this.datas.forEach(scorer -> scorer.getScorerNodes().stream()
                .filter(scorerNode -> StrUtil.equals(scorerNode.getScorerType(), node.getScene()) && (scorerNode.isComputeNode()))
                .forEach(scorerNodes::add));

        List<NodeRaterV3> nodeRaterV3s = new ArrayList<>();
        ListWrap<EmpEvalScorerNode> nodeWrap = new ListWrap<>(scorerNodes).groupBy(EmpEvalScorerNode::getScorerId);
        Set<String> scorerIds = nodeWrap.groupKeySet();
        int raterSize = scorerIds.size();
        if (raterSize <= 3) {//如果互评人数小于3则不需要处理
            return;
        }
        for (String scorerId : scorerIds) {
            NodeRaterV3 nodeRater = new NodeRaterV3(scorerId);
            List<EmpEvalScorerNode> nodes = nodeWrap.groupGet(scorerId);
            List<EvalScorerNodeKpiItem> nodeKpiItems = nodes.stream().map(EmpEvalScorerNode::getAllKpiItems).flatMap(List::stream).collect(Collectors.toList());
            nodeRater.addAllScoreRs(nodeKpiItems);//收集互评人评分信息Rs
            nodeRaterV3s.add(nodeRater);
        }

        NodeRaterListWrapV3 raterWap = new NodeRaterListWrapV3(nodeRaterV3s);
        raterWap.recomputeUseDelMinMax();//去高去低
    }

    //仅所有指标互评人相同才会调用
//    public void recomputeUseDelMinMax(SubScoreNodeEnum node, List<Rater> raters) {
//        NodeRaterListWrapV3 raterWap = new NodeRaterListWrapV3(raters);
//        for (NodeRaterV3 nodeRater : raterWap.getDatas()) {
//            EmpEvalScorer scorer =  getCurEvalScorer(nodeRater.getScorerId());
//            nodeRater.addAllScoreRs(scorer.listItemScore(node));//收集互评人评分信息Rs
//        }
//        raterWap.recomputeUseDelMinMax();
//    }

    public void accEvalScorerNodeKpiItemForItem(Map<String, EmpEvalKpiType> kpiTypeMap) {
        ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap = this.nodeKpiItemWrap();
        if (nodeKpiItemWrap.isEmpty()) {
            return;
        }
        //循环kpiTypeMap
        for (Map.Entry<String, EmpEvalKpiType> entry : kpiTypeMap.entrySet()) {
            EmpEvalKpiType type = entry.getValue();
            //tempItems 筛选出isPassed
            type.getItems().stream().filter(item -> !nodeKpiItemWrap.isEmpty()).forEach(item -> {
                List<EvalScorerNodeKpiItem> tempItems = nodeKpiItemWrap.groupGet(item.asKpiItemKey());
                item.setWaitScores(tempItems);//评价环节各个评分人当前评价指标
                item.setV3AlreadyScorerNodes(tempItems.stream().filter(EvalScorerNodeKpiItem::isPassed).collect(Collectors.toList()));
            });

            if (type.isEvalLevel()) {
                ListWrap<EvalScorerNodeKpiType> nodeKpiTypeWrap = this.nodeKpiTypeWrap();
                if (nodeKpiTypeWrap.isEmpty()) {
                    continue;
                }
                List<EvalScorerNodeKpiType> tempTypes = nodeKpiTypeWrap.groupGet(type.getKpiTypeId());
                if (CollUtil.isEmpty(tempTypes)) {
                    continue;
                }
                type.setWaitScores(tempTypes);//评价环节各个评分人当前评价指标
                type.setAlreadyScoreV3(tempTypes.stream().filter(EvalScorerNodeKpiType::isPassed).collect(Collectors.toList()));
            }
        }
    }
}
