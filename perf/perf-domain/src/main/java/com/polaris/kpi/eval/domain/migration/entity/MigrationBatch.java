package com.polaris.kpi.eval.domain.migration.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.ArrayList;

/**
 * 数据迁移批次实体
 * 表示一批需要迁移的数据记录
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MigrationBatch {

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 页码
     */
    private int pageNumber;

    /**
     * 页大小
     */
    private int pageSize;

    /**
     * 迁移记录列表
     */
    private List<MigrationRecord> records;

    /**
     * 批次创建时间
     */
    private java.time.LocalDateTime createTime;

    /**
     * 批次大小（实际记录数）
     */
    private int actualSize;

    /**
     * 是否为最后一批
     */
    private boolean isLastBatch;

    /**
     * 构造函数
     * 
     * @param companyId 公司ID
     * @param pageNumber 页码
     * @param pageSize 页大小
     */
    public MigrationBatch(String companyId, int pageNumber, int pageSize) {
        this.companyId = companyId;
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.records = new ArrayList<>();
        this.createTime = java.time.LocalDateTime.now();
        this.batchId = generateBatchId();
    }

    /**
     * 添加迁移记录
     * 
     * @param record 迁移记录
     */
    public void addRecord(MigrationRecord record) {
        if (this.records == null) {
            this.records = new ArrayList<>();
        }
        this.records.add(record);
        this.actualSize = this.records.size();
    }

    /**
     * 批量添加迁移记录
     * 
     * @param records 迁移记录列表
     */
    public void addRecords(List<MigrationRecord> records) {
        if (this.records == null) {
            this.records = new ArrayList<>();
        }
        this.records.addAll(records);
        this.actualSize = this.records.size();
    }

    /**
     * 检查批次是否为空
     * 
     * @return 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取批次大小
     * 
     * @return 批次大小
     */
    public int size() {
        return records != null ? records.size() : 0;
    }

    /**
     * 检查批次是否已满
     * 
     * @return 是否已满
     */
    public boolean isFull() {
        return size() >= pageSize;
    }

    /**
     * 获取剩余容量
     * 
     * @return 剩余容量
     */
    public int getRemainingCapacity() {
        return Math.max(0, pageSize - size());
    }

    /**
     * 生成批次ID
     * 
     * @return 批次ID
     */
    private String generateBatchId() {
        return String.format("BATCH_%s_%d_%d_%d", 
                companyId, pageNumber, pageSize, System.currentTimeMillis());
    }

    /**
     * 获取批次统计信息
     * 
     * @return 统计信息
     */
    public String getStatistics() {
        return String.format("Batch[%s]: Company=%s, Page=%d/%d, Records=%d, Created=%s",
                batchId, companyId, pageNumber, pageSize, size(), createTime);
    }

    /**
     * 验证批次数据
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return companyId != null && !companyId.trim().isEmpty()
                && pageNumber > 0
                && pageSize > 0
                && records != null;
    }

    /**
     * 清空批次数据
     */
    public void clear() {
        if (records != null) {
            records.clear();
        }
        actualSize = 0;
    }

    /**
     * 创建空批次
     * 
     * @param companyId 公司ID
     * @param pageNumber 页码
     * @param pageSize 页大小
     * @return 空批次
     */
    public static MigrationBatch empty(String companyId, int pageNumber, int pageSize) {
        MigrationBatch batch = new MigrationBatch(companyId, pageNumber, pageSize);
        batch.setLastBatch(true);
        return batch;
    }

    /**
     * 创建批次构建器
     * 
     * @return 批次构建器
     */
    public static BatchBuilder builder() {
        return new BatchBuilder();
    }

    /**
     * 批次构建器
     */
    public static class BatchBuilder {
        private String companyId;
        private int pageNumber;
        private int pageSize;
        private List<MigrationRecord> records = new ArrayList<>();
        private boolean isLastBatch = false;

        public BatchBuilder companyId(String companyId) {
            this.companyId = companyId;
            return this;
        }

        public BatchBuilder pageNumber(int pageNumber) {
            this.pageNumber = pageNumber;
            return this;
        }

        public BatchBuilder pageSize(int pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public BatchBuilder records(List<MigrationRecord> records) {
            this.records = records != null ? records : new ArrayList<>();
            return this;
        }

        public BatchBuilder addRecord(MigrationRecord record) {
            if (record != null) {
                this.records.add(record);
            }
            return this;
        }

        public BatchBuilder isLastBatch(boolean isLastBatch) {
            this.isLastBatch = isLastBatch;
            return this;
        }

        public MigrationBatch build() {
            MigrationBatch batch = new MigrationBatch(companyId, pageNumber, pageSize);
            batch.setRecords(records);
            batch.setActualSize(records.size());
            batch.setLastBatch(isLastBatch);
            return batch;
        }
    }
}
