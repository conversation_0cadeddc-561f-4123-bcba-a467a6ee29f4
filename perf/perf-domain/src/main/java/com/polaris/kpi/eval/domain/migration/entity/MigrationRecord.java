package com.polaris.kpi.eval.domain.migration.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * 数据迁移记录实体
 * 表示单条需要迁移的数据记录
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationRecord {

    /**
     * 记录ID（主键）
     */
    private String id;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 记录类型
     */
    private String recordType;

    /**
     * 源数据（JSON格式）
     */
    private String sourceData;

    /**
     * 目标数据（JSON格式）
     */
    private String targetData;

    /**
     * 数据长度（字符数）
     */
    private Integer dataLength;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 迁移状态
     */
    private MigrationStatus status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;

    /**
     * 数据校验和（用于验证数据完整性）
     */
    private String checksum;

    /**
     * 优先级（数字越小优先级越高）
     */
    private Integer priority;

    /**
     * 迁移状态枚举
     */
    public enum MigrationStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败"),
        SKIPPED("跳过"),
        RETRY("重试中");

        private final String description;

        MigrationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     * 
     * @param id 记录ID
     * @param companyId 公司ID
     * @param userId 用户ID
     * @param sourceData 源数据
     */
    public MigrationRecord(String id, String companyId, String userId, String sourceData) {
        this.id = id;
        this.companyId = companyId;
        this.userId = userId;
        this.sourceData = sourceData;
        this.status = MigrationStatus.PENDING;
        this.retryCount = 0;
        this.maxRetryCount = 3;
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.attributes = new HashMap<>();
        this.priority = 0;
        
        // 计算数据长度
        if (sourceData != null) {
            this.dataLength = sourceData.length();
        }
    }

    /**
     * 标记为处理中
     */
    public void markAsProcessing() {
        this.status = MigrationStatus.PROCESSING;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 标记为成功
     * 
     * @param targetData 目标数据
     */
    public void markAsSuccess(String targetData) {
        this.status = MigrationStatus.SUCCESS;
        this.targetData = targetData;
        this.updateTime = LocalDateTime.now();
        this.errorMessage = null;
    }

    /**
     * 标记为失败
     * 
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = MigrationStatus.FAILED;
        this.errorMessage = errorMessage;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 标记为跳过
     * 
     * @param reason 跳过原因
     */
    public void markAsSkipped(String reason) {
        this.status = MigrationStatus.SKIPPED;
        this.errorMessage = reason;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 增加重试次数
     * 
     * @return 是否可以继续重试
     */
    public boolean incrementRetry() {
        this.retryCount++;
        this.status = MigrationStatus.RETRY;
        this.updateTime = LocalDateTime.now();
        return this.retryCount <= this.maxRetryCount;
    }

    /**
     * 检查是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount && 
               (this.status == MigrationStatus.FAILED || this.status == MigrationStatus.RETRY);
    }

    /**
     * 检查是否已完成（成功或跳过）
     * 
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this.status == MigrationStatus.SUCCESS || this.status == MigrationStatus.SKIPPED;
    }

    /**
     * 检查是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return this.status == MigrationStatus.FAILED;
    }

    /**
     * 检查是否正在处理
     * 
     * @return 是否正在处理
     */
    public boolean isProcessing() {
        return this.status == MigrationStatus.PROCESSING || this.status == MigrationStatus.RETRY;
    }

    /**
     * 设置扩展属性
     * 
     * @param key 属性键
     * @param value 属性值
     */
    public void setAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new HashMap<>();
        }
        this.attributes.put(key, value);
    }

    /**
     * 获取扩展属性
     * 
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return this.attributes != null ? this.attributes.get(key) : null;
    }

    /**
     * 获取扩展属性（带默认值）
     * 
     * @param key 属性键
     * @param defaultValue 默认值
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        Object value = getAttribute(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 计算数据校验和
     * 
     * @return 校验和
     */
    public String calculateChecksum() {
        if (sourceData == null) {
            return null;
        }
        
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(sourceData.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            this.checksum = sb.toString();
            return this.checksum;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证数据完整性
     * 
     * @return 是否完整
     */
    public boolean validateIntegrity() {
        if (checksum == null) {
            calculateChecksum();
        }
        
        String currentChecksum = calculateChecksum();
        return checksum != null && checksum.equals(currentChecksum);
    }

    /**
     * 获取记录统计信息
     * 
     * @return 统计信息
     */
    public String getStatistics() {
        return String.format("Record[%s]: Company=%s, User=%s, Status=%s, Length=%d, Retry=%d/%d",
                id, companyId, userId, status.getDescription(), 
                dataLength != null ? dataLength : 0,
                retryCount, maxRetryCount);
    }

    /**
     * 验证记录数据
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return id != null && !id.trim().isEmpty()
                && companyId != null && !companyId.trim().isEmpty()
                && sourceData != null
                && status != null
                && retryCount != null && retryCount >= 0
                && maxRetryCount != null && maxRetryCount >= 0;
    }

    /**
     * 重置记录状态
     */
    public void reset() {
        this.status = MigrationStatus.PENDING;
        this.retryCount = 0;
        this.errorMessage = null;
        this.targetData = null;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 创建记录副本
     * 
     * @return 记录副本
     */
    public MigrationRecord copy() {
        return MigrationRecord.builder()
                .id(this.id)
                .companyId(this.companyId)
                .userId(this.userId)
                .recordType(this.recordType)
                .sourceData(this.sourceData)
                .targetData(this.targetData)
                .dataLength(this.dataLength)
                .createTime(this.createTime)
                .updateTime(LocalDateTime.now())
                .status(MigrationStatus.PENDING)
                .retryCount(0)
                .maxRetryCount(this.maxRetryCount)
                .errorMessage(null)
                .attributes(this.attributes != null ? new HashMap<>(this.attributes) : new HashMap<>())
                .checksum(this.checksum)
                .priority(this.priority)
                .build();
    }
}
