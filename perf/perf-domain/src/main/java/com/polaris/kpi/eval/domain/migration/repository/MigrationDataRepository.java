package com.polaris.kpi.eval.domain.migration.repository;

// 注意：这个接口应该被删除或重命名，因为我们要基于现有的OnScoreEvalRepo进行优化

import com.polaris.kpi.eval.domain.migration.entity.MigrationBatch;
import com.polaris.kpi.eval.domain.migration.entity.MigrationRecord;

import java.util.List;

/**
 * 数据迁移数据仓储接口
 * 负责迁移数据的查询和处理操作
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
public interface MigrationDataRepository {

    /**
     * 获取总公司数量
     * 
     * @param migrationType 迁移类型
     * @return 总公司数量
     */
    int getTotalCompanyCount(String migrationType);

    /**
     * 获取总用户数量
     * 
     * @param migrationType 迁移类型
     * @return 总用户数量
     */
    long getTotalUserCount(String migrationType);

    /**
     * 分页获取公司ID列表
     * 
     * @param migrationType 迁移类型
     * @param page 页码（从1开始）
     * @param pageSize 页大小
     * @return 公司ID列表
     */
    List<String> getCompanyIdsByPage(String migrationType, int page, int pageSize);

    /**
     * 获取指定公司的用户数据批次
     * 
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @param page 页码（从1开始）
     * @param pageSize 页大小
     * @return 用户数据批次
     */
    MigrationBatch getUserBatch(String migrationType, String companyId, int page, int pageSize);

    /**
     * 执行单条记录的迁移
     * 
     * @param record 迁移记录
     * @param migrationType 迁移类型
     * @return 是否迁移成功
     */
    boolean migrateRecord(MigrationRecord record, String migrationType);

    /**
     * 批量执行记录迁移
     * 
     * @param records 迁移记录列表
     * @param migrationType 迁移类型
     * @return 迁移结果统计
     */
    MigrationResult batchMigrateRecords(List<MigrationRecord> records, String migrationType);

    /**
     * 检查记录是否已经迁移
     * 
     * @param recordId 记录ID
     * @param migrationType 迁移类型
     * @return 是否已迁移
     */
    boolean isRecordAlreadyMigrated(String recordId, String migrationType);

    /**
     * 获取指定公司的用户总数
     * 
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @return 用户总数
     */
    long getUserCountByCompany(String migrationType, String companyId);

    /**
     * 获取需要迁移的记录ID列表
     * 
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @param limit 限制数量
     * @return 记录ID列表
     */
    List<String> getPendingRecordIds(String migrationType, String companyId, int limit);

    /**
     * 根据记录ID获取迁移记录
     * 
     * @param recordIds 记录ID列表
     * @param migrationType 迁移类型
     * @return 迁移记录列表
     */
    List<MigrationRecord> getRecordsByIds(List<String> recordIds, String migrationType);

    /**
     * 更新记录状态
     * 
     * @param recordId 记录ID
     * @param status 新状态
     * @param errorMessage 错误信息（可选）
     * @return 是否更新成功
     */
    boolean updateRecordStatus(String recordId, MigrationRecord.MigrationStatus status, String errorMessage);

    /**
     * 批量更新记录状态
     * 
     * @param records 记录列表
     * @return 更新成功的数量
     */
    int batchUpdateRecordStatus(List<MigrationRecord> records);

    /**
     * 获取失败的记录列表
     * 
     * @param migrationType 迁移类型
     * @param companyId 公司ID（可选）
     * @param limit 限制数量
     * @return 失败记录列表
     */
    List<MigrationRecord> getFailedRecords(String migrationType, String companyId, int limit);

    /**
     * 重置失败记录的状态
     * 
     * @param recordIds 记录ID列表
     * @return 重置成功的数量
     */
    int resetFailedRecords(List<String> recordIds);

    /**
     * 获取迁移进度统计
     * 
     * @param migrationType 迁移类型
     * @param companyId 公司ID（可选）
     * @return 进度统计
     */
    MigrationProgressStats getProgressStats(String migrationType, String companyId);

    /**
     * 验证数据完整性
     * 
     * @param migrationType 迁移类型
     * @param sampleSize 抽样大小
     * @return 验证结果
     */
    DataIntegrityResult validateDataIntegrity(String migrationType, int sampleSize);

    /**
     * 清理已完成的迁移数据
     * 
     * @param migrationType 迁移类型
     * @param daysToKeep 保留天数
     * @return 清理的记录数
     */
    int cleanupCompletedData(String migrationType, int daysToKeep);

    /**
     * 获取迁移性能指标
     * 
     * @param migrationType 迁移类型
     * @param timeRangeMinutes 时间范围（分钟）
     * @return 性能指标
     */
    PerformanceMetrics getPerformanceMetrics(String migrationType, int timeRangeMinutes);

    /**
     * 迁移结果统计内部类
     */
    class MigrationResult {
        private int totalRecords;
        private int successCount;
        private int failureCount;
        private int skipCount;
        private long processingTimeMs;
        private List<String> failedRecordIds;
        private String errorSummary;

        // Constructors
        public MigrationResult() {}

        public MigrationResult(int totalRecords, int successCount, int failureCount, int skipCount) {
            this.totalRecords = totalRecords;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.skipCount = skipCount;
        }

        // Getters and Setters
        public int getTotalRecords() { return totalRecords; }
        public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }

        public int getSkipCount() { return skipCount; }
        public void setSkipCount(int skipCount) { this.skipCount = skipCount; }

        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }

        public List<String> getFailedRecordIds() { return failedRecordIds; }
        public void setFailedRecordIds(List<String> failedRecordIds) { this.failedRecordIds = failedRecordIds; }

        public String getErrorSummary() { return errorSummary; }
        public void setErrorSummary(String errorSummary) { this.errorSummary = errorSummary; }

        public double getSuccessRate() {
            return totalRecords > 0 ? (double) successCount / totalRecords * 100.0 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("MigrationResult{total=%d, success=%d, failure=%d, skip=%d, successRate=%.2f%%, time=%dms}",
                    totalRecords, successCount, failureCount, skipCount, getSuccessRate(), processingTimeMs);
        }
    }

    /**
     * 迁移进度统计内部类
     */
    class MigrationProgressStats {
        private long totalRecords;
        private long processedRecords;
        private long successRecords;
        private long failedRecords;
        private long skippedRecords;
        private double progressPercentage;
        private double successRate;

        // Getters and Setters
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }

        public long getProcessedRecords() { return processedRecords; }
        public void setProcessedRecords(long processedRecords) { this.processedRecords = processedRecords; }

        public long getSuccessRecords() { return successRecords; }
        public void setSuccessRecords(long successRecords) { this.successRecords = successRecords; }

        public long getFailedRecords() { return failedRecords; }
        public void setFailedRecords(long failedRecords) { this.failedRecords = failedRecords; }

        public long getSkippedRecords() { return skippedRecords; }
        public void setSkippedRecords(long skippedRecords) { this.skippedRecords = skippedRecords; }

        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }

        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }

    /**
     * 数据完整性验证结果内部类
     */
    class DataIntegrityResult {
        private boolean isValid;
        private int totalSamples;
        private int validSamples;
        private int invalidSamples;
        private List<String> errorMessages;

        // Getters and Setters
        public boolean isValid() { return isValid; }
        public void setValid(boolean valid) { isValid = valid; }

        public int getTotalSamples() { return totalSamples; }
        public void setTotalSamples(int totalSamples) { this.totalSamples = totalSamples; }

        public int getValidSamples() { return validSamples; }
        public void setValidSamples(int validSamples) { this.validSamples = validSamples; }

        public int getInvalidSamples() { return invalidSamples; }
        public void setInvalidSamples(int invalidSamples) { this.invalidSamples = invalidSamples; }

        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }

    /**
     * 获取用户ID列表（分页）
     *
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @param page 页码
     * @param pageSize 页大小
     * @return 用户ID列表
     */
    List<String> getUserIdsByPage(String migrationType, String companyId, int page, int pageSize);

    /**
     * 迁移单个用户记录
     *
     * @param companyId 公司ID
     * @param userId 用户ID
     * @param migrationType 迁移类型
     * @return 是否成功
     */
    boolean migrateUserRecord(String companyId, String userId, String migrationType);

    /**
     * 获取公司在全局索引中的起始位置
     *
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @return 起始索引
     */
    long getCompanyStartIndex(String migrationType, String companyId);

    /**
     * 性能指标内部类
     */
    class PerformanceMetrics {
        private double recordsPerSecond;
        private double averageProcessingTimeMs;
        private long totalProcessedRecords;
        private long totalProcessingTimeMs;
        private double memoryUsageMB;
        private double cpuUsagePercent;

        // Getters and Setters
        public double getRecordsPerSecond() { return recordsPerSecond; }
        public void setRecordsPerSecond(double recordsPerSecond) { this.recordsPerSecond = recordsPerSecond; }

        public double getAverageProcessingTimeMs() { return averageProcessingTimeMs; }
        public void setAverageProcessingTimeMs(double averageProcessingTimeMs) { this.averageProcessingTimeMs = averageProcessingTimeMs; }

        public long getTotalProcessedRecords() { return totalProcessedRecords; }
        public void setTotalProcessedRecords(long totalProcessedRecords) { this.totalProcessedRecords = totalProcessedRecords; }

        public long getTotalProcessingTimeMs() { return totalProcessingTimeMs; }
        public void setTotalProcessingTimeMs(long totalProcessingTimeMs) { this.totalProcessingTimeMs = totalProcessingTimeMs; }

        public double getMemoryUsageMB() { return memoryUsageMB; }
        public void setMemoryUsageMB(double memoryUsageMB) { this.memoryUsageMB = memoryUsageMB; }

        public double getCpuUsagePercent() { return cpuUsagePercent; }
        public void setCpuUsagePercent(double cpuUsagePercent) { this.cpuUsagePercent = cpuUsagePercent; }
    }
}
