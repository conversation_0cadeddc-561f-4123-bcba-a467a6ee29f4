package com.polaris.kpi.eval.domain.task.entity.empeval;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.ObjItem;
import cn.com.polaris.kpi.eval.*;
import cn.com.polaris.kpi.temp.PubExcutor;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Editor;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.invite.InviteItem;
import com.polaris.kpi.eval.domain.task.entity.flow.AsDisplayFlowRs;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.domain.temp.entity.PerfTemplKpiItem;
import com.polaris.kpi.eval.domain.temp.entity.std.StdTemp;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: lufei
 * @CreateTime: 2022-11-24  09:00
 * @Version: 流程控制+支持分数计算 指标2+公式4+规则8+基本信息1
 */
@Getter
@Setter
@Slf4j
public class EmpEvalMerge extends BaseEmpEvalRule implements AsCycleRule, AsAdminTaskRule, AsTableRule {
    // mod:1=分类,2=指标,4=指标规则,8=指标公式,16=评分结果,32=任务,64=周期 128=公司配置,512=评分结点流程
    public static final int all = 1 + 2 + 4 + 8 + 16 + 32 + 64 + 128 + 512;
    public static final int beforeScore = 1 + 2 + 4 + 32 + 64 + 128;
    public static final int type = 1;
    public static final int item = 1 + 2;
    public static final int itemRule = 1 + 2 + 4;
    public static final int itemF = 1 + 8;
    public static final int itemRs = 1 + 16;
    public static final int cycleRule = 1 + 32;
    public static final int company = 128;
    public static final int audit = 512;


    public EmpEvalMerge() {
    }

    private String taskId;
    private String taskName;
    //private KpiListWrap kpiTypes;
    private BigDecimal systemFullScore;
    private BigDecimal customFullScore;         //自定义满分分值 如果为null 则使用系统提供分值
    private boolean notAllowExceed;             //总分不能超出满分分值  {true不能超出},{默认false可以超出}、
    private boolean submitWithWeight;           //提交带权重分
    private CompanyConf companyConf;                     //公司级配置
    private BigDecimal finalScore;               //计算的最后得分
    //private PerfTemplEvaluate tmpEvalFlow;         //评分规则(非标准定义，兼容数据)
    //private ScoreAuditsWrap scoreAudits;
    private ScoreChain scoreChain;

    private ListWrap<EvalScoreResult> totalScoreRs;
    private List<EvalScoreResult> totalEvalRs;

    private BigDecimal v3FinalScore;               //v3版计算的最后得分
   // private List<EmpEvalScorer> empEvalScorers;//评分人

    private EvalScorersWrap evalScorersWrap;//评分人

    private Boolean fromOldTask = Boolean.FALSE;//管理任务转化而来
    private Boolean wasTransferRater = false; //是否转交的评分人

    public void accpEvalScorersWrap(List<EmpEvalScorer> empEvalScorers) {
        this.evalScorersWrap = new EvalScorersWrap(empEvalScorers);
    }

    public void addExtraNodeScore(BigDecimal extraScore) {
        if (this.evalScorersWrap.isEmpty() || null == extraScore || BigDecimal.ZERO.compareTo(extraScore) == 0) {
            return;
        }
        this.evalScorersWrap.addExtraNodeScore(extraScore);
    }

    public void reComputeScorerNodeItemScore() {
        Map<String, EmpEvalKpiType> kpiTypeMap = new HashMap<>();
        for (EmpEvalKpiType type : this.kpiTypes.getDatas()) {
            kpiTypeMap.put(type.getKpiTypeId(), type);
        }
        this.evalScorersWrap.reComputeScorerNodeItemScore(kpiTypeMap,this.scoreValueConf.submitWithWeight());
    }

    @Override
    public boolean inputOnScoring() {
        return companyConf.openInputOnScoring();
    }


    @Override
    public boolean openItemAutoScoreMultiplWeight() {
        return companyConf.openItemAutoScoreMultiplWeight();
    }

    public boolean sendInputMsg() {
        return companyConf.isEnableResultInputSendMsg();
    }

    public List<EmpEvalScorerNode> empEvalScorerNodesAll() {
        return evalScorersWrap.empEvalScorerNodesAll();
    }

    public EmpEvalScorer getCurEvalScorer(String scorerId) {
        return evalScorersWrap.getCurEvalScorer(scorerId);
    }

    public Set<String> listEvalScorerNodeTypes() {
        return evalScorersWrap.getEvalScorerNodeTypes();
    }

    //评分环节的配置
    public RaterNodeConf nodeRule(String scene) {
        if (isCustom()) {
            RaterNodeConf raterNodeConf = kpiTypes.nodeRule(scene);
            if (raterNodeConf == null) {
                return new RaterNodeConf("false", "item", null, true);
            }
            raterNodeConf.setRateMode("item");
            return raterNodeConf;
        }
        return super.nodeRule(scene);
    }

    public RaterNodeConf selfConf() {
        return nodeRule(SubScoreNodeEnum.SELF_SCORE.getScene());
    }

    public RaterNodeConf peerConf() {
        return this.nodeRule(SubScoreNodeEnum.PEER_SCORE.getScene());
    }

    public RaterNodeConf subConf() {
        return nodeRule(SubScoreNodeEnum.SUB_SCORE.getScene());
    }

    public RaterNodeConf supperConf() {
        return nodeRule(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
    }

    public boolean checkFinishValueReqIsNull(boolean openInputOnScoring) {
        if (kpiTypes.isEmpty() || CollUtil.isEmpty(kpiTypes.getDatas())) {
            return false;
        }

        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            if (CollUtil.isEmpty(type.getItems())) {
                continue;
            }
            KpiSyncReqConf reqConf = type.getTypeKpiSyncReqConf();
            for (EvalKpi kpi : type.getItems()) {
                if (openInputOnScoring && kpi.isAutoItem()) {
                    return false;//自动计算指标，值为空不校验，【运营后台开启了评分阶段可以录入完成值】
                }
                if (openInputOnScoring) {
                    return false;//【运营后台开启了评分阶段可以录入完成值】
                }
                if("no".equals(kpi.getResultInputType())){
                    return false; //无需录入不校验
                }
                if (Objects.nonNull(reqConf) && Objects.nonNull(reqConf.getReqItems()) && !kpi.checkPassedByTypeReq(reqConf)) {
                    return true;//维度必填配置校验值存在为空的
                }
                if (!kpi.checkPassedByKpiItemFinishValue()) {
                    return true;//指标必填配置校验值存在空的
                }
            }
        }
        return false;
    }

    public void checkReSubmited(List<EvalScoreResult> toSubmits, EvalScoreResult total) {
        kpiTypes.initWeight(typeWeightConf, scoreValueConf.submitWithWeight());//归一化类别权重
        ListWrap<EvalScoreResult> toSubmitWrap = new ListWrap<>(toSubmits).asMap(EvalScoreResult::getKpiItemId);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            type.checkReSubmited(toSubmitWrap, total);
        }
    }

    //计算指标级的分数
    public List<EvalScoreResult> submitAndComputeItem(List<EvalScoreResult> submitedScores, EvalScoreResult total) {
        kpiTypes.initWeight(typeWeightConf, scoreValueConf.submitWithWeight());//归一化类别权重
        kpiTypes.sumTotalWeight(submitedScores, total, scoreValueConf.submitWithWeight());//打总分权重加总
        ListWrap<EvalScoreResult> submits = new ListWrap<>(submitedScores).asMap(EvalScoreResult::getKpiItemId);
        List<EvalScoreResult> computedRs = new ArrayList<>();

        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            List<EvalScoreResult> computeItemScores = type.submitItemScore(submits, scoreValueConf.submitWithWeight());
            computedRs.addAll(computeItemScores);
        }
        //计算指标等级
        submitAndComputeItemLevel(computedRs);
        return computedRs;
    }


    //计算指标级的分数
    public List<EvalScoreResult> initSubmitItemWeight(List<EvalScoreResult> submitedScores, EvalScoreResult total) {
        kpiTypes.initWeight(typeWeightConf, scoreValueConf.submitWithWeight());//归一化类别权重
        kpiTypes.sumTotalWeight(submitedScores, total, scoreValueConf.submitWithWeight());//打总分权重加总
        ListWrap<EvalScoreResult> submits = new ListWrap<>(submitedScores).asMap(EvalScoreResult::getKpiItemId);
        List<EvalScoreResult> computedRs = new ArrayList<>();

        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            List<EvalScoreResult> computeItemScores = type.submitItemScore(submits, scoreValueConf.submitWithWeight());
            computedRs.addAll(computeItemScores);
        }
        //计算指标等级
        submitAndComputeItemLevel(computedRs);
        return computedRs;
    }

    public List<PerfEvalTypeResult> submitAndComputeTypeLevel(List<PerfEvalTypeResult> typeScores) {
        List<PerfEvalTypeResult> computes = new ArrayList<>();
        ListWrap<EmpEvalKpiType> typeListWrap = new ListWrap<>(kpiTypes.getDatas()).asMap(type1 -> type1.getKpiTypeId());
        for (PerfEvalTypeResult typeScore : typeScores) {
            EmpEvalKpiType type = typeListWrap.mapGet(typeScore.getKpiTypeId());
            PerfEvalTypeResult computedType = type.submitTypeScore(typeScore, typeScore.getUpdatedUser());
            computes.add(computedType);
        }
        return computes;
    }

    public List<EvalScoreResult> submitAndComputeItemLevel(List<EvalScoreResult> submitRs) {
        ListWrap<EmpEvalKpiType> typeListWrap = new ListWrap<>(kpiTypes.getDatas()).asMap(type1 -> type1.getKpiTypeId());
        for (EvalScoreResult itemScore : submitRs) {
            EmpEvalKpiType type = typeListWrap.mapGet(itemScore.getKpiTypeId());
            for (EvalKpi item : type.getItems()) {
                item.needUpdateLevel(itemScore);
            }
        }
        return submitRs;
    }

    public List<EvalScoreResult> submitAndComputeItemLevelV3(List<EvalScoreResult> submitRs) {
        ListWrap<EmpEvalKpiType> typeListWrap = new ListWrap<>(kpiTypes.getDatas()).asMap(type1 -> type1.getKpiTypeId());
        for (EvalScoreResult itemScore : submitRs) {
            EmpEvalKpiType type = typeListWrap.mapGet(itemScore.getKpiTypeId());
            for (EvalKpi item : type.getItems()) {
                item.needUpdateLevelV3(itemScore);
            }
        }
        return submitRs;
    }


    public ChainDispatchRs resetScoreEmp(List<ScoreEmp> scoreEmps) {
        List<PerfEvalTypeResult> typeResults = new ArrayList<>();
        ListWrap<EvalScoreResult> scoreResults = new ListWrap<>();
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            scoreResults.addAll(type.resetScoreEmp(scoreEmps));
            typeResults.addAll(type.resetTypeScoreEmp(scoreEmps));
        }
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs(typeResults, scoreResults);
        chainDispatchRs.setEmpEvalScorers(evalScorersWrap.resetScoreEmp(scoreEmps));//评分人
        return chainDispatchRs;
    }


    public ChainDispatchRs resetScoreEmpV3(List<ScoreEmp> scoreEmps) {
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs();
        chainDispatchRs.setEmpEvalScorers(evalScorersWrap.resetScoreEmp(scoreEmps));//评分人
        return chainDispatchRs;
    }

    public ChainDispatchRs rejectScoreNode(List<ScoreEmp> scoreEmps) {
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs();
        chainDispatchRs.setEmpEvalScorers(evalScorersWrap.rejectScoreNode(scoreEmps));//驳回评分人
        return chainDispatchRs;
    }


    public ChainDispatchRs toWaitScoreEmp(String opEmpId, String fromScoreType) {
        List<PerfEvalTypeResult> typeResults = new ArrayList<>();
        ListWrap<EvalScoreResult> scoreResults = new ListWrap<>();
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            scoreResults.addAll(type.toWaitScoreEmp(opEmpId,fromScoreType));
            typeResults.addAll(type.toWaitTypeScoreEmp(opEmpId,fromScoreType));
        }
        return new ChainDispatchRs(typeResults, scoreResults);
    }

    public ChainDispatchRs toWaitScoreEmpV3(String opEmpId, String fromScoreType) {
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs();
        Set<String> dispatchScoreTypes = new HashSet<>();
        dispatchScoreTypes.add(fromScoreType);
        chainDispatchRs.acceptScorers(dispatchScoreTypes, evalScorersWrap.toWaitScoreEmp(opEmpId,fromScoreType));
        return chainDispatchRs;
    }

    public List<EvalScoreResult> computePlusSubMaxScore() {
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            if (type.isPlusSubType()) {
                scoreResults.addAll(type.computePlusSubMaxScore());
            }
            if (type.isPlusType() && type.hasMaxExtraScore()) {
                scoreResults.addAll(type.computePlusMaxScore());
            }
            if (type.isSubtractType() && type.hasMaxExtraScore()) {
                scoreResults.addAll(type.computeSubMaxScore());
            }
        }
        return scoreResults;
    }

    //计算最后综合考核分数
    public FinalWeightSumScore computeFinalScore(BigDecimal finalAutoSum,boolean isOpenAvgWeightCompute) {
        return this.computeFinalScore(finalAutoSum, EvalScoreResult::getFinalWeightScore,isOpenAvgWeightCompute);
    }

    public FinalWeightSumScore computeFinalScore(BigDecimal finalAutoSum, Function<EvalScoreResult, BigDecimal> getScoreFunc, boolean isOpenAvgWeightCompute){
        return computeFinalScore(finalAutoSum,getScoreFunc,getScoreFunc,isOpenAvgWeightCompute);
    }
    //计算最后综合考核分数
    public FinalWeightSumScore computeFinalScore(BigDecimal finalAutoSum,
                                                 Function<EvalScoreResult, BigDecimal> getScoreFunc, Function<EvalScoreResult, BigDecimal> getMutualScoreFunc,
                                                 boolean isOpenAvgWeightCompute) {
        FinalWeightSumScore totalOfSum = new FinalWeightSumScore(getScoreFunc,getMutualScoreFunc);
        totalOfSum.setFinalAutoSum(finalAutoSum);
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            type.computeTypeFinalScore(totalOfSum,isOpenAvgWeightCompute);
        }
        totalOfSum.scaleSubtractSum();
        totalOfSum.addToSum(scoreValueConf.nullBaseScore()); //基准分
        this.finalScore = totalOfSum.sum();
        handleVetoTheScore();       //存在一票否决后的分数
        if (this.finalScore != null) {
            //制定不超出上满分
            this.finalScore = scoreValueConf.finalScoreLimit(systemFullScore, finalScore);
//            this.finalScore = !this.scoreValueConf.getExceedFullScore() ? finalScore :
//                    (finalScore.compareTo(this.scoreValueConf.getCustomFullScore()) == 1 ? this.scoreValueConf.getCustomFullScore() : finalScore);
        }
        return totalOfSum;
    }


    public void accEvalScorerForItem() {
        ListWrap<EvalScorerNodeKpiItem> nodeKpiItemWrap = evalScorersWrap.nodeKpiItemWrap();
        ListWrap<EvalScorerNodeKpiType> nodeKpiTypeWrap = evalScorersWrap.nodeKpiTypeWrap();
        for (EmpEvalKpiType type : getKpiTypes().getDatas()) {
            if (!nodeKpiTypeWrap.isEmpty()) {//维度评价等级
                List<EvalScorerNodeKpiType> scorerNodeKpiTypes = nodeKpiTypeWrap.groupGet(type.getKpiTypeId());
                type.setWaitScores(scorerNodeKpiTypes);
                type.setAlreadyScoreV3(scorerNodeKpiTypes.stream().filter(EvalScorerNodeKpiType::isPassed).collect(Collectors.toList()));
            }

            //tempItems 筛选出isPassed
            for (EvalKpi kpi : type.getItems()) {
                if (!nodeKpiItemWrap.isEmpty()) {
                    List<EvalScorerNodeKpiItem> tempItems = nodeKpiItemWrap.groupGet(kpi.asKpiItemKey());
                    kpi.setWaitScores(tempItems);//评价环节各个评分人当前评价指标
                    kpi.setV3AlreadyScorerNodes(tempItems.stream().filter(EvalScorerNodeKpiItem::isPassed).collect(Collectors.toList()));
                }
            }
        }
    }

    @JSONField(serialize = false)
    public boolean isSameTime() {
        return scoreSortConf.isSameTime();
    }

    @JSONField(serialize = false)
    public boolean isSuperSameTime() {
        return Objects.nonNull(this.superiorScoreOrder) && "sameTime".equals(this.superiorScoreOrder);
    }

    public ChainNode current(SubScoreNodeEnum curNode, int subOrder) {
        return scoreChain.current(curNode, subOrder);
    }

    public ScoreNode currentScoreNoe(SubScoreNodeEnum curNode, int subOrder) {
        return scoreChain.currentScoreNoe(curNode, subOrder);
    }

    public ChainNode next(SubScoreNodeEnum curNode, int subOrder) {
        return scoreChain.next(curNode, subOrder);
    }

    public ChainDispatchRs dispatchChainNode(ChainNode chainNode, String selfEmpId,boolean isOpenAvgWeightCompute) {
        if (chainNode.isTotalLevel() && needSubmitTotalEval()) {
            if (CollUtil.isEmpty(getTotalLevelRaters())) {
                return new ChainDispatchRs();
            }
            if (CollUtil.isNotEmpty(super.totalLevelResults)) {
                super.totalLevelResults.forEach(BaseScoreResult::doDispatched);
                return new ChainDispatchRs(totalLevelResults);
            }
            List<BaseScoreResult> totalRaterRs = getTotalLevelRaters().stream().map(rater -> {
                return new BaseScoreResult(companyId, empEvalId, rater.getEmpId());
            }).collect(Collectors.toList());
            return new ChainDispatchRs(totalRaterRs);
        }

        log.info("===isOpenAvgWeightCompute:{}", isOpenAvgWeightCompute);
        ListWrap<EvalScoreResult> rs = new ListWrap<>();
        List<PerfEvalTypeResult> typeRs = new ArrayList<>();
        List<EmpEvalScorer> dispatchEmpEvalScorers = new ArrayList<>();
        for (ScoreNode scoreNode : chainNode.getScoreNodes()) {
            NodeFilter nodeFilter = new NodeFilter(scoreNode.getNode(), scoreNode.getOrder());
            List<EvalScoreResult> normalRs = kpiTypes.dispatchIf(nodeFilter, isOpenAvgWeightCompute);//分发指标评分记录
            rs.addAll(normalRs);
            List<PerfEvalTypeResult> typeResults = kpiTypes.dispatchTypeIf(nodeFilter, selfEmpId, isOpenAvgWeightCompute);//分发维度评分记录
            typeRs.addAll(typeResults);

            if (evalScorersWrap.isEmpty()) {
                continue;
            }
            //分发评分人的评分环节
            dispatchEmpEvalScorers.addAll(evalScorersWrap.dispatchScorer(nodeFilter));
        }
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs(typeRs, rs);
        chainDispatchRs.setEmpEvalScorers(dispatchEmpEvalScorers);
        return chainDispatchRs;
    }

    public ChainDispatchRs dispatchChainNodeV3(ChainNode chainNode,boolean isOpenAvgWeightCompute) {
        if (Objects.isNull(chainNode) || evalScorersWrap.isEmpty()){
            return new ChainDispatchRs();
        }
        List<EmpEvalScorer> dispatchEmpEvalScorers = new ArrayList<>();
        Set<String> dispatchScoreTypes = new HashSet<>();
        ChainDispatchRs chainDispatchRs = new ChainDispatchRs();
        if (chainNode.isTotalLevel() && needSubmitTotalEval()) {
            //分发总等级评分人的评分环节
            log.info("===[dispatchChainNodeV3]分发总等级:{}", isOpenAvgWeightCompute);
            dispatchEmpEvalScorers.addAll(evalScorersWrap.dispatchScorer(SubScoreNodeEnum.TOTAL_LEVEL,1));
            dispatchScoreTypes.add(SubScoreNodeEnum.TOTAL_LEVEL.getScene());
            chainDispatchRs.acceptScorers(dispatchScoreTypes,dispatchEmpEvalScorers);
            return chainDispatchRs;
        }

        //普通环节
        for (ScoreNode scoreNode : chainNode.getScoreNodes()) {
            log.info("===[dispatchChainNodeV3]分发scoreNode:node:{},order:{}", scoreNode.getNode(),  scoreNode.getOrder());
            NodeFilter nodeFilter = new NodeFilter(scoreNode.getNode(), scoreNode.getOrder());
            //分发评分人的评分环节
            dispatchEmpEvalScorers.addAll(evalScorersWrap.dispatchScorer(nodeFilter));
            dispatchScoreTypes.add(scoreNode.getNode().getScene());
        }
        chainDispatchRs.acceptScorers(dispatchScoreTypes,dispatchEmpEvalScorers);
        return chainDispatchRs;
    }
    public List<EmpEvalScorerNode> handleTransferAutoSubmit(ChainDispatchRs typeDispatchRs) {
        List<EmpEvalScorerNode> sysAutoSubmitSn = new ArrayList<>();
        // 判断当前分发的评分 在上级是否评分过
        if (typeDispatchRs.hasNoneScorerRs()) {
            return sysAutoSubmitSn;
        }

        sysAutoSubmitSn = handleFilterTransferAutoSubmit(typeDispatchRs.getEmpEvalScorers());
        return sysAutoSubmitSn;
    }

    public List<EmpEvalScorerNode> handleFilterTransferAutoSubmit(List<EmpEvalScorer> empEvalScorers) {
        List<EmpEvalScorerNode> sysAutoSubmitSn = new ArrayList<>();
        // 判断当前分发的评分 在上级是否评分过
        if (CollUtil.isEmpty(empEvalScorers)) {
            return sysAutoSubmitSn;
        }

        for (EmpEvalScorer empEvalScorer : empEvalScorers) {
            List<EmpEvalScorerNode> sysAutoSubmitSns = empEvalScorer.copyFinishedDataForTransferNode();//复制已提交的节点数据
            sysAutoSubmitSn.addAll(sysAutoSubmitSns);
        }
        return sysAutoSubmitSn;
    }

    private boolean needSubmitTotalEval() {
        return createTotalLevelType != null && createTotalLevelType == 2;
    }

    public ChainDispatchRs dispatchSuperOfChainNode(ScoreNode scoreNode, String evelEmpId) {
        ListWrap<EvalScoreResult> rs = new ListWrap<>();
        NodeFilter nodeFilter = new NodeFilter(scoreNode.getNode(), scoreNode.getOrder());
        //isOpenAvgWeightCompute上级 是默认开启平均权重的
        List<EvalScoreResult> normalRs = kpiTypes.dispatchIf(nodeFilter, false);//常规
        rs.addAll(new ListWrap<>(normalRs));
        List<PerfEvalTypeResult> typeResults = kpiTypes.dispatchTypeIf(nodeFilter, evelEmpId, false);
        //分发评分人的评分环节
        List<EmpEvalScorer> dispatchEmpEvalScorers = evalScorersWrap.dispatchByScoreType(scoreNode);
        ChainDispatchRs dispatchRs = new ChainDispatchRs(typeResults, rs);
        dispatchRs.setEmpEvalScorers(dispatchEmpEvalScorers);
        return dispatchRs;
    }


    public ChainDispatchRs dispatchSuperOfChainNodeV3(ScoreNode scoreNode) {
        //分发评分人的评分环节
        Set<String> dispatchScoreTypes = new HashSet<>();
        List<EmpEvalScorer> dispatchEmpEvalScorers = evalScorersWrap.dispatchByScoreType(scoreNode);
        ChainDispatchRs dispatchRs = new ChainDispatchRs();
        dispatchScoreTypes.add(scoreNode.getNode().getScene());
        dispatchRs.acceptScorers(dispatchScoreTypes, dispatchEmpEvalScorers);
        return dispatchRs;
    }


    @Override
    public boolean nodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        return kpiTypes.nodeIsEnd(node, nodeOrder);
    }

    public boolean v3NodeIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        return evalScorersWrap.v3NodeIsEnd(node, nodeOrder);
    }
    public boolean subFlowIsEnd(SubScoreNodeEnum node, int nodeOrder) {
        ScoreNode subFlow = currentScoreNoe(node, nodeOrder);
        if (null == subFlow || !subFlow.hasNextSuper()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean chainNodeIsEnd(SubScoreNodeEnum node, int forMainChainOrder) {
        ChainNode current = scoreChain.current(node, forMainChainOrder);//读取当前层级
        if (null == current) {
            return true;
        }
        for (ScoreNode scoreNode : current.scoreNodesContainNext()) {
            if (!kpiTypes.nodeIsEnd(scoreNode.getNode(), scoreNode.getOrder())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean chainNodeIsEndV3(SubScoreNodeEnum node, int forMainChainOrder) {
        ChainNode current = scoreChain.current(node, forMainChainOrder);//读取当前层级
        if (null == current) {
            return true;
        }
        for (ScoreNode scoreNode : current.scoreNodesContainNext()) {
            if (!evalScorersWrap.v3NodeIsEnd(node, scoreNode.getOrder())) {
                return false;
            }
        }
        return true;
    }

    public void initTurnScoreChain() {
        ScoreSceneWrap scoreSceneWrap = kpiTypes.buildNodes();
        List<ScoreSortConf.SortItem> sortItems = scoreSortConf.sort();
        ChainNode head = new ChainNode(SubScoreNodeEnum.AUTO, 1);
        scoreChain = new ScoreChain(head);
        for (ScoreSortConf.SortItem sortItem : sortItems) {
            if (sortItem.isSelfNode() && scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SELF_SCORE)) {
                scoreChain.addNext(new ChainNode(SubScoreNodeEnum.SELF_SCORE, 1));
            }
            if (sortItem.isPeerNode() && scoreSceneWrap.onOpenNode(SubScoreNodeEnum.PEER_SCORE)) {
                scoreChain.addNext(new ChainNode(SubScoreNodeEnum.PEER_SCORE, 1));
            }
            if (sortItem.isSubNode() && scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SUB_SCORE)) {
                scoreChain.addNext(new ChainNode(SubScoreNodeEnum.SUB_SCORE, 1));
            }

            if (sortItem.isSuperNode() && scoreSceneWrap.onOpenNode(SubScoreNodeEnum.SUPERIOR_SCORE)) {
                ScoreNodeWrap orders = scoreSceneWrap.getScoreNodes(SubScoreNodeEnum.SUPERIOR_SCORE);
                if (s3SuperInTurn() || superInTurn()) { //上级串行
                    for (Integer order : orders.getOrders() == null ? Arrays.asList(1) : orders.getOrders()) {
                        scoreChain.addNext(new ChainNode(SubScoreNodeEnum.SUPERIOR_SCORE, order));
                    }
                } else { //上级并行
                    ChainNode chainNode = new ChainNode();
                    scoreChain.addNext(chainNode);
                    for (Integer order : orders.getOrders()) {
                        chainNode.addNode(SubScoreNodeEnum.SUPERIOR_SCORE, order);
                    }
                }
            }

            if (sortItem.isAppointNode() && scoreSceneWrap.onOpenNode(SubScoreNodeEnum.APPOINT_SCORE)) {
                ScoreNodeWrap orders = scoreSceneWrap.getScoreNodes(SubScoreNodeEnum.APPOINT_SCORE);
                ChainNode chainNode = new ChainNode();
                for (Integer order : orders.getOrders()) {
                    chainNode.addNode(SubScoreNodeEnum.APPOINT_SCORE, order);
                }
                scoreChain.addNext(chainNode);
                //appointNodes.getGroups().forEach((orderStr, evalAudits) -> {
                //    scoreChain.addNext(new ChainNode(SubScoreNodeEnum.APPOINT_SCORE, Integer.valueOf(orderStr)));
                //});
                //scoreChain.addNext(new ChainNode(SubScoreNodeEnum.APPOINT_SCORE, 1));
            }
        }
        if (null != head.getNext()) {
            head.getNext().addNode(SubScoreNodeEnum.ITEM_SCORE, 1);
        } else {
            scoreChain.addNext(new ChainNode(SubScoreNodeEnum.ITEM_SCORE, 1));
        }
        addTotalLevelNodeOpt();
    }

    private boolean s3SuperInTurn() {
        return s3SuperRater != null && s3SuperRater.superiorInTurn();
    }

    private boolean superInTurn() {
        return s3SuperRater != null && s3SuperRater.superInTurn();
    }

    public void initSameScoreChain() {
        ScoreSceneWrap sceneWrap = kpiTypes.buildNodes();
        ChainNode chainNode1 = new ChainNode(SubScoreNodeEnum.AUTO, 1);
        scoreChain = new ScoreChain(chainNode1);
        ChainNode chainNode2 = new ChainNode();
        scoreChain.addNext(chainNode2);
        if (sceneWrap.onOpenNode(SubScoreNodeEnum.SELF_SCORE)) {
            chainNode2.addNode(SubScoreNodeEnum.SELF_SCORE, 1);
        }
        if (sceneWrap.onOpenNode(SubScoreNodeEnum.PEER_SCORE)) {
            chainNode2.addNode(SubScoreNodeEnum.PEER_SCORE, 1);
        }
        if (sceneWrap.onOpenNode(SubScoreNodeEnum.SUB_SCORE)) {
            chainNode2.addNode(SubScoreNodeEnum.SUB_SCORE, 1);
        }
        if (sceneWrap.onOpenNode(SubScoreNodeEnum.ITEM_SCORE)) {
            chainNode2.addNode(SubScoreNodeEnum.ITEM_SCORE, 1);
        }
        if (sceneWrap.onOpenNode(SubScoreNodeEnum.APPOINT_SCORE)) {
            //chainNode2.addNode(SubScoreNodeEnum.APPOINT_SCORE, 1);
            ScoreNodeWrap orders = sceneWrap.getScoreNodes(SubScoreNodeEnum.APPOINT_SCORE);
            for (Integer order : orders.getOrders()) {
                chainNode2.addNode(SubScoreNodeEnum.APPOINT_SCORE, order);
            }
        }

        if (sceneWrap.onOpenNode(SubScoreNodeEnum.SUPERIOR_SCORE)) {
            ScoreNodeWrap scoreNodes = sceneWrap.getScoreNodes(SubScoreNodeEnum.SUPERIOR_SCORE);
           // if (isCustom() && !superInTurn()) {  //上级并行,开启了指标流程,上级内部只能同时评分
            if (isCustom() && isSuperSameTime()) {  //上级并行,开启了指标流程,上级内部只能同时评分
                for (Integer order : scoreNodes.getOrders()) {
                    chainNode2.addNode(SubScoreNodeEnum.SUPERIOR_SCORE, order == null ? 1 : order);
                }
            } else if (s3SuperInSame()) {//统一评分上级并行
                List<BaseAuditNode> auditNodes = s3SuperRater.getAuditNodes();
                for (BaseAuditNode auditNode : auditNodes) {
                    chainNode2.addNode(SubScoreNodeEnum.SUPERIOR_SCORE, auditNode.getApprovalOrder() == null ? 1 : auditNode.getApprovalOrder());
                }
            } else {//上级依次串行
                ScoreNode superTial = new ScoreNode(SubScoreNodeEnum.SUPERIOR_SCORE, 1);//第一个是head
                chainNode2.addNode(superTial);
                for (int i = 1; i < scoreNodes.getOrders().size(); i++) {//第二个开始
                    Integer order = scoreNodes.getOrders().get(i);
                    ScoreNode tmp = new ScoreNode(SubScoreNodeEnum.SUPERIOR_SCORE, order == null ? 1 : order);
                    superTial.setNextSuper(tmp);
                    superTial = tmp;
                }
            }
        }
        addTotalLevelNodeOpt();
    }

    private boolean s3SuperInSame() {
        return s3SuperRater != null && s3SuperRater.superiorInSame();
    }

    private void addTotalLevelNodeOpt() {
        if (needSubmitTotalEval()) {
            ChainNode chainNode3 = new ChainNode(SubScoreNodeEnum.TOTAL_LEVEL, 1, 30);
            scoreChain.addNext(chainNode3);
        }
    }

    //更新同级主结点或签方式子结点
    public List<EvalScoreResult> markOrModeScoreRs(SubScoreNodeEnum scoreNode, List<EvalScoreResult> submitItems) {
        List<EvalScoreResult> rs = new ArrayList<>();
        for (EvalScoreResult submitItem : submitItems) {
            List<EvalScoreResult> results = kpiTypes.markOrModeScoreRs(scoreNode, submitItem.getId(), submitItem.getApprovalOrder(), submitItem.getKpiItemId());
            rs.addAll(results);
        }
        return rs;
        //return kpiTypes.markOrModeScoreRs(scoreNode, nodeOrder, submitItemIds);
    }

    @Override
    public Name taskName() {
        return new Name(taskName);
    }

    @Override
    public TenantId companyId() {
        return companyId;
    }

    @Override
    public boolean needAffirm(EvalUser taskUser) {
        return confirmTask.isOpen();
    }

    @Override
    public Integer limitConfirmDays() {
        if (confirmTask.getOpenConfirmLT() == 0) {
            return null;
        }
        return confirmTask.getConfirmLTDay();
    }

    @Override
    @JSONField(serialize = false)
    public boolean isResultAffirmOpen() {
        return confirmResult.isOpen();
    }

    @Override
    public void startAppealOn(TalentStatus curStatus, EvalUser taskUser) {
        if (appealConf == null || !appealConf.isOpen()) {
            return;
        }
        if (appealConf.startAppealOn(curStatus)) {
            taskUser.initAppealDeadLine(appealConf.getCanAppealDay());
            taskUser.setAppealReceiverId(appealConf.getAppealReceiverId(taskUser.getCreatedUser()));
            return;
        }
    }

    @Override
    public boolean isPublishResultOpen() {
        if (publishResult == null) {
            return false;
        }
        return publishResult.isOpen();
    }

    @Override
    public boolean isManualPublicOpen() {
        if (publishResult != null && publishResult.isOpen()) {
            return publishResult.isManual();
        }
        return false;
    }

    @Override
    public List<String> parsePublicEmp() {
        List<String> empIds = new ArrayList<>();
        for (PubExcutor pubExcutor : publishResult.getOpEmps()) {
            if (pubExcutor.isStarter()) {
                empIds.add(StrUtil.isEmpty(initiator) ? createdUser : initiator);
            }
            if (pubExcutor.isFixUser()) {
                empIds.addAll(pubExcutor.fixEmpIds());
            }
        }
        return empIds;
    }

    @Override
    public boolean isResultAuditingOpen() {
        //如果校准节点没有人，不进校准环节
        if (Objects.nonNull(auditResult)) {
            if (auditResult.isOpen() && !auditResult.getAuditNodes().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    //是否打开结果面谈
    @Override
    public boolean isInterviewOpen() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen() && Objects.nonNull(interviewConf.getInterviewExcutorInfo());
        }
        return false;
    }

    //是否打开结果面谈确认签名
    @Override
    public boolean isInterviewConfirmSign() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen() && Objects.nonNull(interviewConf.getInterviewConfirmConf()) && interviewConf.getInterviewConfirmConf().isSign();
        }
        return false;
    }

    //是否打开结果面谈执行转交
    @Override
    @JSONField(serialize = false)
    public boolean isInterviewExcuteTransfer() {
        if (Objects.nonNull(interviewConf)) {
            return interviewConf.isOpen() && Boolean.TRUE.toString().equals(interviewConf.getTransferFlag());
        }
        return false;
    }

    //@Override
    //public String getTemplBaseId() {
    //    //TODO 需再排查一下无模板id 钉钉会不会有问题
    //    return
    //}

    public List<String> allScoreType() {
        List<String> rs = new ArrayList<>();
        if (kpiTypes.hasAutoItem()) {
            rs.add(SubScoreNodeEnum.AUTO.getScene());
        }
        if (isCustom()) {
            List<String> customTypes = kpiTypes.allScoreType();
            rs.addAll(customTypes);
            return rs;
        }
        if (s3SelfRater.isOpen()) {
            rs.add(SubScoreNodeEnum.SELF_SCORE.getScene());
        }
        if (s3SuperRater.isOpen()) {
            rs.add(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        }
        if (s3SubRater.isOpen()) {
            rs.add(SubScoreNodeEnum.SUB_SCORE.getScene());
        }
        if (s3PeerRater.isOpen()) {
            rs.add(SubScoreNodeEnum.PEER_SCORE.getScene());
        }
        return rs;
    }

    public Set<String> allScoreTypeV3() {
        Set<String> rs = new HashSet<>();
        if (kpiTypes.hasAutoItem()) {
            rs.add(SubScoreNodeEnum.AUTO.getScene());
        }

        if (evalScorersWrap.isEmpty()) {
            return rs;
        }

        for (EmpEvalScorer scorer : evalScorersWrap.getDatas()) {
            scorer.getScorerNodes().stream().map(EvalScorerNodeBase::getScorerType).forEach(rs::add);
        }
        return rs;
    }

    public boolean needSelfScore() {
        if (isCustom()) {
            List<? extends EvalKpi> items = kpiTypes.getDatas().stream()
                    .flatMap(type -> type.getItems().stream())
                    .filter(item -> Objects.nonNull(item.getItemScoreRule()))
                    .filter(item -> item.getItemScoreRule().needSelfScore())
                    .collect(Collectors.toList());

            List<? extends EmpEvalKpiType> types = kpiTypes.getDatas().stream()
                    .filter(item -> item.isOpenSelfRater())
                    .collect(Collectors.toList());

            return CollUtil.isNotEmpty(items) || CollUtil.isNotEmpty(types);
        }
        return super.isOpenSelfRater();
    }

    public boolean needPeerScore() {
        if (isCustom()) {
            List<? extends EvalKpi> items = kpiTypes.getDatas().stream()
                    .flatMap(type -> type.getItems().stream())
                    .filter(item -> Objects.nonNull(item.getItemScoreRule()))
                    .filter(item -> item.getItemScoreRule().needPeerScore())
                    .collect(Collectors.toList());
            return CollUtil.isNotEmpty(items);
        }
        return s3PeerRater.isOpen();
    }

    public boolean needSubScore() {
        if (isCustom()) {
            List<? extends EvalKpi> items = kpiTypes.getDatas().stream()
                    .flatMap(type -> type.getItems().stream())
                    .filter(item -> Objects.nonNull(item.getItemScoreRule()))
                    .filter(item -> item.getItemScoreRule().needSubScore())
                    .collect(Collectors.toList());
            return CollUtil.isNotEmpty(items);
        }
        return s3SubRater.isOpen();
    }

    public boolean needSuperScore() {
        return s3SuperRater.isOpen();
    }

    public String itemLogNames(List<EvalScoreResult> submiteds) {
        if (CollUtil.isEmpty(submiteds)) {
            return "";
        }
        List<String> itemNames = new ArrayList<>();
        List<String> itemIds = submiteds.stream().map(evalScoreResult -> evalScoreResult.getKpiItemId()).collect(Collectors.toList());
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            for (EvalKpi item : type.getItems()) {
                if (itemIds.contains(item.getKpiItemId())) {
                    itemNames.add(item.getKpiItemName());
                }
            }
        }
        return CollUtil.join(itemNames, ",");
    }

    public boolean needSetPeerRater(String evalEmpId, String opEmpId, String queryType) {
        return kpiTypes.needSetPeerRater(evalEmpId, opEmpId, queryType);
    }

    public boolean needSetSubRater(String evalEmpId, String opEmpId, String queryType) {
        return kpiTypes.needSetSubRater(evalEmpId, opEmpId, queryType);
    }

    public List<EvalKpi> okrItems() {
        return kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .filter(kpiItem -> kpiItem.isOkrType()).collect(Collectors.toList());
    }
    public List<EvalKpi> allItems() {
        return kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream()).collect(Collectors.toList());
    }

    public List<EvalKpi> businessPlanItems() {
        return kpiTypes.getDatas().stream().flatMap(type -> type.getItems().stream())
                .filter(kpiItem -> kpiItem.isBusinessPlanItem()).collect(Collectors.toList());
    }

    public Boolean fromOldTask() {
        return fromOldTask;
    }

    //是否存在默认流程
    public Boolean hasDefaultRule() {
        return (Objects.nonNull(this.s3SelfRater) && this.s3SelfRater.isOpen()) ||
                (Objects.nonNull(this.s3PeerRater) && this.s3PeerRater.isOpen()) ||
                (Objects.nonNull(this.s3SubRater) && this.s3SubRater.isOpen()) ||
                (Objects.nonNull(this.s3SuperRater) && this.s3SuperRater.isOpen()) ||
                (Objects.nonNull(this.s3AppointRater) && this.s3AppointRater.isOpen());
    }

    public boolean notSetMutualScorer() {
        if (isCustom()) {
            for (EmpEvalKpiType type : kpiTypes.getDatas()) {
                if (type.isAskType()) {
                    if (type.ask360NotSetMutualScorer()) {
                        return true;
                    }
                }
                if (type.isEvalLevel()) {
                    if (type.notSetMutualScorer()) {
                        return true;
                    }
                }
                for (EvalKpi item : type.getItems()) {
                    if (Objects.equals(item.getIndLevelGroupId(), "0")) {
                        continue;
                    }
                    EvalItemScoreRule itemScoreRule = item.getItemScoreRule();
                    //自动计算指标不存在评分流程
                    if (Objects.isNull(itemScoreRule)) {
                        continue;
                    }
                    if (itemScoreRule.notSetMutualScorer()) {
                        return true;
                    }
                }
            }
        } else {
            if (this.s3PeerRater.isOpen() && this.s3SubRater.isOpen()) {
                return CollUtil.isEmpty(this.s3PeerRater.getRaters()) || CollUtil.isEmpty(this.s3SubRater.getRaters());
            }
            if (this.s3PeerRater.isOpen()) {
                return CollUtil.isEmpty(this.s3PeerRater.getRaters());
            }
            if (this.s3SubRater.isOpen()) {
                return CollUtil.isEmpty(this.s3SubRater.getRaters());
            }
        }
        return false;
    }

    public boolean allScorePassed() {
        for (EmpEvalKpiType type : this.kpiTypes.getDatas()) {
            if (type.allScorePassed()) {
                continue;
            } else {
                return false;
            }
        }
        return true;
    }

    public boolean allScorePassedV3() {
        return this.getEvalScorersWrap().getDatas().stream().allMatch(EmpEvalScorer::allScorePassed);
    }


    public List<String> getResultInputEmpIds(String empId) {
        List<String> ids = new ArrayList<>();
        List<String> notSubmit = new ArrayList<>();
        if (CollUtil.isEmpty(this.getKpiTypes().getDatas())) {
            return Arrays.asList("-1");
        }
        for (EmpEvalKpiType kpiType : this.getKpiTypes().getDatas()) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.getInputRaterIds().contains(empId)) {
                    if (item.isMustResultInput() && item.finishValueIsNull() && !item.isFinalSubmit()) {
                        return Arrays.asList("-1");
                    }
                    ids.addAll(item.getInputRaterIds());
                } else {
                    notSubmit.addAll(item.getInputRaterIds());
                }
            }
        }
        List<String> filterId = ids;
        if (!notSubmit.isEmpty()) {
            for (String s : notSubmit) {
                filterId = filterId.stream().filter(id -> !s.equals(id)).collect(Collectors.toList());
            }
        }
        return filterId;
    }

    public List<String> getCurItemAllSubmitInputEmpIds(String empId,List<String> submitItems) {
        if (CollUtil.isEmpty(this.getKpiTypes().getDatas())) {
            return null;
        }
        //当前提交人，同一个指标项目存在多个处理人的，需要校验其他录入人是否已全部提交
        Set<String> tempEmps = new HashSet<>();
        for (EmpEvalKpiType kpiType : this.getKpiTypes().getDatas()) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (CollUtil.isEmpty(item.getInputRaterIds())) {
                    continue;
                }
                if (submitItems.contains(item.getKpiItemId())
                        && item.getInputRaterIds().contains(empId)
                        && item.getInputRaterIds().size() > 1 ) {
                    tempEmps.addAll(item.getInputRaterIds());
                    tempEmps.remove(empId);
                }
            }
        }
        List<String> ids = new ArrayList<>();
        //先筛选出未提交的录入人【排除当前提交人】，且指标是未最终提交的
        Set<String> notSubmits= new HashSet<>();
        for (EmpEvalKpiType kpiType : this.getKpiTypes().getDatas()) {
            if (CollUtil.isEmpty(kpiType.getItems())) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (CollUtil.isEmpty(item.getInputRaterIds())) {
                    continue;
                }
                for (String inputEmpId : item.getInputRaterIds()) {
                    if (tempEmps.contains(inputEmpId) && !item.isFinalSubmit()) {
                        notSubmits.add(inputEmpId);
                    }
                }
            }
        }

        if (CollUtil.isEmpty(notSubmits)) {
            ids.addAll(tempEmps);
            return ids;
        }

        for (String inputEmpId : tempEmps) {
            if (!notSubmits.contains(inputEmpId)) {
                ids.addAll(tempEmps);
            }
        }
        return ids;
    }

    /**
     * 解析流程用作处理异常
     */
    public List<RaterNode> initRaters() {
        List<RaterNode> ratersNodes = new ArrayList<>();
        String self = "self";
        if (s3SelfRater != null && s3SelfRater.isOpen()) {
            ratersNodes.add(new RaterNode(self, new Rater(null, 5)));
        }
        String aSuper = "super";
        if (s3SuperRater != null && s3SuperRater.isOpen()) {
            ratersNodes.add(new RaterNode(aSuper, s3SuperRater.allOrNullRater(), false));
        }
        String peer = "peer";
        if (s3PeerRater != null && s3PeerRater.isOpen()) {
            if (s3PeerRater.getAppointer() != null) {
                if ("emp".equals(s3PeerRater.getAppointer().getType()) || StrUtil.isBlank(s3PeerRater.getAppointer().getType())) {
                    ratersNodes.add(new RaterNode(peer, Arrays.asList(new Rater(null, 5)), false));
                } else {
                    ratersNodes.add(new RaterNode(peer, s3PeerRater.getAppointer().getRaters(), false));
                }
            } else {
                ratersNodes.add(new RaterNode(peer, s3PeerRater.getRaters(), false));
            }
        }
        String sub = "sub";
        if (s3SubRater != null && s3SubRater.isOpen()) {
            if (s3SubRater.getAppointer() != null) {
                if ("emp".equals(s3SubRater.getAppointer().getType()) || StrUtil.isBlank(s3SubRater.getAppointer().getType())) {
                    ratersNodes.add(new RaterNode(sub, Arrays.asList(new Rater(null, 5)), false));
                } else {
                    ratersNodes.add(new RaterNode(sub, s3SubRater.getAppointer().getRaters(), false));
                }
            } else {
                ratersNodes.add(new RaterNode(sub, s3SubRater.getRaters(), false));
            }
        }
        if (isCustom()) {
            Map<String, RaterNode> nodeMap = CollUtil.toMap(ratersNodes, new HashMap<>(), node -> node.getNode());
            for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
                kpiType.setRaterBack(true);
                kpiType.addIndexRaterOpt(nodeMap, ratersNodes);
            }
        }

        //解析定向评分
        String item = "item";
        boolean isOpen = false;
        List<Rater> raters = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.getItems().isEmpty()) {
                return ratersNodes;
            }
            for (EvalKpi evalKpi : kpiType.getItems()) {
                if ("user".equals(evalKpi.getScorerType())) {
                    isOpen = true;
                    List<StaffConfItem> confItems = evalKpi.getScorerObjId();
                    for (StaffConfItem confItem : confItems) {
                        if (!confItem.getObjItems().isEmpty()) {
                            for (ObjItem objItem : confItem.getObjItems()) {
                                raters.add(new Rater(objItem.getObjId(), objItem.getObjName()));
                            }
                        }
                    }
                }
            }
        }
        if (isOpen || !raters.isEmpty()) {
            ratersNodes.add(new RaterNode(item, raters));
        }
        return ratersNodes;
    }

    /**
     * 不需要录入
     */
    public Boolean parseEntryClerkNoInput() {
        if (kpiTypes.getDatas().isEmpty()) {
            return false;
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.getItems().isEmpty()) {
                return false;
            }
            if (!kpiType.isOpenFinishValue()) {
                return false;
            }
            for (EvalKpi kpiTypeItem : kpiType.getItems()) {
                if (!"no".equals(kpiTypeItem.getResultInputType())) {
                    return false;
                }
            }
        }
        return true;
    }

    //解析录入人是否为null
    public Boolean parseEntryClerkIsNull() {
        if (kpiTypes.getDatas().isEmpty()) {
            return false;
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.getItems().isEmpty()) {
                continue;
            }
            if (!kpiType.isOpenFinishValue()) {
                continue;
            }
            for (EvalKpi kpiTypeItem : kpiType.getItems()) {
                if (!"exam".equals(kpiTypeItem.getResultInputType()) && !"no".equals(kpiTypeItem.getResultInputType())) {
                    if (StringUtils.isBlank(kpiTypeItem.getResultInputEmpId())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    //解析录入人
    public List<String> parseEntryClerk() {
        List<String> empIds = new ArrayList<>();
        if (kpiTypes.getDatas().isEmpty()) {
            return new ArrayList<>();
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.getItems().isEmpty()) {
                continue;
            }
            for (EvalKpi kpiTypeItem : kpiType.getItems()) {
                if ("exam".equals(kpiTypeItem.getResultInputType())) {
                    empIds.add(kpiTypeItem.getEmpId());
                }
                if (StringUtils.isNotBlank(kpiTypeItem.getResultInputEmpId())) {
                    empIds.addAll(Arrays.asList(kpiTypeItem.getResultInputEmpId().split(",")));
                }
            }
        }
        return empIds;
    }

    public boolean kpiItemIsEmpt(EmpEvalKpiType kpiType) {
        if (CollUtil.isNotEmpty(kpiType.getItems())) {
            return false;
        }
        return true;
    }

    public boolean kpiItemAllIsEmpt() {
        if (CollUtil.isNotEmpty(kpiTypes.getDatas())) {
            for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
                if (!kpiItemIsEmpt(kpiType)) {
                    return false;
                }
            }
        }
        return true;
    }

    //解析经营计划指标异常
    public void parseBusinessPlanItem(List<BusinessPlanItem> planItemList, EvalUser evalUser) {
        if (kpiTypes.getDatas().isEmpty()) {
            return;
        }
        List<EvalKpi> items = businessPlanItems();
        if (CollUtil.isEmpty(items)) {
            return;
        }
        if (CollUtil.isEmpty(planItemList)) {
            evalUser.confEvalPublicErro("801");  //未创建目标值
            return;
        }
        int notAppoint = CollUtil.filterNew(items, kpi -> StrUtil.isBlank(kpi.getOkrGoalId())).size();
        if (notAppoint > 0) {
            evalUser.confEvalPublicErro("802");  //未指定目标值
        }
    }

    //解析指标完成值审核人
    public List<String> parseFinishedValueAudit() {
        List<String> empIds = new ArrayList<>();
        if (kpiTypes.getDatas().isEmpty()) {
            return null;
        }
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.isOkr()) {
                if (CollUtil.isNotEmpty(kpiType.getFinishValueAudit())) {
                    empIds.addAll(kpiType.getFinishValueAudit().stream().filter(confItem -> CollUtil.isNotEmpty(confItem.getObjItems()))
                            .flatMap(type -> type.getObjItems().stream().map(ObjItem::getObjId)).collect(Collectors.toList()));
                    continue;
                }
            }
            if (kpiItemIsEmpt(kpiType)) {
                continue;
            }
            for (EvalKpi kpiTypeItem : kpiType.getItems()) {
                if (CollUtil.isNotEmpty(kpiTypeItem.getFinishValueAudit())) {
                    empIds.addAll(kpiTypeItem.getFinishValueAudit().stream().filter(confItem -> CollUtil.isNotEmpty(confItem.getObjItems()))
                            .flatMap(type -> type.getObjItems().stream().map(ObjItem::getObjId)).collect(Collectors.toList()));
                }
            }
        }
        return CollUtil.distinct(empIds);
    }

    //解析申述受理人
    public List<String> parseAppealEmp() {
        List<String> empIds = new ArrayList<>();
        for (StaffConfItem item : appealConf.getAppealReceiver()) {
            if (item.isTaskAdminType()) {
                empIds.add(StrUtil.isEmpty(initiator) ? createdUser : initiator);
            }
            if (item.isFixEmpType()) {
                empIds.addAll(item.getObjItems().stream().map(ObjItem::getObjId).collect(Collectors.toList()));
            }
        }
        return empIds;
    }

    @Override
    protected Iterable<? extends EmpEvalKpiType> typeDatas() {
        return kpiTypes.getDatas();
    }

    public AsDisplayFlowRs asDisplayFlow(String empId) {
        String sameTime = isCustom() ? this.getSuperiorScoreWay() : getS3SuperRater().getSuperiorScoreOrder();
        AsDisplayFlowRs disFlows = new AsDisplayFlowRs(scoreConf.getMultiType(), scoreSortConf, sameTime, scoreView);
        //构建已完成分发评分流程及人员,已开始或已完成状态
        AsDisplayFlowRs flowRs = getKpiTypes().asDisplayFlow(disFlows, empId);
        return flowRs;
    }

//    public AsDisplayFlowRs asDisplayFlowV3(String empId) {
//        String sameTime = isCustom() ? this.getSuperiorScoreWay() : getS3SuperRater().getSuperiorScoreOrder();
//        AsDisplayFlowRs disFlows = new AsDisplayFlowRs(scoreConf.getMultiType(), scoreSortConf, sameTime, scoreView);
//        disFlows.addFlowNodeV3(empEvalScorerNodesAll());
//        return disFlows;
//    }

//    public AsDisplayFlowRsV3 asDisplayFlowV3(ListWrap<FlowRater> flowRatersWrap) {
//        String sameTime = isCustom() ? this.getSuperiorScoreWay() : getS3SuperRater().getSuperiorScoreOrder();
//        AsDisplayFlowRsV3 disFlows = new AsDisplayFlowRsV3(scoreConf.getMultiType(), scoreSortConf, sameTime, scoreView, flowRatersWrap);
//        disFlows.covertDisplayEvalFlow();
//        return disFlows;
//    }

    public List<EvalScoreResult> fixItemScore() {
        kpiTypes.initWeight(typeWeightConf, scoreValueConf.submitWithWeight());//归一化类别权重
        List<EvalScoreResult> scoreResults = new ArrayList<>();
        for (EmpEvalKpiType type : kpiTypes.getDatas()) {
            List<EvalScoreResult> results = type.fixItemScore(scoreValueConf.submitWithWeight());
            scoreResults.addAll(results);
        }
        return scoreResults;
    }

    public void initTypeWeight() {
        kpiTypes.initWeight(typeWeightConf, scoreValueConf.submitWithWeight());//归一化类别权重
    }

    //处理一票否决后的分值
    public void handleVetoTheScore() {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        List<EmpEvalKpiType> vetoTypes = this.kpiTypes.getDatas().stream()
                .filter(typeClass -> Objects.equals(typeClass.getKpiTypeClassify(), "oneVoteVeto")).collect(Collectors.toList());
        if (CollUtil.isEmpty(vetoTypes)) {
            return;
        }
        for (EmpEvalKpiType vetoType : vetoTypes) {
            for (EvalKpi tItem : vetoType.getItems()) {
                int vetoCnt = CollUtil.filterNew(tItem.getWaitScores(), v -> Objects.equals(v.getVetoFlag(), "true")).size();
                if (vetoCnt > 0) {
                    this.setFinalScore(BigDecimal.ZERO);
                    this.setV3FinalScore(BigDecimal.ZERO);//V3版的最终得分
                    return;
                }
            }
        }
    }

    //转交评分任务时,需要更新原规则,否则getFlow无法显示转交后的人员,依然显示原人员
    public void transferScoringIf(Set<String> fromEmpScoreTypes, String fromEmpId, KpiEmp toEmp, Boolean raterWasExist, Boolean wasCustom) {
        if (CollUtil.isEmpty(fromEmpScoreTypes)) {
            return;
        }
        Editor<String> editor = empScoreType -> {
            if (StrUtil.contains(empScoreType, "score")) {
                return empScoreType;
            } else {
                return null;
            }
        };

        Collection<String> scoreTypes = CollUtil.filter(fromEmpScoreTypes, editor);
        super.transferScoringIf(scoreTypes, fromEmpId, toEmp, raterWasExist, wasCustom);
    }

    public void skipRaterIf(Set<String> fromEmpScoreTypes, String skipUserId) {
        if (CollUtil.isEmpty(fromEmpScoreTypes)) {
            return;
        }
        super.skipRater(fromEmpScoreTypes, skipUserId);
    }

    //用于处理重置后,重新提交A,B同步指定评 , B提交,重置B,A提交,B再提交. 直接跳过后后续流程
    public ChainNode skipFinishedChainNode(ChainNode from) {
        if (from == null) {
            return null;
        }
        ChainNode to = from;
        while (isChianEnd(to)) {
            to = to.getNext();
            if (to == null) {
                return null;
            }
        }
        return to;
    }

    public boolean isChianEnd(ChainNode to) {
        if (to.isTotalLevel()) {
            return false;
        }
        for (ScoreNode scoreNode : to.getScoreNodes()) {
           // if (!nodeIsEnd(scoreNode.getNode(), scoreNode.getOrder())) {
            if (!v3NodeIsEnd(scoreNode.getNode(), scoreNode.getOrder())) {
                return false;
            }
        }
        return true;
    }

    public boolean isDispactched(ChainNode chainNode) {
        ScoreNode first = chainNode.getScoreNodes().get(0);
        return kpiTypes.isDispactched(first.getNode(), first.getOrder());
    }

    public boolean isAnyNotDispactched(ChainNode current) {
        for (ScoreNode scoreNode : current.getScoreNodes()) {
            if (!kpiTypes.isDispactched(scoreNode.getNode(), scoreNode.getOrder())) {
                return true;
            }
        }
        return false;
    }

    //找出需要opEmpId指定的指标
    public List<InviteItem> listMutualItem(String scene, String opEmpId, String evalEmpId, String queryType) {
        return kpiTypes.listMutualItem(scene, opEmpId, evalEmpId, queryType);
    }

    //找出互评指定人
    public Set<String> listMutualAppointEmpId(String evalEmpId) {
        return kpiTypes.listMutualAppointEmpId(evalEmpId);
    }

    public void initScoreChain() {
        if (this.isSameTime()) {
            this.initSameScoreChain();
        } else {
            this.initTurnScoreChain();
        }
    }

    public void fromTaskConf(String taskName, String taskId, ScoreConf scoreConf, ScoreSortConf scoreSortConf) {
        if (Objects.isNull(this.scoreSortConf)) {
            this.setScoreSortConf(scoreSortConf);
        }
        this.setTaskName(taskName);
        this.setTaskId(taskId);
        this.setScoreConf(scoreConf);
    }

    //指定人员参与的评分环节
    public Set<String> scorerTypes(String opEmpId) {
        return kpiTypes.scoreNode(opEmpId);
    }

    public Map<String, List<EvalScoreResult>> builderWaitScore(String dingCorpId,List<String> defaultCustomScoreConfig,
                                                                   List<String> defaultPlusSubScoreConfig) {
        if (CollUtil.isEmpty(this.evalScorersWrap.getDatas())) {
            return new HashMap<>();
        }

        List<EmpEvalScorerNode> waitSubmitSns= evalScorersWrap.listWaitSubmitScoreNode();
        if (CollUtil.isEmpty(waitSubmitSns)){
            return new HashMap<>();
        }

        for (EmpEvalScorerNode waitSubmitSn : waitSubmitSns) {
            waitSubmitSn.builderItemWaitScore(dingCorpId,defaultCustomScoreConfig, defaultPlusSubScoreConfig,this.scoreValueConf,this.kpiTypes.getDatas());
        }

        ListWrap<EmpEvalScorerNode> scorerNodeListWrap = new ListWrap<>(waitSubmitSns).asMap(EmpEvalScorerNode::getScorerId);

        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            kpiType.builderItemWaitScore(dingCorpId,defaultCustomScoreConfig, defaultPlusSubScoreConfig,
                    this.scoreValueConf);
        }
        List<EvalScoreResult> waitScores = this.kpiTypes.getDatas().stream().flatMap(kpiType -> kpiType.getItems().stream())
                .flatMap(item -> item.getWaitScoresOld().stream()).filter(r -> !r.isPassed()).collect(Collectors.toList());
        if (CollUtil.isEmpty(waitScores)) {
            return new HashMap<>();
        }
        List<EvalScoreResult> submitScores = new ArrayList<>();
        Map<String, List<EvalScoreResult>> scorerTypeMapGroup = CollStreamUtil.groupByKey(waitScores, EvalScoreResult::getScorerType);
        scorerTypeMapGroup.forEach((k, v) -> {
            Map<String, List<EvalScoreResult>> scorerIdMapGroup = CollStreamUtil.groupByKey(v, EvalScoreResult::getScorerId);
            scorerIdMapGroup.forEach((key, val) -> {
                List<Boolean> status = CollUtil.map(val, r -> r.isMaySkip(), true).stream().distinct().collect(Collectors.toList());
                if (status.size() == 1 && status.get(0)) {
                    submitScores.addAll(val);
                }
            });
        });
        if (CollUtil.isEmpty(submitScores)) {
            return new HashMap<>();
        }
        List<EvalScoreResult> allSubmit = submitScores;
        //或签时随意取一人
        if (submitScores.get(0).isOrVisa()) {
            allSubmit = submitScores.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(o -> o.getScorerType() + ";" + o.getKpiTypeId() + ";" + o.getKpiItemId() + ";" + o.getApprovalOrder()))), ArrayList::new));
        }
        return CollStreamUtil.groupByKey(allSubmit, EvalScoreResult::getScorerId);
    }

    public Map<String, List<EvalScoreResult>> builderItemWaitScore(String dingCorpId,List<String> defaultCustomScoreConfig,
                                                                   List<String> defaultPlusSubScoreConfig) {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return new HashMap<>();
        }
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            kpiType.builderItemWaitScore(dingCorpId,defaultCustomScoreConfig, defaultPlusSubScoreConfig,
                    this.scoreValueConf);
        }
        List<EvalScoreResult> waitScores = this.kpiTypes.getDatas().stream().flatMap(kpiType -> kpiType.getItems().stream())
                .flatMap(item -> item.getWaitScoresOld().stream()).filter(r -> !r.isPassed()).collect(Collectors.toList());
        if (CollUtil.isEmpty(waitScores)) {
            return new HashMap<>();
        }
        List<EvalScoreResult> submitScores = new ArrayList<>();
        Map<String, List<EvalScoreResult>> scorerTypeMapGroup = CollStreamUtil.groupByKey(waitScores, EvalScoreResult::getScorerType);
        scorerTypeMapGroup.forEach((k, v) -> {
            Map<String, List<EvalScoreResult>> scorerIdMapGroup = CollStreamUtil.groupByKey(v, EvalScoreResult::getScorerId);
            scorerIdMapGroup.forEach((key, val) -> {
                List<Boolean> status = CollUtil.map(val, r -> r.isMaySkip(), true).stream().distinct().collect(Collectors.toList());
                if (status.size() == 1 && status.get(0)) {
                    submitScores.addAll(val);
                }
            });
        });
        if (CollUtil.isEmpty(submitScores)) {
            return new HashMap<>();
        }
        List<EvalScoreResult> allSubmit = submitScores;
        //或签时随意取一人
        if (submitScores.get(0).isOrVisa()) {
            allSubmit = submitScores.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(o -> o.getScorerType() + ";" + o.getKpiTypeId() + ";" + o.getKpiItemId() + ";" + o.getApprovalOrder()))), ArrayList::new));
        }
        return CollStreamUtil.groupByKey(allSubmit, EvalScoreResult::getScorerId);
    }


    public Map<String, List<PerfEvalTypeResult>> builderTypeWaitScore(List<String> defaultCustomScoreConfig,
                                                                      List<String> defaultPlusSubScoreConfig) {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return new HashMap<>();
        }
        for (EmpEvalKpiType kpiType : this.kpiTypes.getDatas()) {
            kpiType.builderTypeWaitScore(defaultCustomScoreConfig, defaultPlusSubScoreConfig);
        }
        List<PerfEvalTypeResult> waitScores = this.kpiTypes.getDatas().stream().flatMap(kpiType -> kpiType.getWaitScoresOld().stream())
                .filter(score -> !score.isPassed()).collect(Collectors.toList());
        if (CollUtil.isEmpty(waitScores)) {
            return new HashMap<>();
        }
        List<PerfEvalTypeResult> submitScores = new ArrayList<>();
        Map<String, List<PerfEvalTypeResult>> scorerTypeMapGroup = CollStreamUtil.groupByKey(waitScores, PerfEvalTypeResult::getScorerType);
        scorerTypeMapGroup.forEach((k, v) -> {
            Map<String, List<PerfEvalTypeResult>> scorerIdMapGroup = CollStreamUtil.groupByKey(v, PerfEvalTypeResult::getScorerId);
            scorerIdMapGroup.forEach((key, val) -> {
                List<Boolean> status = CollUtil.map(val, r -> r.isMaySkip(), true).stream().distinct().collect(Collectors.toList());
                if (status.size() == 1 && status.get(0)) {
                    submitScores.addAll(val);
                }
            });
        });

        if (CollUtil.isEmpty(submitScores)) {
            return new HashMap<>();
        }
        List<PerfEvalTypeResult> allSubmit = submitScores;
        //或签时随意取一人
        if (submitScores.get(0).isOrVisa()) {
            allSubmit = submitScores.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(o -> o.getScorerType() + ";" + o.getKpiTypeId() + ";" + o.getApprovalOrder()))), ArrayList::new));
        }
        return CollStreamUtil.groupByKey(allSubmit, PerfEvalTypeResult::getScorerId);
    }


    public void convertFormulaField(StdTemp stdTemp) {
        Map<String, PerfTemplKpiItem> itemMap = CollUtil.toMap(stdTemp.kpiItems(), new HashMap<>(), i -> i.getKpiItemId());
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            for (EvalKpi dataItem : data.getItems()) {
                PerfTemplKpiItem tempItem = itemMap.get(dataItem.getKpiItemId());
                if (Objects.nonNull(tempItem)) {
                    if (CollUtil.isNotEmpty(tempItem.getFormulaFieldList())) {
                        List<EvalFormulaField> formulas = Convert.toList(EvalFormulaField.class, tempItem.getFormulaFieldList());
                        if (CollUtil.isNotEmpty(formulas)) {
                            formulas.forEach(f -> f.cleanId());
                        }
                        dataItem.copyFormulaFields(formulas);
                    }
                }
            }
        }
    }

    public void initSignatureFlag() {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return;
        }
        for (EmpEvalKpiType type : this.kpiTypes.getDatas()) {
            type.initTypeSignatureFlagIfOpt();
            type.initItemSignatureFlagIfOpt(isCustom(),this.s3SelfRater, this.s3PeerRater, this.s3SubRater, this.s3SuperRater, this.s3AppointRater);
        }
    }

    //如果开启了 去掉最高,最低分算法,需要在所有评分完成后, 重算覆盖一下互评总分
    public List<EvalScoreResult> recomputeUseDelMinMaxOpt( boolean isOpenAvgWeightCompute) {
        if (s3PeerRater == null && s3SubRater == null) {
            return new ArrayList<>();
        }
        if (!s3PeerRater.onUseDelMinMax() && !s3SubRater.onUseDelMinMax()) {
            return new ArrayList<>();
        }
        if (s3PeerRater.onUseDelMinMax()) {
            kpiTypes.recomputeUseDelMinMax(SubScoreNodeEnum.PEER_SCORE, s3PeerRater.getRaters(),isOpenAvgWeightCompute);
        }
        if (s3SubRater.onUseDelMinMax()) {
            kpiTypes.recomputeUseDelMinMax(SubScoreNodeEnum.SUB_SCORE, s3SubRater.getRaters(),isOpenAvgWeightCompute);
        }
        return this.fixItemScore();
    }


    //如果开启了 去掉最高,最低分算法,需要在所有评分完成后, 重算覆盖一下互评总分
    public void recomputeUseDelMinMaxOpt() {
        if (s3PeerRater == null && s3SubRater == null) {
            return ;
        }

        if (Objects.nonNull(s3PeerRater) && s3PeerRater.onUseDelMinMax()) {
            evalScorersWrap.recomputeUseDelMinMax(SubScoreNodeEnum.PEER_SCORE);
        }
        if (Objects.nonNull(s3SubRater) && s3SubRater.onUseDelMinMax()) {
            evalScorersWrap.recomputeUseDelMinMax(SubScoreNodeEnum.SUB_SCORE);
        }
        this.reComputeScorerNodeItemScore();
    }


    @JSONField(serialize = false)
    public void setAskEvalScore(String ask360EvalId, BigDecimal ask360EvalScore, BigDecimal ask360EvalTotalScore) {
        if (CollUtil.isEmpty(this.listAsk360Types())) {
            return;
        }
        for (EmpEvalKpiType askType : this.listAsk360Types()) {
            askType.setAskEvalScore(ask360EvalId, ask360EvalScore, ask360EvalTotalScore);
        }
    }

    @JSONField(serialize = false)
    public boolean hasNotImportType() {
        if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
            return false;
        }
        for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
            if (data.isAskType()) {
                continue;
            }
            if (CollUtil.isEmpty(data.getItems())) {
                return true;
            }
        }
        return false;
    }

    public Map<String, List<String>> inviteMutualAuditMap(String evalEmpId, List<String> kpiItemIds) {
        Map<String, List<String>> map = new HashMap<>();
        if (Objects.isNull(this.s3PeerRater) && Objects.isNull(this.s3SubRater)) {
            return map;
        }
        if (this.isCustom()) {
            if (CollUtil.isEmpty(this.kpiTypes.getDatas())) {
                return map;
            }
            List<String> peerAuditIds = new ArrayList<>();
            List<String> subAuditIds = new ArrayList<>();
            for (EmpEvalKpiType data : this.kpiTypes.getDatas()) {
                if (data.isAskType()) {
                    if (!kpiItemIds.contains(data.getKpiTypeId())) {
                        continue;
                    }
                    if (null != data.getPeerRater() && data.getPeerRater().isOpen()) {
                        WaitAppoint waitAppoint = data.getPeerRater().getAppointer();
                        if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                            continue;
                        }
                        if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                            peerAuditIds.add(evalEmpId);
                        }
                        for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                            peerAuditIds.add(rater.getEmpId());
                        }
                    }
                    if (null != data.getSubRater() && data.getSubRater().isOpen()) {
                        WaitAppoint waitAppoint = data.getSubRater().getAppointer();
                        if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                            continue;
                        }
                        if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                            subAuditIds.add(evalEmpId);
                        }
                        for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                            subAuditIds.add(rater.getEmpId());
                        }
                    }
                }

                for (EvalKpi item : data.getItems()) {
                    if (data.isEvalLevel()) {
                        if (null != data.getPeerRater() && data.getPeerRater().isOpen()) {
                            WaitAppoint waitAppoint = data.getPeerRater().getAppointer();
                            if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                                continue;
                            }
                            if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                                peerAuditIds.add(item.getEmpId());
                            }
                            for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                                peerAuditIds.add(rater.getEmpId());
                            }
                        }
                        if (null != data.getSubRater() && data.getSubRater().isOpen()) {
                            WaitAppoint waitAppoint = data.getSubRater().getAppointer();
                            if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                                continue;
                            }
                            if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                                subAuditIds.add(item.getEmpId());
                            }
                            for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                                subAuditIds.add(rater.getEmpId());
                            }
                        }
                        continue;
                    }
                    if (!kpiItemIds.contains(item.getKpiItemId())) {
                        continue;
                    }
                    EvalItemScoreRule scoreRule = item.getItemScoreRule();
                    if (null != scoreRule.getPeerRater() && scoreRule.getPeerRater().isOpen()) {
                        WaitAppoint waitAppoint = scoreRule.getPeerRater().getAppointer();
                        if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                            continue;
                        }
                        if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                            peerAuditIds.add(item.getEmpId());
                        }
                        for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                            peerAuditIds.add(rater.getEmpId());
                        }
                    }
                    if (null != scoreRule.getSubRater() && scoreRule.getSubRater().isOpen()) {
                        WaitAppoint waitAppoint = scoreRule.getSubRater().getAppointer();
                        if (Objects.isNull(waitAppoint) || !waitAppoint.isOpenInviteMutualAudit()) {
                            continue;
                        }
                        if (waitAppoint.getInviteMutualAudit().getApproverType().equals("emp")) {
                            subAuditIds.add(item.getEmpId());
                        }
                        for (Rater rater : waitAppoint.getInviteMutualAudit().getRaters()) {
                            subAuditIds.add(rater.getEmpId());
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(peerAuditIds)) {
                map.put("peer_score", peerAuditIds);
            }
            if (CollUtil.isNotEmpty(subAuditIds)) {
                map.put("sub_score", subAuditIds);
            }
            return map;
        }
        if (Objects.nonNull(this.s3PeerRater)) {
            if (this.s3PeerRater.isOpen()) {
                WaitAppoint waitAppoint = this.s3PeerRater.getAppointer();
                if (Objects.nonNull(waitAppoint) && waitAppoint.isOpenInviteMutualAudit()) {
                    List<String> auditEmpIds = new ArrayList<>();
                    auditEmpIds.addAll(waitAppoint.getInviteMutualAudit().getRaters().stream().map(Rater::getEmpId).distinct()
                            .collect(Collectors.toList()));
                    map.put("peer_score", auditEmpIds);
                }
            }
        }
        if (Objects.nonNull(this.s3SubRater)) {
            if (this.s3SubRater.isOpen()) {
                WaitAppoint waitAppoint = this.s3SubRater.getAppointer();
                if (Objects.nonNull(waitAppoint) && waitAppoint.isOpenInviteMutualAudit()) {
                    List<String> auditEmpIds = new ArrayList<>();
                    auditEmpIds.addAll(waitAppoint.getInviteMutualAudit().getRaters().stream().map(Rater::getEmpId).distinct()
                            .collect(Collectors.toList()));
                    map.put("sub_score", auditEmpIds);
                }
            }
        }
        return map;
    }

    public List<String> orFinishedScorer(List<String> orScorerIds) {
        return kpiTypes.orFinishedScorer(orScorerIds);
    }
    public List<String> orFilterWithOutFinishScorer(List<String> orScorerIds) {
        return kpiTypes.orFilterWithOutFinishScorer(orScorerIds);
    }
    public Collection<String> inputEmpIds() {
        List<String> empIds = new ArrayList<>();
        for (EmpEvalKpiType kpiType : kpiTypes.getDatas()) {
            if (kpiType.isEmpty()) {
                continue;
            }
            for (EvalKpi item : kpiType.getItems()) {
                if (item.isFinalSubmit()) {//如果已最终提交，则无需再录入
                    continue;
                }
                empIds.addAll(item.getInputRaterIds());
            }
        }
        return empIds;

    }

    public Boolean hasTransferScores(List<EvalScoreResult> itemScoreList , TransferOrSkipRaters transferOrSkipRaters) {
        List<String> resultIds = itemScoreList.stream().map(s -> s.getId()).collect(Collectors.toList());
        List<EvalScoreResult> res = this.totalEvalRs;
        res = res.stream()
                .filter(s -> !resultIds.contains(s.getId()) &&
                        s.getScorerType().equals(transferOrSkipRaters.getScorerType()) &&
                        s.getScore() == null && s.getScorerId().equals(transferOrSkipRaters.getScorerId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(res)){
            return true;
        }else {
            return false;
        }
    }

    public List<String> transferScorerIds(List<String> scorerIds, String empId) {
        List<EvalScoreResult> res = totalEvalRs.stream()
                .filter(s -> scorerIds.contains(s.getScorerId()))
                .collect(Collectors.toList());

        Map<String, Set<Integer>> scorerOrderMap = res.stream()
                .collect(Collectors.groupingBy(
                        EvalScoreResult::getScorerId,
                        Collectors.mapping(EvalScoreResult::getApprovalOrder, Collectors.toSet())
                ));

        List<String> scorerIdRes = scorerOrderMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        scorerIdRes.remove(empId);
        return scorerIdRes;
    }

    public List<String> listKpiItemId() {
        List<String> kpiItemIds = new ArrayList<>();

        if (kpiTypes.isEmpty()) {
            return kpiItemIds;
        }
        // kpiItemIds add items kpiItemId
        kpiTypes.getDatas().forEach(kpiType -> kpiType.getItems().stream().map(EvalKpi::getKpiItemId).forEach(kpiItemIds::add));
        return kpiItemIds;
    }

    public boolean selfAlreadyRejected() {
        //如果存在已经被驳回的自评，则返回true
        AtomicBoolean selfScoreFlag = new AtomicBoolean(false);

        this.getKpiTypes().getDatas().stream()
                .flatMap(type -> type.getWaitScoresOld().stream())
                .filter(BaseTypeScoreResult::isSelfScoreRejected)
                .findFirst()
                .ifPresent(score -> selfScoreFlag.set(true));

        this.getKpiTypes().getDatas().stream()
                .flatMap(type -> type.getItems().stream())
                .filter(item -> item.getWaitScoresOld().stream()
                        .anyMatch(BaseTypeScoreResult::isSelfScoreRejected))
                .findFirst()
                .ifPresent(item -> {
                    selfScoreFlag.set(true);
                });

        return selfScoreFlag.get();
    }

    public boolean selfAlreadyRejectedV3() {
        //如果存在已经被驳回的自评，则返回true
        return evalScorersWrap.selfAlreadyRejected();
    }

    public boolean selfIsFinished(String empId) {
        return evalScorersWrap.selfIsFinished(empId);
    }
}