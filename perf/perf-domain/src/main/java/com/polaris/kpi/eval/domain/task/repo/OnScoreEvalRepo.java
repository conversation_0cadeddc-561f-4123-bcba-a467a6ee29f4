package com.polaris.kpi.eval.domain.task.repo;

import cn.com.polaris.kpi.eval.ScoreEmp;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SubmitOrEvalScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.SkipScorerDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.score.TransferScorerV3DmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTaskOperation;
import com.polaris.kpi.eval.domain.task.entity.empeval.EvalScorersWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.KpiListWrap;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.SkipScorer;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;

import java.util.List;

/**
 * 评分阶段的仓库
 */
public interface OnScoreEvalRepo {

    EvalOnScoreStage getOnScoreEval(TenantId companyId, String taskUserId);

    EvalResetScoreEmp getResetScoreEmpEval(TenantId companyId, String taskUserId,List<ScoreEmp> scoreEmps);

    TransferScorerV3DmSvc getTransferScorerEmpEval(TenantId companyId, EvalUser evalUser, String fromEmpId, String toEmpId, String opEmpId);

    SkipScorerDmSvc getSkipScorerEmpEval(TenantId companyId, String taskUserId, String skipScorerId, String skipScoreType, String opEmpId);

    ScorerDataMingrationDmSvc getOnScoreEvalMingration(TenantId companyId, String taskUserId);

    ListWrap<EvalOnScoreStage> listOnScoreEval(TenantId companyId, List<String> taskUserIds);

    void batchSaveScore(List<EvalOnScoreStage> evalOnScoreStage, String opEmpId);

    void saveEvalOrScorer(SubmitOrEvalScorerV3DmSvc dmSvc);

    void saveEvalUserNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId);

    void saveEmpEvalSNScoreByTaskUserId(String companyId,String evalUserId,EvalScorersWrap evalScorersWrap);

    void saveEvalNodeScore(EvalUser evalUser, KpiListWrap kpiTypes, String opEmpId, EvalScorersWrap evalScorersWrap);

    void saveResetScoreEmp(EvalUser evalUser, String opEmpId, EvalResetScoreEmp resetScoreEmp);

    void saveRejectScoreEmp(EvalUser evalUser, ScoreReject scoreReject, List<EmpEvalScorer> empEvalScorers, OperationLog opLog);

    void saveTransferEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs, OperationLog log, AdminTaskOperation adminTaskOperation);

    void saveSkipEval(EvalUser evalUser, List<CompanyMsgCenter> fromCenterMsgs, OperationLog log);

    // ==================== 新增的优化方法（支持大规模数据迁移） ====================

    /**
     * 获取总迁移记录数（新增方法，支持位图初始化）
     *
     * @param migrationType 迁移类型
     * @return 总记录数
     */
    long getTotalMigrationRecordCount(String migrationType);

    /**
     * 分页获取公司ID列表（新增方法，支持断点恢复）
     *
     * @param migrationType 迁移类型
     * @param page 页码（从1开始）
     * @param pageSize 页大小
     * @return 公司ID列表
     */
    List<String> getCompanyIdsByPage(String migrationType, int page, int pageSize);

    /**
     * 分页获取用户ID列表（新增方法，支持分批处理）
     *
     * @param migrationType 迁移类型
     * @param companyId 公司ID
     * @param page 页码（从1开始）
     * @param pageSize 页大小
     * @return 用户ID列表
     */
    List<String> getUserIdsByPage(String migrationType, String companyId, int page, int pageSize);

    /**
     * 批量版本的getOnScoreEvalMingration（新增方法，提高查询性能）
     *
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @return 评分迁移数据列表
     */
    List<ScorerDataMingrationDmSvc> getOnScoreEvalMingrationBatch(TenantId companyId, List<String> userIds);

    /**
     * 批量添加员工评分员（新增优化方法）
     *
     * @param scorers 评分员列表
     */
    void batchAddEmpEvalScorer(List<EmpEvalScorer> scorers);

    /**
     * 批量保存评分结果（新增方法）
     *
     * @param scoreResults 评分结果列表
     */
    void batchSaveScoreResult(List<EvalOnScoreStage> scoreResults);

    /**
     * 批量更新评分结果（新增方法）
     *
     * @param scoreResults 评分结果列表
     */
    void batchUpdateScoreResult(List<EvalOnScoreStage> scoreResults);

    /**
     * 获取未完成的评分结果（新增方法，支持NO_FINISHED类型迁移）
     *
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @return 未完成的评分结果列表
     */
    List<EvalOnScoreStage> getUnfinishedScoreResults(TenantId companyId, List<String> userIds);

    /**
     * 处理未完成的评分结果（新增方法）
     *
     * @param unfinishedResults 未完成的评分结果列表
     */
    void processUnfinishedScoreResults(List<EvalOnScoreStage> unfinishedResults);
}
