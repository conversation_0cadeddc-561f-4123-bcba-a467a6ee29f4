package com.polaris.kpi.eval.domain.task.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 迁移进度管理实体
 * 支持序列化到文件，包含状态管理、计数统计、位置跟踪
 * 支持原子操作和线程安全
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class MigrationProgress implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 迁移类型
     */
    private String migrationType;

    /**
     * 操作人员ID
     */
    private String operatorId;

    /**
     * 迁移状态
     */
    private MigrationStatus status = MigrationStatus.PENDING;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 总记录数
     */
    private final AtomicLong totalRecords = new AtomicLong(0);

    /**
     * 已处理记录数
     */
    private final AtomicLong processedRecords = new AtomicLong(0);

    /**
     * 成功记录数
     */
    private final AtomicLong successRecords = new AtomicLong(0);

    /**
     * 失败记录数
     */
    private final AtomicLong failureRecords = new AtomicLong(0);

    /**
     * 跳过记录数
     */
    private final AtomicLong skippedRecords = new AtomicLong(0);

    /**
     * 当前批次号
     */
    private volatile int currentBatchNumber = 0;

    /**
     * 当前公司ID
     */
    private volatile String currentCompanyId;

    /**
     * 当前页码
     */
    private volatile int currentPageNumber = 1;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 配置信息
     */
    private String configJson;

    /**
     * 扩展属性
     */
    private String extendedProperties;

    /**
     * 迁移状态枚举
     */
    public enum MigrationStatus {
        PENDING("待处理"),
        RUNNING("运行中"),
        PAUSED("已暂停"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        MigrationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 构造函数
     */
    public MigrationProgress(String sessionId, String migrationType, String operatorId) {
        this.sessionId = sessionId;
        this.migrationType = migrationType;
        this.operatorId = operatorId;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 开始迁移
     */
    public void start() {
        this.status = MigrationStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        log.info("Migration started: sessionId={}", sessionId);
    }

    /**
     * 暂停迁移
     */
    public void pause() {
        this.status = MigrationStatus.PAUSED;
        this.lastUpdateTime = LocalDateTime.now();
        log.info("Migration paused: sessionId={}", sessionId);
    }

    /**
     * 完成迁移
     */
    public void complete() {
        this.status = MigrationStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        log.info("Migration completed: sessionId={}", sessionId);
    }

    /**
     * 迁移失败
     */
    public void fail(String errorMessage) {
        this.status = MigrationStatus.FAILED;
        this.errorMessage = errorMessage;
        this.endTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        log.error("Migration failed: sessionId={}, error={}", sessionId, errorMessage);
    }

    /**
     * 取消迁移
     */
    public void cancel() {
        this.status = MigrationStatus.CANCELLED;
        this.endTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
        log.info("Migration cancelled: sessionId={}", sessionId);
    }

    /**
     * 更新进度
     */
    public void updateProgress(long processed, long success, long failure, long skipped) {
        this.processedRecords.set(processed);
        this.successRecords.set(success);
        this.failureRecords.set(failure);
        this.skippedRecords.set(skipped);
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 增加处理记录数
     */
    public void incrementProcessed() {
        this.processedRecords.incrementAndGet();
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 增加成功记录数
     */
    public void incrementSuccess() {
        this.successRecords.incrementAndGet();
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 增加失败记录数
     */
    public void incrementFailure() {
        this.failureRecords.incrementAndGet();
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 增加跳过记录数
     */
    public void incrementSkipped() {
        this.skippedRecords.incrementAndGet();
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 更新当前位置
     */
    public void updateCurrentPosition(String companyId, int pageNumber, int batchNumber) {
        this.currentCompanyId = companyId;
        this.currentPageNumber = pageNumber;
        this.currentBatchNumber = batchNumber;
        this.lastUpdateTime = LocalDateTime.now();
    }

    /**
     * 获取进度百分比
     */
    public double getProgressPercentage() {
        long total = totalRecords.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) processedRecords.get() / total * 100.0;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        long processed = processedRecords.get();
        if (processed == 0) {
            return 0.0;
        }
        return (double) successRecords.get() / processed * 100.0;
    }

    /**
     * 获取失败率
     */
    public double getFailureRate() {
        long processed = processedRecords.get();
        if (processed == 0) {
            return 0.0;
        }
        return (double) failureRecords.get() / processed * 100.0;
    }

    /**
     * 是否正在运行
     */
    public boolean isRunning() {
        return status == MigrationStatus.RUNNING;
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return status == MigrationStatus.COMPLETED;
    }

    /**
     * 是否失败
     */
    public boolean isFailed() {
        return status == MigrationStatus.FAILED;
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return status == MigrationStatus.PAUSED || status == MigrationStatus.FAILED;
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format(
            "Session: %s, Status: %s, Progress: %.2f%%, " +
            "Total: %d, Processed: %d, Success: %d, Failure: %d, Skipped: %d, " +
            "Success Rate: %.2f%%, Failure Rate: %.2f%%",
            sessionId, status.getDescription(), getProgressPercentage(),
            totalRecords.get(), processedRecords.get(), successRecords.get(), 
            failureRecords.get(), skippedRecords.get(),
            getSuccessRate(), getFailureRate()
        );
    }

    @Override
    public String toString() {
        return String.format("MigrationProgress{sessionId='%s', status=%s, progress=%.2f%%}", 
                sessionId, status, getProgressPercentage());
    }
}
