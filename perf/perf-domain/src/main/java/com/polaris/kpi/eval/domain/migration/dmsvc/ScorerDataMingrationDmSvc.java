package com.polaris.kpi.eval.domain.migration.dmsvc;

import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.migration.util.ConcurrentBitmapManager;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import com.polaris.kpi.eval.domain.task.repo.OnScoreEvalRepo;
import com.polaris.kpi.eval.domain.task.entity.EvalScoreResult;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 评分数据迁移领域服务
 * 基于现有设计优化，实现分批处理和断点续传
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScorerDataMingrationDmSvc {

    private final OnScoreEvalRepo onScoreEvalRepo;
    private final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();

    /**
     * 执行优化的评分数据迁移
     * 使用位图管理和断点续传
     * 
     * @param progress 位图进度管理器
     * @param bitmapManager 位图管理器
     * @param indexCalculator 索引计算器
     */
    public void executeOptimizedMigration(OptimizedBitmapProgress progress, 
                                        ConcurrentBitmapManager bitmapManager,
                                        RecordIndexCalculator indexCalculator) {
        
        log.info("Starting optimized scorer migration for session: {}", progress.getSessionId());
        
        try {
            String migrationType = progress.getMigrationType();
            String sessionId = progress.getSessionId();
            
            // 配置参数
            int companyPageSize = 10;
            int userPageSize = 1000;
            
            // 从断点位置开始处理
            int startCompanyPage = progress.getCurrentCompanyPage();
            
            log.info("Resuming optimized migration from company page: {}", startCompanyPage);
            
            for (int companyPage = startCompanyPage; ; companyPage++) {
                // 检查是否需要暂停
                if (shouldPause(progress)) {
                    log.info("Migration paused at company page: {}", companyPage);
                    break;
                }
                
                // 内存检查和清理
                if (isMemoryThresholdExceeded()) {
                    performMemoryCleanup(progress, bitmapManager);
                }
                
                // 使用OnScoreEvalRepo获取公司列表
                List<String> companyIds = onScoreEvalRepo.getCompanyIdsByPage(migrationType, companyPage, companyPageSize);
                if (companyIds.isEmpty()) {
                    log.info("No more companies to process, breaking at page: {}", companyPage);
                    break;
                }
                
                // 处理每个公司
                for (String companyId : companyIds) {
                    processCompanyWithOptimizedBitmap(progress, companyId, userPageSize, bitmapManager, indexCalculator);
                    progress.updatePosition(companyId, companyPage, 1);
                }
                
                // 更新当前页码
                progress.updatePosition(null, companyPage + 1, 1);
                
                // 定期保存进度
                if (companyPage % 5 == 0) {
                    progress.saveMetadata();
                    bitmapManager.flushAllSegments(sessionId);
                    log.info("Progress saved at company page: {}", companyPage);
                }
            }
            
            // 完成迁移
            if (isAllProcessed(progress)) {
                progress.complete();
                log.info("Optimized migration completed successfully for session: {}", progress.getSessionId());
            }
            
        } catch (Exception e) {
            log.error("Optimized migration failed for session: {}", progress.getSessionId(), e);
            progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
            progress.saveMetadata();
            throw e;
        }
    }

    /**
     * 使用优化位图处理单个公司
     * 
     * @param progress 进度管理器
     * @param companyId 公司ID
     * @param userPageSize 用户页大小
     * @param bitmapManager 位图管理器
     * @param indexCalculator 索引计算器
     */
    private void processCompanyWithOptimizedBitmap(OptimizedBitmapProgress progress, String companyId, 
                                                 int userPageSize, ConcurrentBitmapManager bitmapManager,
                                                 RecordIndexCalculator indexCalculator) {
        
        log.debug("Processing company with optimized bitmap: {}", companyId);
        
        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();
        
        // 确定起始用户页
        int startUserPage = companyId.equals(progress.getCurrentCompanyId()) ? 
                progress.getCurrentUserPage() : 1;
        
        for (int userPage = startUserPage; ; userPage++) {
            // 使用OnScoreEvalRepo获取用户列表
            List<String> userIds = onScoreEvalRepo.getUserIdsByPage(migrationType, companyId, userPage, userPageSize);
            if (userIds.isEmpty()) {
                log.debug("No more users for company: {}, page: {}", companyId, userPage);
                break;
            }
            
            // 位图检查：过滤已处理的用户
            List<String> usersToProcess = new ArrayList<>();
            List<Long> indexesToProcess = new ArrayList<>();
            
            for (int i = 0; i < userIds.size(); i++) {
                String userId = userIds.get(i);
                
                // 计算全局索引
                long globalIndex = indexCalculator.calculateGlobalIndex(
                        companyId, progress.getCurrentCompanyPage(), userPage, i, 10, userPageSize);
                
                // 位图检查：如果已处理则跳过
                if (!bitmapManager.getBit(sessionId, globalIndex, progress.getSegmentSize())) {
                    usersToProcess.add(userId);
                    indexesToProcess.add(globalIndex);
                } else {
                    log.debug("User already processed, skipping: companyId={}, userId={}, index={}", 
                            companyId, userId, globalIndex);
                }
            }
            
            if (!usersToProcess.isEmpty()) {
                // 批量处理用户
                processBatchUsersWithExistingMethods(progress, companyId, usersToProcess, indexesToProcess, 
                                                   migrationType, bitmapManager);
            } else {
                log.debug("All users in this page already processed: companyId={}, page={}", companyId, userPage);
            }
            
            // 更新用户页位置
            progress.updatePosition(companyId, progress.getCurrentCompanyPage(), userPage + 1);
            
            // 定期保存进度
            if (userPage % 10 == 0) {
                progress.saveMetadata();
            }
        }
    }

    /**
     * 使用您现有的OnScoreEvalRepo方法批量处理用户
     * 
     * @param progress 进度管理器
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @param indexes 对应的全局索引列表
     * @param migrationType 迁移类型
     * @param bitmapManager 位图管理器
     */
    private void processBatchUsersWithExistingMethods(OptimizedBitmapProgress progress, String companyId, 
                                                    List<String> userIds, List<Long> indexes, 
                                                    String migrationType, ConcurrentBitmapManager bitmapManager) {
        
        String sessionId = progress.getSessionId();
        
        try {
            log.debug("Processing batch users: companyId={}, count={}, type={}", companyId, userIds.size(), migrationType);
            
            // 尝试批量处理
            boolean batchSuccess = false;
            
            if ("FINISHED".equals(migrationType)) {
                batchSuccess = processBatchFinishedUsers(companyId, userIds);
            } else if ("NO_FINISHED".equals(migrationType)) {
                batchSuccess = processBatchNoFinishedUsers(companyId, userIds);
            }
            
            if (batchSuccess) {
                // 批量成功：更新所有位图
                for (int i = 0; i < indexes.size(); i++) {
                    Long globalIndex = indexes.get(i);
                    bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                    progress.markProcessed(globalIndex, true);
                }
                
                log.info("Successfully batch processed users: companyId={}, count={}", companyId, userIds.size());
                
            } else {
                // 批量失败：逐个处理
                log.warn("Batch processing failed, falling back to individual processing: companyId={}", companyId);
                processIndividualUsers(progress, companyId, userIds, indexes, migrationType, bitmapManager);
            }
            
        } catch (Exception e) {
            log.error("Error in batch processing, falling back to individual processing: companyId={}", companyId, e);
            processIndividualUsers(progress, companyId, userIds, indexes, migrationType, bitmapManager);
        }
    }

    /**
     * 批量处理已完成的用户
     * 集成您现有的OnScoreEvalRepo业务逻辑
     * 
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    private boolean processBatchFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Batch processing finished users: companyId={}, count={}", companyId, userIds.size());
            
            // 1. 使用OnScoreEvalRepo查询需要迁移的评分数据
            List<EvalScoreResult> scoreResults = onScoreEvalRepo.getOnScoreEvalMingration(companyId, userIds);
            
            if (scoreResults.isEmpty()) {
                log.debug("No score results found for finished users: companyId={}", companyId);
                return true; // 没有数据也算成功
            }
            
            // 2. 使用OnScoreEvalRepo的批量保存方法
            onScoreEvalRepo.batchSaveScoreResult(scoreResults);
            
            // 3. 使用OnScoreEvalRepo的批量更新方法
            onScoreEvalRepo.batchUpdateScoreResult(scoreResults);
            
            // 4. 构建评分员数据并批量添加
            List<EmpEvalScorer> scorers = buildEmpEvalScorers(companyId, userIds, scoreResults);
            if (!scorers.isEmpty()) {
                onScoreEvalRepo.batchAddEmpEvalScorer(scorers);
            }
            
            log.debug("Successfully batch processed finished users: companyId={}, scoreResults={}, scorers={}", 
                    companyId, scoreResults.size(), scorers.size());
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process finished users: companyId={}, userCount={}", companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 批量处理未完成的用户
     * 
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    private boolean processBatchNoFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Batch processing no-finished users: companyId={}, count={}", companyId, userIds.size());
            
            // 根据您的具体业务逻辑实现未完成用户的处理
            // 这里可能涉及不同的OnScoreEvalRepo方法
            
            // 示例：查询未完成的评分数据
            List<EvalScoreResult> unfinishedResults = onScoreEvalRepo.getUnfinishedScoreResults(companyId, userIds);
            
            if (!unfinishedResults.isEmpty()) {
                // 处理未完成的数据
                onScoreEvalRepo.processUnfinishedScoreResults(unfinishedResults);
            }
            
            log.debug("Successfully batch processed no-finished users: companyId={}, results={}", 
                    companyId, unfinishedResults.size());
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process no-finished users: companyId={}, userCount={}", companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 逐个处理用户（批量失败时的降级处理）
     * 
     * @param progress 进度管理器
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @param indexes 对应的全局索引列表
     * @param migrationType 迁移类型
     * @param bitmapManager 位图管理器
     */
    private void processIndividualUsers(OptimizedBitmapProgress progress, String companyId, 
                                      List<String> userIds, List<Long> indexes, String migrationType,
                                      ConcurrentBitmapManager bitmapManager) {
        
        String sessionId = progress.getSessionId();
        
        log.debug("Processing individual users: companyId={}, count={}", companyId, userIds.size());
        
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            Long globalIndex = indexes.get(i);
            
            try {
                // 处理单个用户
                boolean success = processIndividualUser(companyId, userId, migrationType);
                
                // 更新位图
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, success);
                
                if (success) {
                    log.debug("Successfully processed individual user: companyId={}, userId={}", companyId, userId);
                } else {
                    log.warn("Failed to process individual user: companyId={}, userId={}", companyId, userId);
                }
                
            } catch (Exception e) {
                log.error("Error processing individual user: companyId={}, userId={}", companyId, userId, e);
                
                // 即使失败也要标记为已处理，避免重复处理
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, false);
            }
        }
    }

    /**
     * 处理单个用户
     * 调用您现有的单用户处理逻辑
     * 
     * @param companyId 公司ID
     * @param userId 用户ID
     * @param migrationType 迁移类型
     * @return 是否成功
     */
    private boolean processIndividualUser(String companyId, String userId, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                return processSingleFinishedUser(companyId, userId);
            } else if ("NO_FINISHED".equals(migrationType)) {
                return processSingleNoFinishedUser(companyId, userId);
            }
            return false;
            
        } catch (Exception e) {
            log.error("Failed to process individual user: companyId={}, userId={}, type={}", 
                    companyId, userId, migrationType, e);
            return false;
        }
    }

    /**
     * 处理单个已完成用户
     * 
     * @param companyId 公司ID
     * @param userId 用户ID
     * @return 是否成功
     */
    private boolean processSingleFinishedUser(String companyId, String userId) {
        try {
            // 查询单个用户的评分数据
            List<String> singleUserList = List.of(userId);
            List<EvalScoreResult> scoreResults = onScoreEvalRepo.getOnScoreEvalMingration(companyId, singleUserList);
            
            if (!scoreResults.isEmpty()) {
                // 保存和更新评分结果
                onScoreEvalRepo.batchSaveScoreResult(scoreResults);
                onScoreEvalRepo.batchUpdateScoreResult(scoreResults);
                
                // 添加评分员
                List<EmpEvalScorer> scorers = buildEmpEvalScorers(companyId, singleUserList, scoreResults);
                if (!scorers.isEmpty()) {
                    onScoreEvalRepo.batchAddEmpEvalScorer(scorers);
                }
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to process single finished user: companyId={}, userId={}", companyId, userId, e);
            return false;
        }
    }

    /**
     * 处理单个未完成用户
     * 
     * @param companyId 公司ID
     * @param userId 用户ID
     * @return 是否成功
     */
    private boolean processSingleNoFinishedUser(String companyId, String userId) {
        try {
            // 根据您的业务逻辑处理未完成用户
            List<String> singleUserList = List.of(userId);
            List<EvalScoreResult> unfinishedResults = onScoreEvalRepo.getUnfinishedScoreResults(companyId, singleUserList);
            
            if (!unfinishedResults.isEmpty()) {
                onScoreEvalRepo.processUnfinishedScoreResults(unfinishedResults);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Failed to process single no-finished user: companyId={}, userId={}", companyId, userId, e);
            return false;
        }
    }

    /**
     * 构建评分员数据
     * 
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @param scoreResults 评分结果列表
     * @return 评分员列表
     */
    private List<EmpEvalScorer> buildEmpEvalScorers(String companyId, List<String> userIds, List<EvalScoreResult> scoreResults) {
        // 根据您的业务逻辑构建EmpEvalScorer对象
        // 这里提供一个示例实现
        
        return scoreResults.stream()
                .filter(result -> userIds.contains(result.getUserId()))
                .map(result -> {
                    EmpEvalScorer scorer = new EmpEvalScorer();
                    scorer.setCompanyId(companyId);
                    scorer.setUserId(result.getUserId());
                    scorer.setScoreId(result.getScoreId());
                    scorer.setEvaluatorId(result.getEvaluatorId());
                    scorer.setCreateTime(java.time.LocalDateTime.now());
                    return scorer;
                })
                .collect(Collectors.toList());
    }

    // 辅助方法
    
    /**
     * 检查是否应该暂停
     */
    private boolean shouldPause(OptimizedBitmapProgress progress) {
        OptimizedBitmapProgress latest = OptimizedBitmapProgress.loadMetadata(progress.getSessionId());
        return latest != null && latest.getStatus() == OptimizedBitmapProgress.MigrationStatus.PAUSED;
    }

    /**
     * 检查内存阈值是否超出
     */
    private boolean isMemoryThresholdExceeded() {
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long usedMemoryMB = heapMemoryUsage.getUsed() / (1024 * 1024);
        long maxMemoryMB = heapMemoryUsage.getMax() / (1024 * 1024);
        
        double usagePercentage = (double) usedMemoryMB / maxMemoryMB * 100;
        return usagePercentage > 80.0; // 80%阈值
    }

    /**
     * 执行内存清理
     */
    private void performMemoryCleanup(OptimizedBitmapProgress progress, ConcurrentBitmapManager bitmapManager) {
        log.warn("Memory threshold exceeded, performing cleanup");
        
        // 保存当前进度
        progress.saveMetadata();
        
        // 清理位图段缓存
        bitmapManager.flushAllSegments(progress.getSessionId());
        
        // 强制垃圾回收
        System.gc();
        
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("Memory cleanup completed");
    }

    /**
     * 检查是否全部处理完成
     */
    private boolean isAllProcessed(OptimizedBitmapProgress progress) {
        return progress.getProcessedCount().get() >= progress.getTotalRecords().get();
    }

    // 原有方法的占位符（保持向后兼容）
    
    public void executeOriginalMigration(String sessionId, String migrationType, String operatorId) {
        // 您原有的迁移逻辑
        log.info("Executing original migration: sessionId={}, type={}, operator={}", sessionId, migrationType, operatorId);
    }

    public boolean resumeOriginalMigration(String sessionId) {
        // 您原有的恢复逻辑
        log.info("Resuming original migration: {}", sessionId);
        return true;
    }

    public boolean pauseOriginalMigration(String sessionId) {
        // 您原有的暂停逻辑
        log.info("Pausing original migration: {}", sessionId);
        return true;
    }

    public Object getOriginalMigrationStatus(String sessionId) {
        // 您原有的状态查询逻辑
        log.debug("Getting original migration status: {}", sessionId);
        return null;
    }

    public boolean cleanupOriginalMigration(String sessionId) {
        // 您原有的清理逻辑
        log.info("Cleaning up original migration: {}", sessionId);
        return true;
    }

    public String getOriginalMigrationStatistics(String sessionId) {
        // 您原有的统计逻辑
        return "Original migration statistics for: " + sessionId;
    }
}
