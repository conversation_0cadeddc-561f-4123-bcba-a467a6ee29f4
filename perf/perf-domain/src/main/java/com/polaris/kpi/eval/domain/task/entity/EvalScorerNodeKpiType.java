package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.Pecent;
import cn.com.polaris.kpi.eval.Rater;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.empeval.EmpEvalKpiType;
import com.polaris.kpi.eval.domain.task.entity.empeval.PerfEvalTypeResult;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.INodeConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreValueConf;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.*;

@Setter
@Getter
@NoArgsConstructor
public class EvalScorerNodeKpiType extends EvalScorerNodeScoreItemBase {
    private String id;//
    private String kpiTypeId;//指标类id
    @JSONField(serialize = false, deserialize = false)
    private boolean updateTypeLevel;                 //更新维度的等级结果
    private Integer scoreOptType;                 //维度考核类型

    @Getter
    private List<EvalScorerNodeKpiItem> scorerNodeKpiItems = new ArrayList<>();//维度下的考核指标

    public EvalScorerNodeKpiType(String companyId, String opEmpId, String taskId, String taskUserId, String empId,
                                 String kpiTypeId, Rater rater, INodeConf iNodeConf, List<EvalKpi> kpiItems) {

        super(companyId, opEmpId, taskId, taskUserId, empId, rater, iNodeConf);
        this.id = UUID.randomUUID().toString();
        this.kpiTypeId = kpiTypeId;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
        this.version = 0;

        kpiItems.stream().map(kpiItem -> new EvalScorerNodeKpiItem(getCompanyId().getId(), opEmpId, taskId,
                taskUserId, empId, kpiItem.getKpiTypeId(), kpiItem.getKpiItemId(), rater, iNodeConf)).forEach(this::addScorerNodeKpiItem);
    }

    public void initBase(String companyId, String opEmpId,String scorerScoreNodeId) {
        if (Objects.isNull(id)) {
            this.id = UUID.randomUUID().toString();
        }
        this.companyId = new TenantId(companyId);
        this.scorerScoreNodeId = scorerScoreNodeId;
        this.createdUser = opEmpId;
        this.updatedUser = opEmpId;
        this.createdTime = new Date();
        this.updatedTime = new Date();
    }

    public void builderItemWaitScore(String dingCorpId, List<String> defaultCustomScoreConfig,
                                     List<String> defaultPlusSubScoreConfig,
                                     IndLevelGroup indLevelGroup,
                                     ScoreValueConf scoreValueConf,
                                     List<EvalKpi> kpis){
        ListWrap<EvalKpi> kpiListWrap = new ListWrap<>(kpis).asMap(EvalKpi::asKpiItemKey);
        for (EvalScorerNodeKpiItem scorerNodeKpiItem : scorerNodeKpiItems) {
            EvalKpi kpi = kpiListWrap.mapGet(scorerNodeKpiItem.asKpiItemKey());
            if (kpi == null){
                continue;
            }
            scorerNodeKpiItem.builderItemWaitScore(kpi,dingCorpId, defaultCustomScoreConfig, defaultPlusSubScoreConfig, indLevelGroup, scoreValueConf);
        }
    }
    public void initIdNull(){
        this.id = null;
        this.scorerScoreNodeId = null;
        this.scorerNodeKpiItems.forEach(EvalScorerNodeKpiItem::initIdNull);
    }

    public boolean onlyTypeEval(){
        return null != this.scoreOptType && this.scoreOptType == 8;
    }
    public boolean needTypeAndItemEval(){
        return null != this.scoreOptType && (this.scoreOptType == 8 || this.scoreOptType == 1);
    }
    public void resetScoreNodeForWaitDispatch() {
        this.status = 0;
        this.score = null;
        this.scoreLevel = null;
        this.scoreComment = null;
        this.scoreAttUrl = null;
        this.scoreOption = null;
        this.updatedTime = new Date();
        if (CollUtil.isEmpty(scorerNodeKpiItems)){
            return;
        }
        scorerNodeKpiItems.forEach(EvalScorerNodeKpiItem::resetScoreNodeForWaitDispatch);
    }

    public void autoSubmitInitZero(){
        for(EvalScorerNodeKpiItem scorerNodeKpiItem:this.scorerNodeKpiItems){
           scorerNodeKpiItem.setScore(BigDecimal.ZERO);
        }
    }

    public void accpSubmitLevel(EmpEvalKpiType type, Map<String, EvalKpi> upKpiMap) {
        this.setUpdateTypeLevel(true);
        type.setTypeLevel(this.getScoreLevel());
        // itemMap
        Map<String, EvalScorerNodeKpiItem> itemMap = this.getScoreKpiItemMap();
        if (MapUtil.isEmpty(itemMap)) {
            return;
        }
        type.getItems().stream().filter(kpi -> itemMap.containsKey(kpi.getKpiItemId())).forEach(kpi -> {
            EvalScorerNodeKpiItem kpiItem = itemMap.get(kpi.getKpiItemId());
            kpi.setIndLevel(kpiItem.getScoreLevel());
            upKpiMap.put(kpiItem.asKpiItemKey(), kpi);
        });
    }

    public void submitLevel(EmpEvalKpiType type, Map<String, EvalKpi> upKpiMap) {
        // itemMap
        Map<String, EvalScorerNodeKpiItem> itemMap = this.getScoreKpiItemMap();
        if (MapUtil.isEmpty(itemMap)) {
            return;
        }

        //指标打等级
        type.getItems().stream().filter(kpi -> itemMap.containsKey(kpi.getKpiItemId())).forEach(kpi -> {
            if (this.isNeedUpdateLevel(kpi)) {
                EvalScorerNodeKpiItem kpiItem = itemMap.get(kpi.getKpiItemId());
                kpi.setIndLevel(kpiItem.getScoreLevel());
                upKpiMap.put(kpiItem.asKpiItemKey(), kpi);
            }
        });
    }

    public boolean isNeedUpdateLevel(EvalKpi item) {
        if (!item.isEvalLevel()) {
            return false;
        }
        boolean containSup = new ListWrap<>(item.getV3AlreadyScorerNodes()).mapTo(EvalScorerNodeKpiItem::getScorerType).contains(SubScoreNodeEnum.SUPERIOR_SCORE.getScene());
        return !containSup || this.isSupper();
    }
    public Map<String, EvalScorerNodeKpiItem> getScoreKpiItemMap() {
        Map<String, EvalScorerNodeKpiItem> scoreKpiItemMap = new HashMap<>();
        if (CollUtil.isEmpty(this.scorerNodeKpiItems)){
            return scoreKpiItemMap;
        }
        for (EvalScorerNodeKpiItem kpiItem : this.getScorerNodeKpiItems()) {
            scoreKpiItemMap.put(kpiItem.getKpiItemId(), kpiItem);
        }
        return scoreKpiItemMap;
    }

    public void copyOtherNodeData(EvalScorerNodeKpiType otherKpiType) {
        //接收评分数据
        this.scoreLevel = otherKpiType.getScoreLevel();
        this.scoreComment = otherKpiType.getScoreComment();
        this.scoreAttUrl = otherKpiType.getScoreAttUrl();
        this.scoreLevel = otherKpiType.getScoreLevel();
        this.updatedTime = otherKpiType.getUpdatedTime();
        if (CollUtil.isEmpty(otherKpiType.getScorerNodeKpiItems())){
            return;
        }
        ListWrap<EvalScorerNodeKpiItem> scoreKpiItemGroup = new ListWrap<>(otherKpiType.getScorerNodeKpiItems()).asMap(EvalScorerNodeKpiItem::getKpiItemId);
        for (EvalScorerNodeKpiItem scoreKpiItem : this.scorerNodeKpiItems) {
            if (!scoreKpiItemGroup.contains(scoreKpiItem.getKpiItemId())) {
                continue;
            }
            scoreKpiItem.copyOtherNodeData(scoreKpiItemGroup.mapGet(scoreKpiItem.getKpiItemId()));
        }
    }


    public void accpScorerSubmitTypeScore(EvalScorerNodeKpiType scorerNodeKpiType) {
        this.scoreComment = scorerNodeKpiType.getScoreComment();
        this.scoreAttUrl = scorerNodeKpiType.getScoreAttUrl();
        this.scoreLevel = scorerNodeKpiType.getScoreLevel();
        this.updatedTime = new Date();
        for(EvalScorerNodeKpiItem scorerNodeKpiItem:this.scorerNodeKpiItems){
            scorerNodeKpiItem.setScoreLevel(scorerNodeKpiType.getScoreLevel());
            scorerNodeKpiItem.setUpdatedTime(new Date());
        }
    }



    public void accpTypeScoreData(PerfEvalTypeResult scoreResult) {
        this.id = scoreResult.getId();
        //历史数据，接收评分数据
        this.scoreComment = scoreResult.getScoreComment();
        this.scoreAttUrl = scoreResult.getScoreAttUrl();
        this.scoreLevel = scoreResult.getScoreLevel();
        this.updatedTime = scoreResult.getUpdatedTime();
    }
    // 添加考核指标的方法
    public void addScorerNodeKpiItem(EvalScorerNodeKpiItem item) {
        if (item == null) {
            return;
        }
        this.scorerNodeKpiItems.add(item);
    }

    public void computeFinalScorerNodeScore(Map<String, EmpEvalKpiType> kpiTypeMap, boolean submitWithWeigh,SubScoreNodeEnum nodeEnum) {
        Map<String, EvalKpi> kpiMap = new HashMap<>();
        scorerNodeKpiItems.forEach(scoreItem -> {
            EmpEvalKpiType kpiType = kpiTypeMap.get(scoreItem.getKpiTypeId());
            kpiType.getItems().forEach(kpi -> kpiMap.put(kpi.asKpiItemKey(), kpi));
            EvalKpi kpiItem = kpiMap.get(scoreItem.asKpiItemKey());
            BigDecimal okrGoalWeight = kpiType.okrGoalWeight(kpiItem.getActionId());
            EvalItemScoreRule itemScoreRule = kpiItem.getItemScoreRule();
            BigDecimal nodePecWeight =Objects.isNull(itemScoreRule)? Pecent.ONE : itemScoreRule.nodePecWeight(nodeEnum);
            BigDecimal nodeWeight = kpiItem.isDirectional() ? Pecent.ONE : nodePecWeight;
            scoreItem.initPlusType(kpiItem);
            if (kpiItem.isPlusType() || kpiItem.isSubtractType() || kpiItem.isPlusSubType()) {
                //加分类,减分类, 加减分类,指标,维度权重都是1,仅需要计算评分人权重和维度权重
                scoreItem.computeScoreWeightScore(Pecent.ONE,Pecent.ONE,nodeWeight);
            } else if (kpiItem.isOneVoteVetoType()) {
                scoreItem.setVetoFlag(scoreItem.getVetoFlag());
            } else {//按常规指标,事项计算
                if (kpiItem.openOkrScore()){
                    scoreItem.setScore(kpiItem.getOkrScore());//如果是okr类型的，且开启了同步okr分数，则初始分数使用 OkrScore
                }
                scoreItem.computeScoreWeightScore(okrGoalWeight, kpiItem.pecWeight(submitWithWeigh), nodeWeight);
            }
        });
    }

    public BigDecimal computeFinalScorerNodeScore() {
        BigDecimal finalScorerNodeScore = BigDecimal.ZERO;
        if (CollUtil.isEmpty(this.scorerNodeKpiItems)){
            return finalScorerNodeScore;
        }

        for (EvalScorerNodeKpiItem item : this.scorerNodeKpiItems) {
            if (Objects.nonNull(item.getScore()) && item.isPassed()) { //需提交通过的
                BigDecimal itemScore = item.getItemWeightScore();
                if (item.isSubtractType()) {
                    itemScore = scaleSubtractSum(item.getItemWeightScore());
                }
                finalScorerNodeScore = finalScorerNodeScore.add(itemScore);//未乘以环节权重
            }
        }
        return finalScorerNodeScore;
    }

    public BigDecimal scaleSubtractSum(BigDecimal subtractScore) {
        if (subtractScore == null) {
            return null;
        }
        if (subtractScore.compareTo(BigDecimal.ZERO) > 0) {
            subtractScore = subtractScore.negate().setScale(2, BigDecimal.ROUND_HALF_UP);
        } else {
            subtractScore = subtractScore.abs().setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return subtractScore;
    }

    @Override
    public EvalScorerNodeKpiType  clone() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), EvalScorerNodeKpiType.class);
    }
}
