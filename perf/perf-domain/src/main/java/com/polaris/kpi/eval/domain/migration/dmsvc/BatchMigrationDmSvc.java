package com.polaris.kpi.eval.domain.migration.dmsvc;

import com.polaris.kpi.eval.domain.task.entity.MigrationProgress;
import com.polaris.kpi.eval.domain.migration.repository.MigrationProgressRepository;
import com.polaris.kpi.eval.domain.migration.repository.MigrationDataRepository;
import com.polaris.kpi.eval.domain.migration.entity.MigrationBatch;
import com.polaris.kpi.eval.domain.migration.entity.MigrationRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 批量数据迁移领域服务
 * 负责执行分批处理和断点续传逻辑
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BatchMigrationDmSvc {

    private final MigrationProgressRepository migrationProgressRepository;
    private final MigrationDataRepository migrationDataRepository;
    private final MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();

    /**
     * 执行批量迁移
     * 
     * @param progress 迁移进度实体
     */
    @Transactional
    public void executeBatchMigration(MigrationProgress progress) {
        log.info("Starting batch migration for session: {}", progress.getSessionId());
        
        try {
            // 初始化总数据量（如果是首次执行）
            if (progress.getTotalUsers().get() == 0) {
                initializeTotalCounts(progress);
            }
            
            // 按公司分批处理
            processCompaniesBatch(progress);
            
            // 完成迁移
            if (isAllProcessed(progress)) {
                progress.complete();
                migrationProgressRepository.save(progress);
                log.info("Migration completed successfully for session: {}", progress.getSessionId());
            }
            
        } catch (Exception e) {
            log.error("Batch migration failed for session: {}", progress.getSessionId(), e);
            progress.fail(e.getMessage());
            migrationProgressRepository.save(progress);
            throw e;
        }
    }

    /**
     * 初始化总数据量
     * 
     * @param progress 迁移进度
     */
    private void initializeTotalCounts(MigrationProgress progress) {
        log.info("Initializing total counts for migration: {}", progress.getSessionId());
        
        // 获取总公司数量
        int totalCompanies = migrationDataRepository.getTotalCompanyCount(progress.getMigrationType());
        progress.setTotalCompanies(totalCompanies);
        
        // 获取总用户数量（预估）
        long totalUsers = migrationDataRepository.getTotalUserCount(progress.getMigrationType());
        progress.getTotalUsers().set(totalUsers);
        
        migrationProgressRepository.save(progress);
        
        log.info("Initialized counts - Companies: {}, Users: {}", totalCompanies, totalUsers);
    }

    /**
     * 按公司分批处理
     * 
     * @param progress 迁移进度
     */
    private void processCompaniesBatch(MigrationProgress progress) {
        int companyPageSize = progress.getBatchConfig().getCompanyPageSize();
        int currentPage = progress.getCurrentCompanyPage();
        
        while (currentPage <= calculateTotalCompanyPages(progress)) {
            // 检查是否需要暂停
            if (shouldPause(progress)) {
                log.info("Migration paused at company page: {}", currentPage);
                break;
            }
            
            // 检查内存使用情况
            if (isMemoryThresholdExceeded(progress)) {
                log.warn("Memory threshold exceeded, triggering GC and pausing briefly");
                System.gc();
                try {
                    Thread.sleep(1000); // 暂停1秒让GC完成
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 获取当前页的公司列表
            List<String> companyIds = migrationDataRepository.getCompanyIdsByPage(
                    progress.getMigrationType(), currentPage, companyPageSize);
            
            if (companyIds.isEmpty()) {
                log.info("No more companies to process, breaking at page: {}", currentPage);
                break;
            }
            
            // 处理每个公司的用户数据
            for (String companyId : companyIds) {
                processCompanyUsers(progress, companyId);
                
                // 更新公司处理状态
                progress.setCompanyStatus(companyId, MigrationProgress.CompanyProcessStatus.COMPLETED);
                progress.incrementCompanyProcessed();
            }
            
            // 更新当前页码
            currentPage++;
            progress.updateCurrentPosition(null, currentPage, 1);
            
            // 定期保存进度
            if (currentPage % 5 == 0) { // 每5页保存一次
                migrationProgressRepository.save(progress);
                log.info("Progress saved at company page: {}", currentPage);
            }
            
            // 批次间隔
            sleepBetweenBatches(progress.getBatchConfig().getBatchIntervalMs());
        }
    }

    /**
     * 处理单个公司的用户数据
     * 
     * @param progress 迁移进度
     * @param companyId 公司ID
     */
    private void processCompanyUsers(MigrationProgress progress, String companyId) {
        log.debug("Processing users for company: {}", companyId);
        
        progress.setCompanyStatus(companyId, MigrationProgress.CompanyProcessStatus.PROCESSING);
        progress.setCurrentCompanyId(companyId);
        
        int userPageSize = progress.getBatchConfig().getUserPageSize();
        int userPage = (companyId.equals(progress.getCurrentCompanyId())) ? 
                progress.getCurrentUserPage() : 1;
        
        AtomicLong companyProcessedCount = new AtomicLong(0);
        AtomicLong companySuccessCount = new AtomicLong(0);
        AtomicLong companyFailureCount = new AtomicLong(0);
        AtomicLong companySkipCount = new AtomicLong(0);
        
        while (true) {
            // 获取用户数据批次
            MigrationBatch batch = migrationDataRepository.getUserBatch(
                    progress.getMigrationType(), companyId, userPage, userPageSize);
            
            if (batch.isEmpty()) {
                log.debug("No more users for company: {}, page: {}", companyId, userPage);
                break;
            }
            
            // 处理批次数据
            processBatch(batch, companyProcessedCount, companySuccessCount, 
                    companyFailureCount, companySkipCount, progress);
            
            // 更新进度
            progress.updateCurrentPosition(companyId, progress.getCurrentCompanyPage(), userPage + 1);
            
            // 自动保存进度
            if (companyProcessedCount.get() % progress.getBatchConfig().getAutoSaveInterval() == 0) {
                migrationProgressRepository.save(progress);
            }
            
            userPage++;
            
            // 批次间隔
            sleepBetweenBatches(progress.getBatchConfig().getBatchIntervalMs());
        }
        
        // 更新总计数
        progress.incrementCounts(
                companyProcessedCount.get(),
                companySuccessCount.get(),
                companyFailureCount.get(),
                companySkipCount.get()
        );
        
        log.info("Completed company: {}, processed: {}, success: {}, failure: {}, skip: {}",
                companyId, companyProcessedCount.get(), companySuccessCount.get(),
                companyFailureCount.get(), companySkipCount.get());
    }

    /**
     * 处理单个批次
     * 
     * @param batch 数据批次
     * @param processedCount 已处理计数
     * @param successCount 成功计数
     * @param failureCount 失败计数
     * @param skipCount 跳过计数
     * @param progress 迁移进度
     */
    private void processBatch(MigrationBatch batch, AtomicLong processedCount, 
                             AtomicLong successCount, AtomicLong failureCount, 
                             AtomicLong skipCount, MigrationProgress progress) {
        
        for (MigrationRecord record : batch.getRecords()) {
            try {
                // 检查记录是否需要跳过
                if (shouldSkipRecord(record, progress)) {
                    skipCount.incrementAndGet();
                    continue;
                }
                
                // 执行数据迁移逻辑
                boolean success = migrationDataRepository.migrateRecord(record, progress.getMigrationType());
                
                if (success) {
                    successCount.incrementAndGet();
                } else {
                    failureCount.incrementAndGet();
                    log.warn("Failed to migrate record: {}", record.getId());
                }
                
            } catch (Exception e) {
                failureCount.incrementAndGet();
                log.error("Error processing record: {}", record.getId(), e);
            } finally {
                processedCount.incrementAndGet();
            }
        }
    }

    /**
     * 检查是否应该跳过记录
     * 
     * @param record 迁移记录
     * @param progress 迁移进度
     * @return 是否跳过
     */
    private boolean shouldSkipRecord(MigrationRecord record, MigrationProgress progress) {
        // 实现跳过逻辑，例如：
        // 1. 记录已经存在
        // 2. 记录不符合迁移条件
        // 3. 记录数据不完整
        return migrationDataRepository.isRecordAlreadyMigrated(record.getId(), progress.getMigrationType());
    }

    /**
     * 检查是否应该暂停
     * 
     * @param progress 迁移进度
     * @return 是否暂停
     */
    private boolean shouldPause(MigrationProgress progress) {
        // 重新加载最新状态
        MigrationProgress latestProgress = migrationProgressRepository.findBySessionId(progress.getSessionId());
        return latestProgress != null && latestProgress.getStatus() == MigrationProgress.MigrationStatus.PAUSED;
    }

    /**
     * 检查内存阈值是否超出
     * 
     * @param progress 迁移进度
     * @return 是否超出阈值
     */
    private boolean isMemoryThresholdExceeded(MigrationProgress progress) {
        if (!progress.getBatchConfig().isEnableMemoryMonitoring()) {
            return false;
        }
        
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        long usedMemoryMB = heapMemoryUsage.getUsed() / (1024 * 1024);
        long maxMemoryMB = heapMemoryUsage.getMax() / (1024 * 1024);
        
        double usagePercentage = (double) usedMemoryMB / maxMemoryMB * 100;
        
        log.debug("Memory usage: {}MB / {}MB ({}%)", usedMemoryMB, maxMemoryMB, String.format("%.2f", usagePercentage));
        
        return usedMemoryMB > progress.getBatchConfig().getMemoryThresholdMb();
    }

    /**
     * 计算总公司页数
     * 
     * @param progress 迁移进度
     * @return 总页数
     */
    private int calculateTotalCompanyPages(MigrationProgress progress) {
        int totalCompanies = progress.getTotalCompanies();
        int pageSize = progress.getBatchConfig().getCompanyPageSize();
        return (int) Math.ceil((double) totalCompanies / pageSize);
    }

    /**
     * 检查是否全部处理完成
     * 
     * @param progress 迁移进度
     * @return 是否完成
     */
    private boolean isAllProcessed(MigrationProgress progress) {
        return progress.getProcessedCompanies().get() >= progress.getTotalCompanies();
    }

    /**
     * 批次间休眠
     * 
     * @param intervalMs 间隔毫秒数
     */
    private void sleepBetweenBatches(long intervalMs) {
        if (intervalMs > 0) {
            try {
                Thread.sleep(intervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Sleep interrupted between batches");
            }
        }
    }
}
