package com.polaris.kpi.eval.domain.task.entity;

import cn.com.polaris.kpi.eval.ConcurrentBitMap;
import cn.com.polaris.kpi.eval.RecordIndexCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 数据迁移配置类
 * 确保位图管理和索引计算组件被正确初始化
 */
@Configuration
@Slf4j
public class MigrationConfig {

    /**
     * 配置并发位图管理器
     * 直接创建Bean，确保组件可用
     */
    @Bean
    @Primary
    public ConcurrentBitMap concurrentBitMap() {
        log.info("Initializing ConcurrentBitMap for optimized migration");
        try {
            return new ConcurrentBitMap();
        } catch (Exception e) {
            log.warn("Failed to initialize ConcurrentBitMap: {}", e.getMessage());
            return new ConcurrentBitMap(); // 返回默认实例
        }
    }

    /**
     * 配置记录索引计算器
     * 直接创建Bean，确保组件可用
     */
    @Bean
    @Primary
    public RecordIndexCalculator recordIndexCalculator() {
        log.info("Initializing RecordIndexCalculator for optimized migration");
        try {
            return new RecordIndexCalculator();
        } catch (Exception e) {
            log.warn("Failed to initialize RecordIndexCalculator: {}", e.getMessage());
            return new RecordIndexCalculator(); // 返回默认实例
        }
    }
}
