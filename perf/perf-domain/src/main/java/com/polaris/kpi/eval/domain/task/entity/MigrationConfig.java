package com.polaris.kpi.eval.app.task.config;

import cn.com.polaris.kpi.eval.ConcurrentBitMap;
import cn.com.polaris.kpi.eval.RecordIndexCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 数据迁移配置类
 * 确保位图管理和索引计算组件被正确初始化
 */
@Configuration
@Slf4j
public class MigrationConfig {

    /**
     * 配置并发位图管理器
     * 当启用位图优化时才创建此Bean
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "scorer.migration.use-bitmap", havingValue = "true", matchIfMissing = true)
    public ConcurrentBitMap concurrentBitMap() {
        log.info("Initializing ConcurrentBitMap for optimized migration");
        return new ConcurrentBitMap();
    }

    /**
     * 配置记录索引计算器
     * 当启用位图优化时才创建此Bean
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "scorer.migration.use-bitmap", havingValue = "true", matchIfMissing = true)
    public RecordIndexCalculator recordIndexCalculator() {
        log.info("Initializing RecordIndexCalculator for optimized migration");
        return new RecordIndexCalculator();
    }

    /**
     * 当位图优化被禁用时，提供空的实现
     */
    @Bean
    @ConditionalOnProperty(name = "scorer.migration.use-bitmap", havingValue = "false")
    public ConcurrentBitMap disabledConcurrentBitMap() {
        log.info("ConcurrentBitMap disabled due to configuration");
        return null;
    }

    /**
     * 当位图优化被禁用时，提供空的实现
     */
    @Bean
    @ConditionalOnProperty(name = "scorer.migration.use-bitmap", havingValue = "false")
    public RecordIndexCalculator disabledRecordIndexCalculator() {
        log.info("RecordIndexCalculator disabled due to configuration");
        return null;
    }
}
