#!/bin/bash

# 数据迁移功能单元测试运行脚本
# 用于运行所有与数据迁移相关的单元测试

echo "=========================================="
echo "开始运行数据迁移功能单元测试"
echo "=========================================="

# 设置测试环境变量
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=256m"

# 进入项目根目录
cd "$(dirname "$0")"

echo "当前目录: $(pwd)"

# 清理之前的测试结果
echo "清理之前的测试结果..."
mvn clean -q

# 运行特定的测试类
echo ""
echo "1. 运行 OptimizedBitmapProgress 实体类测试（推荐：完全可运行）..."
mvn test -Dtest=OptimizedBitmapProgressTest -q

if [ $? -eq 0 ]; then
    echo "✅ OptimizedBitmapProgress 测试通过"
else
    echo "❌ OptimizedBitmapProgress 测试失败"
fi

echo ""
echo "2. 运行 ScorerDataMingrationController 控制器测试（推荐：完全可运行）..."
mvn test -Dtest=ScorerDataMingrationControllerTest -q

if [ $? -eq 0 ]; then
    echo "✅ ScorerDataMingrationController 测试通过"
else
    echo "❌ ScorerDataMingrationController 测试失败"
fi

echo ""
echo "3. 运行 ScorerDataMingrationAppSvc 服务层测试（注意：可能需要环境配置）..."
mvn test -Dtest=ScorerDataMingrationAppSvcTest -q

if [ $? -eq 0 ]; then
    echo "✅ ScorerDataMingrationAppSvc 测试通过"
else
    echo "⚠️  ScorerDataMingrationAppSvc 测试失败（可能需要环境配置）"
fi

echo ""
echo "4. 运行所有数据迁移相关测试..."
mvn test -Dtest="*Migration*Test" -q

if [ $? -eq 0 ]; then
    echo "✅ 所有数据迁移测试通过"
else
    echo "❌ 部分数据迁移测试失败"
fi

echo ""
echo "5. 生成测试报告..."
mvn surefire-report:report -q

echo ""
echo "=========================================="
echo "测试完成！"
echo "=========================================="
echo ""
echo "测试报告位置:"
echo "- HTML报告: target/site/surefire-report.html"
echo "- XML报告: target/surefire-reports/"
echo ""
echo "如需查看详细测试输出，请移除 -q 参数重新运行"
echo ""

# 检查测试覆盖率（如果配置了JaCoCo）
if [ -f "target/site/jacoco/index.html" ]; then
    echo "测试覆盖率报告: target/site/jacoco/index.html"
fi

echo ""
echo "📋 测试总结："
echo "✅ OptimizedBitmapProgress - 实体类测试，完全可运行"
echo "✅ ScorerDataMingrationController - Controller测试，完全可运行"
echo "⚠️  ScorerDataMingrationAppSvc - 服务层测试，可能需要环境配置"
echo ""
echo "💡 建议："
echo "1. 优先运行实体类和Controller测试验证核心功能"
echo "2. 服务层测试如果失败，请参考 @Docs/Testing/UnitTest_corrections.md"
echo "3. 考虑使用集成测试验证完整的业务流程"
echo ""
echo "运行完成！"
