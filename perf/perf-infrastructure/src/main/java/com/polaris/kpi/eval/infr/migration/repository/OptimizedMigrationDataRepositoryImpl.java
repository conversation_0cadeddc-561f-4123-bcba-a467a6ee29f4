package com.polaris.kpi.eval.infr.migration.repository;

import com.polaris.kpi.eval.domain.migration.entity.MigrationBatch;
import com.polaris.kpi.eval.domain.migration.entity.MigrationRecord;
import com.polaris.kpi.eval.domain.migration.repository.MigrationDataRepository;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.eval.infr.task.dao.EvaluateTaskDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 优化的迁移数据仓储实现
 * 集成您现有的TaskUserRepo和相关DAO
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class OptimizedMigrationDataRepositoryImpl implements MigrationDataRepository {

    private final TaskUserRepo taskUserRepo;
    private final TaskUserDao taskUserDao;
    private final EvaluateTaskDao evaluateTaskDao;
    private final RecordIndexCalculator indexCalculator;

    // 您现有的ScorerDataMigrationDmSvc相关逻辑可以在这里集成
    // private final ScorerDataMigrationDmSvc scorerDataMigrationDmSvc;

    @Override
    public int getTotalCompanyCount(String migrationType) {
        log.debug("Getting total company count for migration type: {}", migrationType);
        
        // 根据迁移类型查询公司总数
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalCompanyCountForFinished();
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalCompanyCountForNoFinished();
        }
        
        return taskUserDao.getTotalCompanyCount();
    }

    @Override
    public long getTotalUserCount(String migrationType) {
        log.debug("Getting total user count for migration type: {}", migrationType);
        
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalUserCountForFinished();
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalUserCountForNoFinished();
        }
        
        return taskUserDao.getTotalUserCount();
    }

    @Override
    public List<String> getCompanyIdsByPage(String migrationType, int page, int pageSize) {
        log.debug("Getting company IDs by page: type={}, page={}, pageSize={}", migrationType, page, pageSize);
        
        int offset = (page - 1) * pageSize;
        
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getCompanyIdsForFinished(offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getCompanyIdsForNoFinished(offset, pageSize);
        }
        
        return taskUserDao.getCompanyIds(offset, pageSize);
    }

    @Override
    public List<String> getUserIdsByPage(String migrationType, String companyId, int page, int pageSize) {
        log.debug("Getting user IDs by page: type={}, companyId={}, page={}, pageSize={}", 
                migrationType, companyId, page, pageSize);
        
        int offset = (page - 1) * pageSize;
        
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getUserIdsForFinished(companyId, offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getUserIdsForNoFinished(companyId, offset, pageSize);
        }
        
        return taskUserDao.getUserIds(companyId, offset, pageSize);
    }

    @Override
    public MigrationBatch getUserBatch(String migrationType, String companyId, int page, int pageSize) {
        log.debug("Getting user batch: type={}, companyId={}, page={}, pageSize={}", 
                migrationType, companyId, page, pageSize);
        
        List<String> userIds = getUserIdsByPage(migrationType, companyId, page, pageSize);
        
        if (userIds.isEmpty()) {
            return MigrationBatch.empty(companyId, page, pageSize);
        }
        
        // 构建迁移记录
        List<MigrationRecord> records = new ArrayList<>();
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            
            // 计算全局索引
            long globalIndex = indexCalculator.calculateGlobalIndex(
                    companyId, 1, page, i, 10, pageSize); // 假设公司页大小为10
            
            MigrationRecord record = MigrationRecord.builder()
                    .id(generateRecordId(companyId, userId))
                    .companyId(companyId)
                    .userId(userId)
                    .recordType(migrationType)
                    .sourceData(buildSourceData(companyId, userId, migrationType))
                    .status(MigrationRecord.MigrationStatus.PENDING)
                    .build();
            
            records.add(record);
        }
        
        return MigrationBatch.builder()
                .companyId(companyId)
                .pageNumber(page)
                .pageSize(pageSize)
                .records(records)
                .isLastBatch(userIds.size() < pageSize)
                .build();
    }

    @Override
    public boolean migrateUserRecord(String companyId, String userId, String migrationType) {
        log.debug("Migrating user record: companyId={}, userId={}, type={}", companyId, userId, migrationType);
        
        try {
            // 这里集成您现有的业务逻辑
            if ("FINISHED".equals(migrationType)) {
                return migrateFinishedUserRecord(companyId, userId);
            } else if ("NO_FINISHED".equals(migrationType)) {
                return migrateNoFinishedUserRecord(companyId, userId);
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("Failed to migrate user record: companyId={}, userId={}, type={}", 
                    companyId, userId, migrationType, e);
            return false;
        }
    }

    /**
     * 迁移已完成的用户记录
     * 集成您现有的batchAddEmpEvalScorer等方法
     */
    private boolean migrateFinishedUserRecord(String companyId, String userId) {
        try {
            // 1. 查询需要迁移的评分数据
            // 这里调用您现有的查询方法，例如：
            // List<EvalScoreResult> scoreResults = getOnScoreEvalMingration(companyId, userId);
            
            // 2. 批量保存评分结果
            // 使用您现有的批量保存方法：
            // taskUserRepo.batchSaveScoreResult(scoreResults);
            
            // 3. 批量更新相关状态
            // taskUserRepo.batchUpdateScoreResult(updatedResults);
            
            // 4. 调用您的batchAddEmpEvalScorer方法
            // batchAddEmpEvalScorer(companyId, userId, scorerData);
            
            // 临时实现：模拟成功
            log.debug("Successfully migrated finished user record: companyId={}, userId={}", companyId, userId);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to migrate finished user record: companyId={}, userId={}", companyId, userId, e);
            return false;
        }
    }

    /**
     * 迁移未完成的用户记录
     */
    private boolean migrateNoFinishedUserRecord(String companyId, String userId) {
        try {
            // 类似的逻辑，处理未完成的记录
            log.debug("Successfully migrated no-finished user record: companyId={}, userId={}", companyId, userId);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to migrate no-finished user record: companyId={}, userId={}", companyId, userId, e);
            return false;
        }
    }

    @Override
    public boolean migrateRecord(MigrationRecord record, String migrationType) {
        return migrateUserRecord(record.getCompanyId(), record.getUserId(), migrationType);
    }

    @Override
    public MigrationResult batchMigrateRecords(List<MigrationRecord> records, String migrationType) {
        log.debug("Batch migrating {} records for type: {}", records.size(), migrationType);
        
        int totalRecords = records.size();
        int successCount = 0;
        int failureCount = 0;
        int skipCount = 0;
        List<String> failedRecordIds = new ArrayList<>();
        
        long startTime = System.currentTimeMillis();
        
        for (MigrationRecord record : records) {
            try {
                // 检查是否需要跳过
                if (isRecordAlreadyMigrated(record.getId(), migrationType)) {
                    skipCount++;
                    continue;
                }
                
                boolean success = migrateRecord(record, migrationType);
                if (success) {
                    successCount++;
                } else {
                    failureCount++;
                    failedRecordIds.add(record.getId());
                }
                
            } catch (Exception e) {
                failureCount++;
                failedRecordIds.add(record.getId());
                log.error("Error migrating record: {}", record.getId(), e);
            }
        }
        
        long processingTime = System.currentTimeMillis() - startTime;
        
        MigrationResult result = new MigrationResult(totalRecords, successCount, failureCount, skipCount);
        result.setProcessingTimeMs(processingTime);
        result.setFailedRecordIds(failedRecordIds);
        
        log.info("Batch migration completed: {}", result);
        return result;
    }

    @Override
    public boolean isRecordAlreadyMigrated(String recordId, String migrationType) {
        // 检查记录是否已经迁移
        // 可以通过查询目标表或者状态表来判断
        return taskUserDao.isRecordMigrated(recordId, migrationType);
    }

    @Override
    public long getUserCountByCompany(String migrationType, String companyId) {
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getUserCountByCompanyForFinished(companyId);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getUserCountByCompanyForNoFinished(companyId);
        }
        
        return taskUserDao.getUserCountByCompany(companyId);
    }

    @Override
    public long getCompanyStartIndex(String migrationType, String companyId) {
        // 计算公司在全局索引中的起始位置
        // 这里可以基于公司ID的排序或者其他业务逻辑
        return taskUserDao.getCompanyStartIndex(companyId, migrationType);
    }

    /**
     * 生成记录ID
     */
    private String generateRecordId(String companyId, String userId) {
        return companyId + "_" + userId + "_" + System.currentTimeMillis();
    }

    /**
     * 构建源数据
     */
    private String buildSourceData(String companyId, String userId, String migrationType) {
        // 构建需要迁移的源数据JSON
        // 这里可以包含评分数据、用户信息等
        return String.format("{\"companyId\":\"%s\",\"userId\":\"%s\",\"type\":\"%s\"}", 
                companyId, userId, migrationType);
    }

    // 实现其他接口方法...
    @Override
    public List<String> getPendingRecordIds(String migrationType, String companyId, int limit) {
        return taskUserDao.getPendingRecordIds(migrationType, companyId, limit);
    }

    @Override
    public List<MigrationRecord> getRecordsByIds(List<String> recordIds, String migrationType) {
        // 根据ID列表获取记录
        return recordIds.stream()
                .map(id -> buildMigrationRecord(id, migrationType))
                .collect(Collectors.toList());
    }

    private MigrationRecord buildMigrationRecord(String recordId, String migrationType) {
        // 根据记录ID构建迁移记录
        String[] parts = recordId.split("_");
        String companyId = parts[0];
        String userId = parts[1];
        
        return MigrationRecord.builder()
                .id(recordId)
                .companyId(companyId)
                .userId(userId)
                .recordType(migrationType)
                .status(MigrationRecord.MigrationStatus.PENDING)
                .build();
    }

    @Override
    public boolean updateRecordStatus(String recordId, MigrationRecord.MigrationStatus status, String errorMessage) {
        return taskUserDao.updateMigrationRecordStatus(recordId, status.name(), errorMessage);
    }

    @Override
    public int batchUpdateRecordStatus(List<MigrationRecord> records) {
        return taskUserDao.batchUpdateMigrationRecordStatus(records);
    }

    @Override
    public List<MigrationRecord> getFailedRecords(String migrationType, String companyId, int limit) {
        return taskUserDao.getFailedMigrationRecords(migrationType, companyId, limit);
    }

    @Override
    public int resetFailedRecords(List<String> recordIds) {
        return taskUserDao.resetFailedMigrationRecords(recordIds);
    }

    @Override
    public MigrationProgressStats getProgressStats(String migrationType, String companyId) {
        return taskUserDao.getMigrationProgressStats(migrationType, companyId);
    }

    @Override
    public DataIntegrityResult validateDataIntegrity(String migrationType, int sampleSize) {
        return taskUserDao.validateMigrationDataIntegrity(migrationType, sampleSize);
    }

    @Override
    public int cleanupCompletedData(String migrationType, int daysToKeep) {
        return taskUserDao.cleanupCompletedMigrationData(migrationType, daysToKeep);
    }

    @Override
    public PerformanceMetrics getPerformanceMetrics(String migrationType, int timeRangeMinutes) {
        return taskUserDao.getMigrationPerformanceMetrics(migrationType, timeRangeMinutes);
    }
}
