package com.polaris.kpi.eval.infr.task.ppojo.backlog;

import cn.com.polaris.kpi.eval.ItemCustomFieldValue;
import cn.com.polaris.kpi.eval.KpiItemUsedField;
import cn.com.polaris.kpi.eval.KpiTypeUsedField;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.grade.IndLevelGroup;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore3Po;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.ibatis.annotations.JsonColumn;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效校准指标类信息
 */
@Getter
@Setter
@NoArgsConstructor
public class CalibratedEmpEvalKpiTypePo {

    private String kpiTypeId;//指标类id
    private String kpiTypeName;//指标类名称
    private String isOkr;       //是否OKR类别，true/false
    private BigDecimal kpiTypeWeight;//指标类权重
    private String kpiTypeClassify;//指标类别，为空表示已有类别，custom自定义/plus加分项/subtract减分项
    private BigDecimal maxExtraScore;//本类别最大加减分上限
    private Integer scoreOptType;
    private Integer typeOrder;
    private List<KpiTypeUsedField> kpiTypeUsedFields;
    private List<CalibratedPerfEvaluateTaskResultPo> resultPoListOld;   //评分信息
    private List<CalibratedEmpEvalKpiItemPo> resultPoList;   //评分信息
    @JsonColumn
    private IndLevelGroup indLevelGroup;         //生成人员考核时,从模板或者考核表复制来
    protected String typeLevel;                 //维度的等级
    private String ask360TempId;   // 360问卷模版ID
    private String ask360TempName;
    private String ask360TempDesc;  //360问卷模版描述
    private Integer scoringType;   // 计分方式(1:问卷平均分(题目分数之和/题目数)  2:问卷总分(题目分数之和))
    private String ask360EvalId;  //360问卷考核实例ID
    private BigDecimal ask360EvalScore;  //360问卷分数
    private Set<String> scoreTypes = new HashSet<>();
    //维度的不同环节的评分列表
    @JSONField(name = "scorerTypeScoreDetailList")
    private List<EmpEvalTaskScore3Po.ScoreNodeScore> scoreNodeScores = new ArrayList<>();
    public void addScoreNode(String scorerType,BigDecimal scorerNodeScore,List<EmpEvalTaskScore3Po.SubmitedScoreResult> submitedRs) {
        scoreNodeScores.add(new EmpEvalTaskScore3Po.ScoreNodeScore(scorerType,scorerNodeScore, submitedRs));
    }


    /**初始化指标自定义字段配置*/
    public void initKpiItemUsedFields(List<KpiItemUsedField> usedFieldDos) {
        /**初始化指标自定义字段配置*/
        if (CollUtil.isNotEmpty(this.resultPoList) && CollUtil.isNotEmpty(usedFieldDos)) {
            Map<String, List<KpiItemUsedField>> groupMap = usedFieldDos.stream()
                    .collect(Collectors.groupingBy(KpiItemUsedField::getKpiItemId));
            this.resultPoList.forEach(obj ->{
                if (groupMap.get(obj.getKpiItemId()) != null) {
                    List<ItemCustomFieldValue> fieldValueList = new ArrayList<>();
                    List<KpiItemUsedField> itemUsedFields = groupMap.get(obj.getKpiItemId());
                    if (itemUsedFields != null && itemUsedFields.size() > 0 ) {
                        itemUsedFields.forEach(item ->{
                            ItemCustomFieldValue fieldValue = new ItemCustomFieldValue();
                            BeanUtils.copyProperties(item,fieldValue);
                            fieldValue.setFieldName(item.getName());
                            fieldValue.setFieldValue(item.getValue());
                            fieldValue.setFieldStatus(item.getStatus());
                            fieldValue.setId(item.getFieldId());
                            fieldValue.setIsReq(item.getReq());
                            fieldValueList.add(fieldValue);
                        });
                        obj.setFieldValueList(fieldValueList);
                    }
                }
            });
        }
    }

    public boolean isAsk360Type() {
        return Objects.equals(this.kpiTypeClassify,"ask360");
    }
}
