package com.polaris.kpi.eval.app.task.appsvc;

import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 测试数据工厂类
 * 用于创建各种测试数据对象
 */
public class TestDataFactory {

    // 常量定义
    public static final String DEFAULT_SESSION_ID = "TEST_SESSION_001";
    public static final String DEFAULT_MIGRATION_TYPE = "FINISHED";
    public static final String DEFAULT_USER_ID = "test_user_001";
    public static final String DEFAULT_COMPANY_ID = "test_company_001";
    public static final String DEFAULT_OPERATOR_ID = "test_operator_001";

    /**
     * 创建基础的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createBasicProgress() {
        return createBasicProgress(DEFAULT_SESSION_ID, DEFAULT_MIGRATION_TYPE);
    }

    /**
     * 创建指定会话ID和迁移类型的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createBasicProgress(String sessionId, String migrationType) {
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);
        progress.getTotalRecords().set(1000);
        progress.getProcessedCount().set(500);
        progress.getSuccessCount().set(480);
        progress.getFailureCount().set(20);
        return progress;
    }

    /**
     * 创建运行中的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createRunningProgress() {
        OptimizedBitmapProgress progress = createBasicProgress();
        progress.start();
        return progress;
    }

    /**
     * 创建已完成的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createCompletedProgress() {
        OptimizedBitmapProgress progress = createBasicProgress();
        progress.start();
        progress.getTotalRecords().set(1000);
        progress.getProcessedCount().set(1000);
        progress.getSuccessCount().set(980);
        progress.getFailureCount().set(20);
        progress.complete();
        return progress;
    }

    /**
     * 创建带有失败记录的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createProgressWithFailures() {
        OptimizedBitmapProgress progress = createRunningProgress();
        
        // 添加不同类型的失败记录
        progress.addFailureRecord(DEFAULT_USER_ID + "_1", DEFAULT_COMPANY_ID, 1001L, 
                "数据库连接超时", "TIMEOUT_ERROR");
        progress.addFailureRecord(DEFAULT_USER_ID + "_2", DEFAULT_COMPANY_ID, 1002L, 
                "数据验证失败", "VALIDATION_ERROR");
        progress.addFailureRecord(DEFAULT_USER_ID + "_3", DEFAULT_COMPANY_ID, 1003L, 
                "网络连接异常", "NETWORK_ERROR");
        progress.addFailureRecord(DEFAULT_USER_ID + "_4", DEFAULT_COMPANY_ID, 1004L, 
                "数据库连接超时", "TIMEOUT_ERROR");
        
        return progress;
    }

    /**
     * 创建带有状态变化记录的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createProgressWithStatusChanges() {
        OptimizedBitmapProgress progress = createRunningProgress();
        progress.enableIncrementalDetection(30);
        
        // 添加状态变化记录
        progress.recordStatusChange(DEFAULT_USER_ID + "_1", DEFAULT_COMPANY_ID, 
                "FINISHED", "NO_FINISHED");
        progress.recordStatusChange(DEFAULT_USER_ID + "_2", DEFAULT_COMPANY_ID, 
                "NO_FINISHED", "FINISHED");
        progress.recordStatusChange(DEFAULT_USER_ID + "_3", DEFAULT_COMPANY_ID, 
                "FINISHED", "NO_FINISHED");
        
        return progress;
    }

    /**
     * 创建带有动态用户的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createProgressWithDynamicUsers() {
        OptimizedBitmapProgress progress = createRunningProgress();
        progress.enableDynamicExpansion(15);
        
        // 添加动态用户
        progress.addDynamicUser(DEFAULT_USER_ID + "_dynamic_1", DEFAULT_COMPANY_ID, 
                "FINISHED", "MANUAL");
        progress.addDynamicUser(DEFAULT_USER_ID + "_dynamic_2", DEFAULT_COMPANY_ID, 
                "NO_FINISHED", "AUTO_DETECTION");
        progress.addDynamicUser(DEFAULT_USER_ID + "_dynamic_3", DEFAULT_COMPANY_ID, 
                "FINISHED", "BATCH_MANUAL");
        
        // 标记一个为已处理
        progress.markDynamicUserAsProcessed(DEFAULT_USER_ID + "_dynamic_1", 2001L);
        
        return progress;
    }

    /**
     * 创建分离式任务的OptimizedBitmapProgress对象
     */
    public static OptimizedBitmapProgress createSeparatedTaskProgress(OptimizedBitmapProgress.TaskType taskType) {
        OptimizedBitmapProgress progress = createRunningProgress();
        String relatedSessionId = "RELATED_" + DEFAULT_SESSION_ID;
        progress.enableTaskSeparation(taskType, relatedSessionId);
        return progress;
    }

    /**
     * 创建失败记录列表
     */
    public static List<OptimizedBitmapProgress.FailureRecord> createFailureRecords(int count) {
        List<OptimizedBitmapProgress.FailureRecord> records = new ArrayList<>();
        
        String[] errorTypes = {"TIMEOUT_ERROR", "VALIDATION_ERROR", "NETWORK_ERROR", "DATABASE_ERROR"};
        String[] errorMessages = {"连接超时", "数据验证失败", "网络异常", "数据库错误"};
        
        for (int i = 0; i < count; i++) {
            int typeIndex = i % errorTypes.length;
            OptimizedBitmapProgress.FailureRecord record = new OptimizedBitmapProgress.FailureRecord(
                    DEFAULT_USER_ID + "_" + i,
                    DEFAULT_COMPANY_ID,
                    1000L + i,
                    errorMessages[typeIndex],
                    errorTypes[typeIndex]
            );
            records.add(record);
        }
        
        return records;
    }

    /**
     * 创建状态变化记录列表
     */
    public static List<OptimizedBitmapProgress.StatusChangeRecord> createStatusChangeRecords(int count) {
        List<OptimizedBitmapProgress.StatusChangeRecord> records = new ArrayList<>();
        
        String[] statusChanges = {"FINISHED->NO_FINISHED", "NO_FINISHED->FINISHED"};
        
        for (int i = 0; i < count; i++) {
            String[] parts = statusChanges[i % statusChanges.length].split("->");
            OptimizedBitmapProgress.StatusChangeRecord record = new OptimizedBitmapProgress.StatusChangeRecord(
                    DEFAULT_USER_ID + "_" + i,
                    DEFAULT_COMPANY_ID,
                    parts[0],
                    parts[1]
            );
            records.add(record);
        }
        
        return records;
    }

    /**
     * 创建动态用户记录列表
     */
    public static List<OptimizedBitmapProgress.DynamicUserRecord> createDynamicUserRecords(int count) {
        List<OptimizedBitmapProgress.DynamicUserRecord> records = new ArrayList<>();
        
        String[] userStatuses = {"FINISHED", "NO_FINISHED"};
        String[] addedBys = {"MANUAL", "AUTO_DETECTION", "BATCH_MANUAL"};
        
        for (int i = 0; i < count; i++) {
            OptimizedBitmapProgress.DynamicUserRecord record = new OptimizedBitmapProgress.DynamicUserRecord(
                    DEFAULT_USER_ID + "_dynamic_" + i,
                    DEFAULT_COMPANY_ID,
                    userStatuses[i % userStatuses.length],
                    addedBys[i % addedBys.length]
            );
            
            // 随机设置一些为已处理
            if (i % 3 == 0) {
                record.setGlobalIndex(2000L + i);
                record.markAsProcessed();
            }
            
            records.add(record);
        }
        
        return records;
    }

    /**
     * 创建动态用户映射（用于批量添加）
     */
    public static Map<String, Map<String, String>> createDynamicUsersMap(int count) {
        Map<String, Map<String, String>> users = new HashMap<>();
        
        String[] userStatuses = {"FINISHED", "NO_FINISHED"};
        
        for (int i = 0; i < count; i++) {
            Map<String, String> userInfo = new HashMap<>();
            userInfo.put("companyId", DEFAULT_COMPANY_ID);
            userInfo.put("userStatus", userStatuses[i % userStatuses.length]);
            
            users.put(DEFAULT_USER_ID + "_batch_" + i, userInfo);
        }
        
        return users;
    }

    /**
     * 创建分离式任务概览对象
     */
    public static ScorerDataMingrationAppSvc.SeparatedTasksOverview createSeparatedTasksOverview() {
        ScorerDataMingrationAppSvc.SeparatedTasksOverview overview = new ScorerDataMingrationAppSvc.SeparatedTasksOverview();
        
        overview.setCurrentTaskSessionId(DEFAULT_SESSION_ID);
        overview.setCurrentTaskType(OptimizedBitmapProgress.TaskType.FINISHED_ONLY);
        overview.setCurrentTaskStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        overview.setCurrentTaskProgress(65.5);
        
        overview.setRelatedTaskSessionId("RELATED_" + DEFAULT_SESSION_ID);
        overview.setRelatedTaskType(OptimizedBitmapProgress.TaskType.NO_FINISHED_ONLY);
        overview.setRelatedTaskStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        overview.setRelatedTaskProgress(72.3);
        
        overview.setCurrentTaskStatusChanges(15);
        overview.setCrossTaskChanges(8);
        overview.setInTaskChanges(7);
        
        return overview;
    }

    /**
     * 创建测试用的公司和用户数据
     */
    public static Map<String, List<String>> createTestCompanyUserData() {
        Map<String, List<String>> companyUsers = new HashMap<>();
        
        for (int companyIndex = 1; companyIndex <= 3; companyIndex++) {
            String companyId = "COMPANY_" + String.format("%03d", companyIndex);
            List<String> userIds = new ArrayList<>();
            
            for (int userIndex = 1; userIndex <= 10; userIndex++) {
                String userId = "USER_" + companyId + "_" + String.format("%03d", userIndex);
                userIds.add(userId);
            }
            
            companyUsers.put(companyId, userIds);
        }
        
        return companyUsers;
    }

    /**
     * 创建随机的会话ID
     */
    public static String createRandomSessionId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0x1000000));
    }

    /**
     * 创建测试用的时间范围
     */
    public static class TimeRange {
        private final LocalDateTime start;
        private final LocalDateTime end;
        
        public TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
        
        public LocalDateTime getStart() { return start; }
        public LocalDateTime getEnd() { return end; }
    }

    /**
     * 创建测试用的时间范围
     */
    public static TimeRange createTestTimeRange() {
        LocalDateTime now = LocalDateTime.now();
        return new TimeRange(now.minusHours(1), now);
    }
}
