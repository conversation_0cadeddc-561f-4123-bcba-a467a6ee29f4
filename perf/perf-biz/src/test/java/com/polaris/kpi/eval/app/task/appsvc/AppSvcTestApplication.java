package com.polaris.kpi.eval.app.task.appsvc;

import com.perf.www.dao.dingding.DingBaseDao;
import com.perf.www.dao.dingding.impl.DingBaseDaoImpl;
import com.perf.www.dao.dingding.mapper.dingds.DingBaseMapper;
import org.lufei.ibatis.dao.AutoBaseDao;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.lufei.ibatis.mapper.BaseAutoMapper;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.Resource;

//@EnableScheduling // 开启定时任务功能
@SpringBootApplication(
        scanBasePackages = {
                "com.polaris.dingtalk", "com.polaris.baiying", "com.polaris.weixin",
                "com.perf.www", "com.alibaba.cola", "com.polaris.kpi", "com.polaris.org",
                "com.polaris.acl", "com.polaris.kpi.org.domain.common"})
@EnableConfigurationProperties
@EnableTransactionManagement
//@EnableAsync
public class AppSvcTestApplication extends SpringBootServletInitializer {


    @Resource
    private BaseAutoMapper baseAutoMapper;
    //@Resource
    //private GlobalBaseMapper globalBaseMapper;
    @Resource
    private DingBaseMapper dingBaseMapper;

    @Bean
    public AutoBaseDao autoBaseDao() {
        DomainDaoImpl autoBaseDao = new DomainDaoImpl();
        autoBaseDao.setAutoMapper(baseAutoMapper);
        return autoBaseDao;
    }

    //@Bean
    //public GlobalBaseDao globalBaseDao() {
    //    GlobalBaseDaoImpl autoBaseDao = new GlobalBaseDaoImpl();
    //    autoBaseDao.setglobalBaseMapper(globalBaseMapper);
    //    return autoBaseDao;
    //}

    @Bean
    public DingBaseDao dingBaseDao() {
        DingBaseDaoImpl autoBaseDao = new DingBaseDaoImpl();
        autoBaseDao.setDingBaseMapper(dingBaseMapper);
        return autoBaseDao;
    }

    @Bean
    @Primary
    public TestCompanyDataSourceProperties companyDataSourceProperties() {
        return new TestCompanyDataSourceProperties();
    }


    @Bean
    public TestGlobalDataSourceProperties globalDataSourceProperties() {
        return new TestGlobalDataSourceProperties();
    }

    @Bean
    public TestDingDataSourceProperties dingDataSourceProperties() {
        return new TestDingDataSourceProperties();
    }
}
