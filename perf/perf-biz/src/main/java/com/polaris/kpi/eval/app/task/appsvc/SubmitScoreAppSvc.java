package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.EvaluateAuditSceneEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.eval.LevelManager;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.perf.www.common.em.OperationLogSceneEnum;
import com.perf.www.manager.dic.CompanySysSettingManager;
import com.polaris.acl.dept.pojo.CompanyDo;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.task.dto.sumit.*;
import com.polaris.kpi.eval.domain.task.dmsvc.CustomComputeDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.CustomDispatchDmSvc;
import com.polaris.kpi.eval.domain.task.dmsvc.SimpleComputeDmSvc;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.flow.ScoreNodeFlow;
import com.polaris.kpi.eval.domain.task.entity.grade.ScoreRule;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.SendTodoForInputOnScoreEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.*;
import com.polaris.kpi.eval.domain.task.repo.EmpEvalRuleRepo;
import com.polaris.kpi.eval.domain.task.repo.EvaluateTaskRepo;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.kpi.eval.domain.task.type.ScoringNodeEnum;
import com.polaris.kpi.eval.domain.task.type.ScoringStageChain;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.EvalMultistageSupScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EnbledSaveScorePo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.ScoreResultWithItemPo;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.domain.emp.repo.KpiEmpRepo;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.AuditEnum;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: lufei
 * @CreateTime: 2022-05-11  05:56
 * @Description: 评分应用层服务 1.0
 * @Version: 1.0
 */
@Service
@Slf4j
public class SubmitScoreAppSvc {
    @Autowired
    private EvaluateTaskRepo taskRepo;
    @Autowired
    private EvaluateTaskDao taskDao;
    @Autowired
    private TaskUserRepo userRepo;
    @Resource
    private EmpEvalRuleRepo ruleRepo;
    @Autowired
    private TaskUserDao taskUserDao;
    @Lazy
    @Autowired
    private SubmitScoreAppSvc selfRef;//为了事务融合引用自己
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private GradeDao gradeDao;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private TaskKpiItemDao itemDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private EmpEvalRuleRepo evalRuleRepo;
    @Autowired
    private KpiEmpRepo kpiEmpRepo;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private CompanySysSettingManager companySysSettingManager;

    @Autowired
    private EmpEvalScorerDao empEvalScorerDao;

    // taskBiz.3475 启动自评环节节点
    @Transactional
    public void handleSelfNodeStart(SelfNodeStart event) {
        event.startProcess();
        CycleEval cycleEval = event.getCycleEval();
        EvalUser taskUser = event.getTaskUser();
        //EmpEvalRule empEvalRule = evalRuleRepo.getEmpEvalRule(taskUser.getCompanyId(), taskUser.getId());
        if (!cycleEval.needSelfEval()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SELF).publish();
            return;
        }
        log.info("进入自评流程taskUserId:{}", taskUser.getId());
        List<EvalScoreResult> raterItems = taskUser.dispatchSelfRaterItem();
        if (CollUtil.isEmpty(raterItems)) {//配置打开了自评,实际上没有自评的指标
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SELF).publish();
            return;
        }
        //发送自评通知
        String empId = taskUser.getEmpId();
        String taskId = taskUser.getTaskId();
        userRepo.batchSaveScoreResult(raterItems);

        List<String> scorerIds = raterItems.stream().map(EvalScoreResult::getScorerId).distinct().collect(Collectors.toList());
        // 更新负责人信息
        List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
        taskUser.reviewers(raters);
        userRepo.updateTaskUser(taskUser);

        new MsgTodoAggregate(cycleEval.getCompanyId(), taskId, cycleEval.getTaskName(), empId, taskUser.getId())
                .useScene(MsgSceneEnum.TASK_SELF_SCORE, CompanyMsgActionEnum.SELF_SCORE)
                .sendExtTodo().sendExtMsg().addRecEmpId(empId).addCenterMsg().publish();
    }

    //旧的简易 360 启动互评评分
    @Transactional
    public void handlePeerNodeStart(PeerNodeStart event) {
        // 同级互评： 1. 下级互评先于同级互评 2. 没有顺序
        CycleEval cycleEval = event.getCycleEval();
        EvalUser taskUser = event.getTaskUser();
        if (!cycleEval.needMutualEval()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_PEER).publish();
            return;
        }
        //@link{EvaluateTaskBiz.handlePeerScore}
        log.info("解析互评审核人,taskId={}, empId={}, orgId={}", taskUser.getTaskId(), taskUser.getEmpId(), taskUser.getOrgId());
        //查询同级和下级互评责任人
        List<EvalAudit> audits = new ArrayList<>();
        if (cycleEval.usedPeerAfterSub()) {
            audits = taskUser.loadAudits(EvaluateAuditSceneEnum.SUB_SCORE);
        } else if (cycleEval.usedPeerAndSub()) {
            audits.addAll(taskUser.loadAudits(EvaluateAuditSceneEnum.SUB_SCORE));
            audits.addAll(taskUser.loadAudits(EvaluateAuditSceneEnum.PEER_SCORE));
        } else if (cycleEval.usedOnlySub()) {
            audits = taskUser.loadAudits(EvaluateAuditSceneEnum.SUB_SCORE);
        } else if (cycleEval.usedOnlyPeer()) {
            audits.addAll(taskUser.loadAudits(EvaluateAuditSceneEnum.PEER_SCORE));
        }
        if (CollUtil.isEmpty(audits)) {//配置打开了自评,实际上没有要评分指标
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_PEER).publish();
            return;
        }
        List<EvalScoreResult> scoreResults = taskUser.dispatchPeerAudit(audits);
        if (CollUtil.isEmpty(scoreResults)) {//配置打开了自评,实际上没有要评分指标
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_PEER).publish();
            return;
        }
        //保存展开的 score result
        this.userRepo.batchSaveScoreResult(scoreResults);
        //审核人状态修改为已分发
        this.userRepo.batchUpdateDispacthed(audits);

        List<String> scorerIds = scoreResults.stream().map(EvalScoreResult::getScorerId).distinct().collect(Collectors.toList());
        // 更新负责人信息
        List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
        taskUser.reviewers(raters);
        userRepo.updateTaskUser(taskUser);
        //  发送通知与待办
        new MsgTodoAggregate(cycleEval.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_PEER_SCORE, CompanyMsgActionEnum.PEER_SCORE)
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(scorerIds)
                .publish();
    }


    //简易 360 启动定向评分 taskBiz.3886
    @Transactional
    public void handleItemNodeStart(ItemNodeStart event) {
        event.start();
        CycleEval cycleEval = event.getCycleEval();
        EvalUser taskUser = event.getTaskUser();
        if (!taskUser.hasDirectionalItem()) {//没有配置定向评分指标
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_APPOINT).publish();
            return;
        }
        if (taskUser.wasTempTask()) {
            List<LevelManager> managers = kpiEmpRepo.listEmpsManager(new TenantId(event.getTenantId()), new EmpId(taskUser.getEmpId()));
            Map<String, List<LevelManager>> groups = new ListWrap<>(managers).groupBy(manager -> manager.getLevel() + "").getGroups();
            taskUser.setLevelManagers(groups);
        }
        //启动动定向评分 scoreResult ,通知,待办 taskBiz.3886
        List<EvalScoreResult> scoreResults = taskUser.dispatchDirectRaterItem();
        if (CollUtil.isEmpty(scoreResults)) {//配置打开了自评,实际上没有要评分指标
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_APPOINT).publish();
            return;
        }
        userRepo.batchSaveScoreResult(scoreResults);
        List<String> scorerIds = scoreResults.stream().map(result -> result.getScorerId()).distinct().collect(Collectors.toList());
        // 更新负责人信息
        List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
        taskUser.reviewers(raters);
        userRepo.updateTaskUser(taskUser);
        new MsgTodoAggregate(cycleEval.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_APPOINT_SCORE, CompanyMsgActionEnum.ITEM_SCORE)
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(scorerIds)
                .publish();

    }

    //简易 360 启动上级评分 taskBiz.
    @Transactional
    public void handleSuperiorNodeStart(SuperiorNodeStart event) {
        log.info("简易 360 启动上级评分考核人信息");
        CycleEval cycleEval = event.getCycleEval();
        EvalUser taskUser = event.getTaskUser();
        //EmpEvalRule empEvalRule = evalRuleRepo.getEmpEvalRule(taskUser.getCompanyId(), taskUser.getId());
        EmpEvalMerge empEvalMerge = evalRuleRepo.getEmpEvalMerge(taskUser.getCompanyId(), taskUser.getId(), EmpEvalMerge.all);

        //未开启过上级评分
        if (!empEvalMerge.needSuperScore()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SUP).publish();
            return;
        }
        Collection<EvalScoreResult> scoreResults = null;
        List<EvalAudit> curLevelAudits = new ArrayList<>();
        //上级评顺序类型（同时/依次）
        if (empEvalMerge.getS3SuperRater().superiorInTurn()) {//依次给第一级审核人发待办
            scoreResults = taskUser.dispatchSuperiorInTurnItems(1);
            EvalAudit curSupperAuditNode = taskUser.getCurSupperAuditNode(1);
            curLevelAudits.add(curSupperAuditNode);
        } else if (empEvalMerge.getS3SuperRater().superiorInSame()) {//同时
            CustomDispatchDmSvc dmSvc = new CustomDispatchDmSvc(taskUser);
            scoreResults = dmSvc.dispatchSuperiorSameItems();
            //curLevelAudits = dmSvc.getLeftAudits();
            curLevelAudits = taskUser.getSupperScorers();
        }
        //没有要执行上级评分的指标跳过
        if (scoreResults.isEmpty()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SUP).publish();
            return;
        }
        //保存展开的 score result
        this.userRepo.batchSaveScoreResult(scoreResults);
        //审核人状态修改为已分发
        this.userRepo.batchUpdate(curLevelAudits);

        List<String> scorerIds = scoreResults.stream().map(result -> result.getScorerId()).distinct().collect(Collectors.toList());

        // 更新负责人信息
        List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
        taskUser.reviewers(raters);
        userRepo.updateTaskUser(taskUser);
        new MsgTodoAggregate(cycleEval.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_SUP_SCORE, CompanyMsgActionEnum.SUPERIOR_SCORE)
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(scorerIds)
                .publish();
        ////把待办关联的指标存起来
        //String msgAtt = CollUtil.isEmpty(scoreResults) ? null :
        //        scoreResults.stream().map(PerfEvaluateTaskScoreResult::getId)
        //                .distinct().collect(Collectors.joining(","));
    }

    //简易 360
    @Transactional
    public Collection<EvalScoreResult> reCreateSuperiorNode(TenantId companyId, String taskUserId, boolean resetNode) {
        CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, taskUserId);
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        //未开启过上级评分
        if (!cycleEval.needSuperiorEval() || taskUser.confedNoneSupKpi()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SUP).publish();
            return Collections.emptyList();
        }
        Collection<EvalScoreResult> scoreResults = null;
        List<EvalAudit> curLevelAudits = new ArrayList<>();
        //上级评顺序类型（同时/依次）
        if (cycleEval.superiorInTurn()) {//依次给第一级审核人发待办
            scoreResults = taskUser.dispatchSuperiorInTurnItems(1);
            EvalAudit curSupperAuditNode = taskUser.getCurSupperAuditNode(1);
            curLevelAudits.add(curSupperAuditNode);
        } else if (cycleEval.superiorInSame()) {//同时
            CustomDispatchDmSvc dmSvc = new CustomDispatchDmSvc(taskUser);
            scoreResults = dmSvc.dispatchSuperiorSameItems();
            //curLevelAudits = dmSvc.getLeftAudits();
            curLevelAudits = taskUser.getSupperScorers();
        }
        //没有要执行上级评分的指标跳过
        if (scoreResults.isEmpty()) {
            new ScoreNodeEnd(cycleEval, taskUser, ScoringNodeEnum.SCORING_SUP).publish();
            return Collections.emptyList();
        }
        if (resetNode) {
            userRepo.clearTotalLevelScoreResult(taskUser.getCompanyId(), taskUserId);
            //保存展开的 score result
            taskUser.acceptScorer(scoreResults);
            this.userRepo.batchSaveScoreResult(scoreResults);
            //审核人状态修改为已分发
            this.userRepo.batchUpdate(curLevelAudits);
        }
        return scoreResults;
    }

    @Autowired
    private PlatformTransactionManager platTransaction;

    //提交评分 , 提交上级评分 taskBiz.5701-5760,提交定向评分 taskBiz.5701-5760
    //@Transactional
    public void submitScore(SubmitScoreCmd cmd) {
        //TransactionStatus transaction = platTransaction.getTransaction(new DefaultTransactionDefinition());
        TenantId companyId = cmd.getCompanyId();
        CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId());
        EvalUser taskUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        ScoreNodeFlow supNodeFlow = userRepo.getScoreNodeFlow(companyId, cmd.getTaskUserId(), cmd.getScoreNode());
        supNodeFlow.submitScoreResults(cmd.getOpEmpId(), cmd.getTotal(), cmd.getItemScoreList());
        SimpleComputeDmSvc scoreDmSvc = new SimpleComputeDmSvc(cycleEval, taskUser, cmd.getScoreNode());
        scoreDmSvc.computeItemFinalScore(supNodeFlow.getCurSubNodes(), cmd.getTotal());
        userRepo.batchUpdateScoreResult(supNodeFlow.curRaterResluts());
        userRepo.batchSaveScoreResult(cmd.totalAsList());
        //记录日志
        opLogDao.addSumitScoreLog(companyId, taskUser.getId(), cmd.getOpEmpId(), cmd.getLogScene(), scoreDmSvc.computeItemNames(), null);
        //添加评分总结
        userRepo.addEvalUserSummary(cmd.getScoreSummarys(), cmd.getOpEmpId());
        //手动提交事务
        //platTransaction.commit(transaction);
        new CancelTodoEvent(companyId, cmd.opEmpId(), taskUser.getId(), cmd.getScoreNode().todoScenes()).publish();
        ScoringNodeEnum scoringNodeEnum = ScoringNodeEnum.fromSub(cmd.getScoreNode());
        new RaterScoreNodeEnd(cycleEval, taskUser, scoringNodeEnum, cmd.getOpEmpId(), supNodeFlow).publish();
    }

    public SubmitScoreCmd autoSubmitCmd(TenantId tenantId, EvalUser evalUser) {
        List<EvalScoreResult> rs = taskUserDao.listResultByScoreType(tenantId, evalUser.getId(), AuditEnum.SELF_SCORE.getScene());
        List<PerfEvalTypeResult> typRs = taskUserDao.listTypeResultByScoreType(tenantId, evalUser.getId(), AuditEnum.SELF_SCORE.getScene()).stream().filter(r -> !r.isPassed())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(rs) && CollUtil.isEmpty(typRs)) {
            throw new KpiI18NException("error.skip.null");
        }
        EvalScoreSummary scoreSummary = new EvalScoreSummary();
        scoreSummary.asSelfScoreSummary(tenantId, evalUser.getId());
        SubmitScoreCmd cmd = new SubmitScoreCmd();
        cmd.autoCommitSelfScore(evalUser.getId(), rs, Arrays.asList(scoreSummary));
        cmd.setOpEmpId(new EmpId(evalUser.getEmpId()));
        cmd.setTypeScores(typRs);
        cmd.setTaskUserId(evalUser.getId());
        for (EvalScoreResult result : rs) {
            if (result.isPassed()) {//已提交了,不能再跳过
                throw new KpiI18NException("error.skip.null");
            }
            result.setScore(BigDecimal.ZERO);
//            EvalKpi evalKpi = kpiDao.getEvalKpi(tenantId.getId(), evalUser.getId(), result.getKpiItemId());
//            if (evalKpi.isEvalScore()) {
//                cmd.setTotalScore(BigDecimal.ZERO);
//            }
        }
        return cmd;
    }


    public SubmitScoreV3Cmd autoSubmitCmd3(TenantId tenantId, EvalUser evalUser) {
        List<String> taskUserIds = new ArrayList<>();
        taskUserIds.add(evalUser.getId());
        //查询 当前操作人在taskUserIds任务评分关系
        ListWrap<EmpEvalScorer> evalScorerGroup = empEvalScorerDao.listEmpEvalScorerByUserIds(tenantId.getId(),taskUserIds,evalUser.getEmpId());
        EmpEvalScorer scorer =  evalScorerGroup.mapGet(evalUser.getId());
        if (Objects.isNull(scorer)) {
            throw new KpiI18NException("error.skip.null");
        }
        EmpEvalScorerNode node = scorer.getCurScoreNodeOne(SubScoreNodeEnum.SELF_SCORE.getScene(),1);
        if (Objects.isNull(node)) {
            throw new KpiI18NException("error.skip.null");
        }

        if (node.isPassed()) {//已提交了,不能再跳过
            throw new KpiI18NException("error.skip.null");
        }
        node.autoSubmitInitZero();

        SubmitScoreV3Cmd cmd = new SubmitScoreV3Cmd();
        V3SubmitedEvalNodeScore nodeScore = new V3SubmitedEvalNodeScore();
        cmd.setOpEmpId(new EmpId(evalUser.getEmpId()));
        cmd.setTaskUserId(evalUser.getId());
        List<V3SubmitedEvalNodeScore> nodes = new ArrayList<>();
        BeanUtil.copyProperties(node,nodeScore);
        nodes.add(nodeScore);
        cmd.setNodeScores(nodes);
        return cmd;
    }


    public Map<String, List<SubmitScoreCmd>> skipScoreSubmit(TenantId tenantId, String taskUserId, String opEmpId) {
        //查询默认分值配置项
        List<String> defaultCustomScoreConfig = companySysSettingManager.listDefaultCustomScoreConfig(tenantId.getId());
        List<String> defaultPlusSubScoreConfig = companySysSettingManager.listDefaultPlusSubScoreConfig(tenantId.getId());
        CompanyDo companyDo = companyDao.getCompany(tenantId);
        String dingCorpId = Objects.nonNull(companyDo) ? companyDo.getDingCorpId() : null;//查询公司dingCorpId
        EmpEvalMerge rule = evalRuleRepo.getEmpEvalMerge(tenantId, taskUserId, opEmpId, EmpEvalMerge.all, 1);
        Map<String, List<EvalScoreResult>> itemMapGroup = rule.builderItemWaitScore(dingCorpId,defaultCustomScoreConfig, defaultPlusSubScoreConfig);
        Map<String, List<PerfEvalTypeResult>> typeMapGroup = rule.builderTypeWaitScore(defaultCustomScoreConfig, defaultPlusSubScoreConfig);

        Map<String, List<SubmitScoreCmd>> submitMap = new HashMap<>();
        itemMapGroup.forEach((k, v) -> {
            List<SubmitScoreCmd> cmds = new ArrayList<>();
            Map<String, List<EvalScoreResult>> scorerTypeItemMapGroup = CollStreamUtil.groupByKey(v, EvalScoreResult::getScorerType);
            Map<String, List<PerfEvalTypeResult>> scorerTypeTypeMapGroup = CollStreamUtil.groupByKey(typeMapGroup.get(k), PerfEvalTypeResult::getScorerType);
            scorerTypeItemMapGroup.forEach((key, val) -> {
                SubmitScoreCmd cmd = new SubmitScoreCmd();
                cmd.accop(tenantId, new EmpId(opEmpId), true);
                EvalScoreSummary scoreSummary = new EvalScoreSummary();
                scoreSummary.asScoreSummary(tenantId, taskUserId, key);
                cmd.ininSubmitScore(CollUtil.isEmpty(scorerTypeTypeMapGroup.get(key)) ? new ArrayList<>() : scorerTypeTypeMapGroup.get(key),
                        val, Arrays.asList(scoreSummary), taskUserId, key);
                cmd.setScorerType(key);
                cmd.submitScoreLog(tenantId, k);
                cmd.checkParamNullAndBuild();
                cmds.add(cmd);
            });
            submitMap.put(k, cmds);
        });
        return submitMap;
    }


    public List<BatchSubmitScoreCmd3.CmdWrap> skipScoreSubmitV3(TenantId tenantId, String taskUserId, String opEmpId) {
        List<BatchSubmitScoreCmd3.CmdWrap> cms = new ArrayList<>();
        //查询默认分值配置项
        List<String> defaultCustomScoreConfig = companySysSettingManager.listDefaultCustomScoreConfig(tenantId.getId());
        List<String> defaultPlusSubScoreConfig = companySysSettingManager.listDefaultPlusSubScoreConfig(tenantId.getId());
        CompanyDo companyDo = companyDao.getCompany(tenantId);
        String dingCorpId = Objects.nonNull(companyDo) ? companyDo.getDingCorpId() : null;//查询公司dingCorpId
        EmpEvalMerge rule = evalRuleRepo.getEmpEvalMerge(tenantId, taskUserId, opEmpId, EmpEvalMerge.all, 1);
        Map<String, List<EvalScorerNodeKpiItem>> itemMapGroup = rule.builderItemWaitScoreV3(dingCorpId, defaultCustomScoreConfig, defaultPlusSubScoreConfig);
        Map<String, List<EvalScorerNodeKpiType>> typeMapGroup = rule.builderTypeWaitScoreV3(defaultCustomScoreConfig, defaultPlusSubScoreConfig);
        itemMapGroup.forEach((k, v) -> {
            BatchSubmitScoreCmd3.CmdWrap cmdWrap = new BatchSubmitScoreCmd3.CmdWrap();
            List<SubmitScoreCmd3> cmds = new ArrayList<>();
            Map<String, List<EvalScorerNodeKpiItem>> scorerTypeItemMapGroup = CollStreamUtil.groupByKey(v, EvalScorerNodeKpiItem::getScorerType);
            Map<String, List<EvalScorerNodeKpiType>> scorerTypeTypeMapGroup = CollStreamUtil.groupByKey(typeMapGroup.get(k), EvalScorerNodeKpiType::getScorerType);
            scorerTypeItemMapGroup.forEach((key, val) -> {
                SubmitScoreCmd3 cmd = new SubmitScoreCmd3();
                cmd.accop(tenantId, new EmpId(opEmpId), true);
                EvalScoreSummary scoreSummary = new EvalScoreSummary();
                scoreSummary.asScoreSummary(tenantId, taskUserId, key);
                cmd.ininSubmitScore(CollUtil.isEmpty(scorerTypeTypeMapGroup.get(key)) ? new ArrayList<>() : scorerTypeTypeMapGroup.get(key),
                        val, Arrays.asList(scoreSummary), taskUserId, key);
                cmd.setScorerType(key);
                cmd.submitScoreLog(tenantId, k);
                cmd.checkParamNullAndBuild();
                cmds.add(cmd);
            });
            cmdWrap.setNodeScores(cmds);
            cmdWrap.setTaskUserId(taskUserId);
            cms.add(cmdWrap);
        });
        cms.forEach(cmdWrap -> cmdWrap.checkAndBuild(tenantId.getId(), null));
        return cms;
    }

    //提交多种环节一起s3评分
    @Transactional
    public void submitScenesScore(CycleEval cycleEval, List<SubmitScoreCmd> cmds) {
        SubmitScoreCmd submitScoreCmd = cmds.get(0);
        TenantId companyId = submitScoreCmd.getCompanyId();
        cycleEval = cycleEval == null ? taskRepo.getMergeCycleEval(companyId, submitScoreCmd.getTaskUserId()) : cycleEval;
        //EvalUser taskUser = taskDao.findPerfEvaluateTaskUser(companyId, submitScoreCmd.taskId(), submitScoreCmd.evalEmpId(), submitScoreCmd.getOrgId());
        EvalUser taskUser = userRepo.getTaskUser(companyId, submitScoreCmd.getTaskUserId());
        for (SubmitScoreCmd cmd : cmds) {
            ScoreNodeFlow supNodeFlow = userRepo.getScoreNodeFlow(companyId, cmd.getTaskUserId(), cmd.getScoreNode());
            supNodeFlow.submitScoreResults(cmd.getOpEmpId(), cmd.getTotal(), cmd.getItemScoreList());
            SimpleComputeDmSvc scoreDmSvc = new SimpleComputeDmSvc(cycleEval, taskUser, cmd.getScoreNode());
            scoreDmSvc.computeItemFinalScore(supNodeFlow.getCurSubNodes(), cmd.getTotal());
            userRepo.batchUpdateScoreResult(supNodeFlow.curRaterResluts());
            userRepo.batchSaveScoreResult(cmd.totalAsList());
            //记录日志
            opLogDao.addSumitScoreLog(companyId, taskUser.getId(), cmd.getOpEmpId(), cmd.getLogScene(), scoreDmSvc.computeItemNames(), null);
            //添加评分总结
            userRepo.addEvalUserSummary(cmd.getScoreSummarys(), cmd.getOpEmpId());
            new CancelTodoEvent(companyId, cmd.opEmpId(), taskUser.getId(), cmd.getScoreNode().todoScenes()).publish();
            ScoringNodeEnum scoringNodeEnum = ScoringNodeEnum.fromSub(cmd.getScoreNode());
            new RaterScoreNodeEnd(cycleEval, taskUser, scoringNodeEnum, cmd.getOpEmpId(), supNodeFlow).publish();
        }
    }

    //重新提交s3评分
    @Transactional
    public void reSubmitScenesScore(CycleEval cycleEval, List<SubmitScoreCmd> cmds) {
        SubmitScoreCmd submitScoreCmd = cmds.get(0);
        TenantId companyId = submitScoreCmd.getCompanyId();
        cycleEval = cycleEval == null ? taskRepo.getMergeCycleEval(companyId, submitScoreCmd.getTaskUserId()) : cycleEval;
        EvalUser taskUser = userRepo.getTaskUser(companyId, submitScoreCmd.getTaskUserId());
        for (SubmitScoreCmd cmd : cmds) {
            ScoreNodeFlow supNodeFlow = userRepo.getScoreNodeFlow(companyId, cmd.getTaskUserId(), cmd.getScoreNode(), true);
            supNodeFlow.setCheckSubmited(false);//重新提交不检查已提交
            supNodeFlow.submitScoreResults(cmd.getOpEmpId(), cmd.getTotal(), cmd.getItemScoreList());
            SimpleComputeDmSvc scoreDmSvc = new SimpleComputeDmSvc(cycleEval, taskUser, cmd.getScoreNode());
            scoreDmSvc.computeItemFinalScore(supNodeFlow.getCurSubNodes(), cmd.getTotal());
            userRepo.batchUpdateScoreResult(supNodeFlow.curRaterResluts());
            userRepo.batchSaveScoreResult(cmd.totalAsList());
            //记录日志
            OperationLogSceneEnum modifyScore = OperationLogSceneEnum.MODIFY_SCORE;
            opLogDao.addSumitScoreLog(companyId, taskUser.getId(), cmd.getOpEmpId(), modifyScore.getScene(), scoreDmSvc.computeItemNames(), null);
        }
        computeFinalResult(cycleEval, taskUser);
    }

//    public void scoreEndFix(ScoreEndFixCmd cmd) {
//        for (String taskUserId : cmd.getTaskUserIds()) {
//            EmpEvalMerge empEvalMerge = evalRuleRepo.getEmpEvalMerge(new TenantId(cmd.getCompanyId()), taskUserId, EmpEvalMerge.all);
////            CycleEval cycleEval = taskRepo.getMergeCycleEval(new TenantId(cmd.getCompanyId()), taskUserId);
//            List<String> taskUserIds = this.taskUserDao.listTaskUserIds(cmd.getCompanyId(), Arrays.asList(taskUserId));
//            if (CollUtil.isEmpty(taskUserIds)) {
//                return;
//            }
//            EvalUser taskUser = userRepo.getTaskUser(new TenantId(cmd.getCompanyId()), taskUserId);
//            if (taskUser.allScorerSubmited()) {
//                log.info("评分完成了：taskId={}, taskUserId={}", taskUser.getTaskId(), taskUser.getId());
//                new ThisStageEnded(empEvalMerge, taskUser, TalentStatus.SCORING).publish();
//            } else {
//                log.info("仍然在评分中：taskId={}, taskUserId={}", taskUser.getTaskId(), taskUser.getId());
//            }
//        }
//    }

    @Transactional
    public void reSubmitCustomScore(CycleEval cycleEval, SubmitScoreCmd cmd) {
        TenantId companyId = cmd.getCompanyId();
        cycleEval = cycleEval == null ? taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId()) : cycleEval;
        EvalUser taskUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        CustomComputeDmSvc computeDmSvc = new CustomComputeDmSvc(cycleEval.isTypeWeightOpen(), cycleEval.getTemplItemJson(), taskUser);
        //isFullScoreRange=提交不带权重的分,计算时需要计算权重
        Boolean itemWeightOpened = cycleEval.isFullScoreRange();
        computeDmSvc.computeItemFinalScore(cmd.getItemScoreList(), itemWeightOpened);
        userRepo.batchUpdateScoreResult(computeDmSvc.getCurSubNodes());
        userRepo.batchSaveScoreResult(cmd.totalAsList());
        if (computeDmSvc.allCustomScorerPassed()) {//自定直接结束
            selfRef.computeFinalResult(cycleEval, taskUser);
        }
        userRepo.finishedScoreNodeFlag(taskUser);
    }

    private SubmitScoreCmd merge2Custom(List<SubmitScoreCmd> cmds) {
        SubmitScoreCmd rs = cmds.get(0);
        rs.setScoreNode(SubScoreNodeEnum.CUSTOM);
        for (int i = 0; i < cmds.size(); i++) {

            SubmitScoreCmd cmd = cmds.get(i);
            for (EvalScoreResult result : cmd.getItemScoreList()) {
                result.setScorerType(cmd.getScorerType());
            }
            if (i == 0) {
                continue;
            }
            rs.getItemScoreList().addAll(cmd.getItemScoreList());
            rs.getScoreSummarys().addAll(cmd.getScoreSummarys());
        }
        rs.setLogScene(SubScoreNodeEnum.CUSTOM.asLogScene());
        return rs;
    }


    @Transactional
    public void reComputeScore(TenantId companyId, TaskId taskId, String taskUserId) {
        CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, taskUserId);
        if (taskUserId != null) {
            if (cycleEval.isCustom()) {
                selfRef.fixComputeItemScoreCustom(cycleEval, companyId, taskUserId);
            } else {
                log.error("selfRef={}", selfRef);
                selfRef.fixSubmitScore(cycleEval, companyId, taskUserId);
            }
            return;
        }

        List<EvalUser> evalUsers = taskDao.listFixTaskUser(companyId.getId(), taskId.getId());
        for (EvalUser evalUser : evalUsers) {
            try {
                if (cycleEval.isCustom()) {
                    selfRef.fixComputeItemScoreCustom(cycleEval, companyId, evalUser.getId());
                } else {
                    selfRef.fixSubmitScore(cycleEval, companyId, evalUser.getId());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }


    }


    //提交自定评分,可以有所有结点
    @Transactional
    public void submitCustomScore(CycleEval cycleEval, SubmitScoreCmd cmd) {
        TenantId companyId = cmd.getCompanyId();
        cycleEval = cycleEval == null ? taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId()) : cycleEval;
        EvalUser taskUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        CustomComputeDmSvc computeDmSvc = new CustomComputeDmSvc(cycleEval.isTypeWeightOpen(), cycleEval.getTemplItemJson(), taskUser);
        Boolean itemWeightOpened = cycleEval.isFullScoreRange();
        computeDmSvc.computeItemFinalScore(cmd.getItemScoreList(), itemWeightOpened);
        userRepo.batchUpdateScoreResult(computeDmSvc.getCurSubNodes());
        userRepo.batchSaveScoreResult(cmd.totalAsList());
        //记录日志
        Set<String> scoreTypes = cmd.getItemScoreList().stream().map(item -> item.getScorerType()).collect(Collectors.toSet());
        opLogDao.addSumitScoreLog(companyId, taskUser.getId(), cmd.getOpEmpId(), cmd.getLogScene(), JSON.toJSONString(scoreTypes), null);
        //添加评分总结
        userRepo.addEvalUserSummary(cmd.getScoreSummarys(), cmd.getOpEmpId());
        List<String> receiverIds = computeDmSvc.getCurSubNodes().stream().map(EvalScoreResult::getScorerId).collect(Collectors.toList());
        new CancelTodoEvent(companyId, receiverIds, taskUser.getId(), cmd.getScoreNode().todoScenes()).publish();
        if (computeDmSvc.allCustomScorerPassed()) {//自定直接结束
            selfRef.computeFinalResult(cycleEval, taskUser);
            //开启评分后可以继续录入完成值， 最后一个人提交评分，完成值还未提交，不能进入下一个阶段
            CompanyConf companyConf = companyDao.findCompanyConf(companyId);
            if (companyConf.canResSubmitInputFinish() && finishValueNotSubmit(taskUser)) {
                return;
            }
            new ThisStageEnded(cycleEval, taskUser, TalentStatus.SCORING).publish();
            return;
        }
        computeDmSvc.doneNodePassed();
        userRepo.finishedScoreNodeFlag(taskUser);
        /**如果该企业开启了评分环节，自动评分发送录入完成待办*/
        CompanyConf companyConf = companyDao.findCompanyConf(companyId);
        if (companyConf.openInputOnScoring()) {
            new SendTodoForInputOnScoreEvent(companyId, cmd.getTaskUserId(), cycleEval.getTaskName(), companyConf.isEnableResultInputSendMsg()).publish();
        }
    }

    //展开上级的下个流程
    @Transactional
    public void nextSuperSubNodes(CycleEval cycleEval, EvalUser taskUser, ScoreNodeFlow supNodeFlow) {
        //2 计算上级评的并行方式无需处理,只需看所有子流程结点完成,就总流程完成. taskBiz.5572
        //2 计算上级评的串行子流程,以下一个主结点展开子流程,发送消息及保存scoreReslut,标记audit为dispatched  [输入,1步的scoreReslute ]
        //taskBiz.5572
        if (cycleEval.superiorInSame()) {//上级评的并行方式无需处理
            return;
        }
        //主流程串行:当前结点是否完成
        if (!supNodeFlow.curNodeIsEnd()) {
            return;
        }
        EvalAudit nextMainNode = supNodeFlow.nextMainNode();
        //TODO
        List<EvalScoreResult> subNodes = nextMainNode.dispatchKpiItems(taskUser.normalItems(),
                Collections.singletonList(SubScoreNodeEnum.SUPERIOR_SCORE.getScene()));
        taskUser.acceptScorer(subNodes);
        userRepo.dispatch(nextMainNode);
        userRepo.batchSaveScoreResult(subNodes);
        //  发送通知与待办
        List<String> scorerIds = subNodes.stream().map(result -> result.getScorerId()).distinct().collect(Collectors.toList());
        new MsgTodoAggregate(cycleEval.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(), taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_SUP_SCORE, CompanyMsgActionEnum.SUPERIOR_SCORE)
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(scorerIds)
                .publish();
    }

    //计算总分与等级 taskBiz.5139-6692 setFinalScore ,1.0评分的处理
    @Transactional
    public void computeFinalResult(CycleEval cycleEval, EvalUser userEval) {
        //taskBiz.5139
        TenantId companyId = userEval.getCompanyId();
        String evalEmpId = userEval.getEmpId();
        FinalWeightSumScore weightSumScore = taskDao.sumFinalWeightScore(companyId, userEval.getId(),userEval.isOpenAvgWeightCompute());
        log.info("统计result得分：{}", JSONObject.toJSONString(weightSumScore));
        FinalPlusSubSumScore extratScore = taskDao.sumPlusAndSubScore(companyId, userEval.getId());
        if (CollectionUtils.isEmpty(userEval.getScoreRanges())) {
            ScoreRule rule = gradeDao.getEmpScoreRule(companyId, evalEmpId, userEval.getOrgId());
            userEval.scoreRanges(rule.getRanges());
        }
        BigDecimal finalScore = weightSumScore.addToSum(userEval.getFinalItemAutoScore()).addToSum(extratScore.sum()).sum();
        //BigDecimal finalScore = weightSumScore.sum().add(userEval.getFinalItemAutoScore()).add(extratScore.sum());
        log.info("最后得分={}", finalScore);
        if (cycleEval.isBaseScoreOpen()) {//基准分
            log.info("基准分={}", cycleEval.baseScore());
            finalScore = weightSumScore.addToSum(cycleEval.baseScore()).getSum();
        }
        BigDecimal systemFullScore = companyDao.findFullScoreValue(cycleEval.getCompanyId());
        if (cycleEval.canNotExceedFullScore()) {
            if (compareFullScore(cycleEval.baseScore(), finalScore)) {//基准分
                log.info("基准分大于最后得分={},取基准分", finalScore);
                finalScore = cycleEval.baseScore();
            }
            if (cycleEval.getCustomFullScore() == null) {
                if (compareFullScore(finalScore, systemFullScore)) {
                    finalScore = systemFullScore;
                }
            } else {
                if (compareFullScore(finalScore, cycleEval.getCustomFullScore())) {
                    finalScore = cycleEval.getCustomFullScore();
                }
            }
        }
        userEval.computeFinalScoreAndLevel(weightSumScore, finalScore, extratScore);
        userRepo.updateTaskUser(userEval);
    }

    private boolean compareFullScore(BigDecimal finalScore, BigDecimal fullScore) {
        return finalScore.compareTo(fullScore) == 1;
    }

    //360 简易分发串行任务控制器
    public void nextScoreNode(CycleEval cycleEval, EvalUser taskUser, ScoringNodeEnum curNode) {
        ScoringStageChain.StageNode stageNode = cycleEval.nextNodeOneByOne(curNode);
        if (stageNode == null) {
            return;
        }
        if (stageNode.getStatus() == ScoringNodeEnum.SCORING_SELF) {
            new SelfNodeStart(cycleEval, taskUser).publish();
            return;
        }
        if (stageNode.getStatus() == ScoringNodeEnum.SCORING_PEER) {
            new PeerNodeStart(cycleEval, taskUser).publish();
            return;
        }
        //if (stageNode.getStatus() == ScoringNodeEnum.SCORING_APPOINT) {
        //    new ItemNodeStart(cycleEval, taskUser).publish();
        //    return;
        //}
        if (stageNode.getStatus() == ScoringNodeEnum.SCORING_SUP) {
            new SuperiorNodeStart(cycleEval, taskUser).publish();
            return;
        }
    }

    //自定流程分发自定评分任务 taskBiz.3442 自定流程 并行发送通知所有结点 自动打分,定向,自评,上级 ,上级串行第一个,上级并行所有
    @Transactional
    public void dispatchCustomRaters(CycleEval cycleEval, EvalUser userEval) {
        List<EvalScoreResult> raterItems;
        List<String> distinctScoreIds;
        userEval.dispatchLazyLoad();
        //if (userEval.wasTempTask()) {//旧的模板任务
        if (userEval.customRaterDispatched()) {
            log.info("自定评分流程,只分发一次taskUserId:{}", userEval.getId());
            return;
        }
        CustomDispatchDmSvc dispatchDmSvc = new CustomDispatchDmSvc(userEval);
        raterItems = dispatchDmSvc.dispatchCustomRaterItems();
        userRepo.batchUpdate(userEval.getAllCustomAudits());
        userRepo.finishedScoreNodeFlag(userEval);

        if (CollUtil.isEmpty(raterItems)) {//配置打开了自评,实际上没有要评分指标
            userEval.finishCustomScore();
            new ScoreNodeEnd(cycleEval, userEval, ScoringNodeEnum.SCORING_AUTO).publish();
            //new ThisStageEnded(cycleEval, userEval, TalentStatus.SCORING).publish();
            return;
        }
        userRepo.batchSaveScoreResult(raterItems);
        // TODO发送工作通知,taskBiz.3702通知逻辑改了. @志林确认新的场景
        new MsgTodoAggregate(cycleEval.getCompanyId()
                , cycleEval.getId(), cycleEval.getTaskName(), userEval.getEmpId(), userEval.getId())
                .useScene(MsgSceneEnum.TASK_ALL_SCORE, CompanyMsgActionEnum.CUSTOM_SCORE)
                .sendExtTodo().sendExtMsg().addRecEmpId(dispatchDmSvc.distinctScoreIds()).addCenterMsg().publish();
    }

    @Transactional
    public void markScoreNodeFinish(EvalUser userEval, ScoringNodeEnum scoringNode) {
        if (scoringNode.isSelfScoreNode()) {
            userEval.finishSelfScore();
            new CancelTodoEvent(userEval.getCompanyId(), userEval.getId(), MsgSceneEnum.TASK_SELF_SCORE.getType()).publish();
            log.info("自评环节完成 taskUserId:{}", userEval.getId());
        }
        if (scoringNode.isPeerScoreNode()) {
            userEval.finishManualScore();
            new CancelTodoEvent(userEval.getCompanyId(), userEval.getId(), MsgSceneEnum.TASK_PEER_SCORE.getType()).publish();
            //new CancelTodoEvent(userEval.getCompanyId(), userEval.getId(), MsgSceneEnum.TASK_SUP_SCORE.getType()).publish();
            log.info("互评分环节完成 taskUserId:{}", userEval.getId());
        }
        if (scoringNode.isSuperScoreNode()) {
            userEval.finishSuperScore();
            log.info("上级评分环节完成 taskUserId:{}", userEval.getId());
            //清除一下签人员的待办
            new CancelTodoEvent(userEval.getCompanyId(), userEval.getId(), MsgSceneEnum.TASK_SUP_SCORE.getType()).publish();
        }
        if (scoringNode.isItemScoreNode()) {
            userEval.finishItemScore();
            log.info("定向评分环节完成 taskUserId:{}", userEval.getId());
            new CancelTodoEvent(userEval.getCompanyId(), userEval.getId(), MsgSceneEnum.TASK_APPOINT_SCORE.getType()).publish();
        }
        log.info("环节结束");
        userRepo.updateTaskUser(userEval);
    }

    public boolean nextPeerSubNodes(CycleEval cycleEval, EvalUser taskUser, ScoreNodeFlow supNodeFlow) {
        //下级评分之后进行同级评 -- 只有这种情况有评分人的先后顺序
        List<EvalAudit> peerScorers = taskUser.getPeerScorers();
        if (CollUtil.isEmpty(peerScorers)) {
            return false;
        }
        if (!cycleEval.usedPeerAfterSub()) {//上级评的并行方式无需处理
            return false;
        }
        EvalAudit nextMainNode = supNodeFlow.nextMainNode();
        List<EvalScoreResult> subNodes = nextMainNode.dispatchKpiItems(taskUser.normalItems());
        taskUser.acceptScorer(subNodes);
        userRepo.batchSaveScoreResult(subNodes);
        userRepo.dispatch(nextMainNode);
        //  发送通知与待办
        List<String> scorerIds = subNodes.stream().map(EvalScoreResult::getScorerId).distinct().collect(Collectors.toList());
        new MsgTodoAggregate(cycleEval.getCompanyId(), cycleEval.getId(), cycleEval.getTaskName(),
                taskUser.getEmpId(), taskUser.getId())
                .useScene(MsgSceneEnum.TASK_PEER_SCORE, CompanyMsgActionEnum.PEER_SCORE)
                .sendExtTodo().sendExtMsg().addCenterMsg().addRecEmpId(scorerIds).publish();
        return true;
    }

    //自定模板评分完成
    @Transactional
    public void handleCustomScoreNodeEnd(ScoreNodeEnd event) {
        event.startCustom();
        EvalUser userEval = event.getTaskUser();
        CycleEval cycleEval = event.getCycleEval();
        if (event.getScoreNode() != ScoringNodeEnum.SCORING_AUTO) {
            selfRef.markScoreNodeFinish(userEval, event.getScoreNode());
        }
        //未配置别的评分环节结束[仅有自动计算]
        if (!userEval.confedScoreNodes()) {
            selfRef.computeFinalResult(cycleEval, userEval);
            new ThisStageEnded(cycleEval, userEval, TalentStatus.SCORING).publish();
            return;
        }
        if (userEval.customAllRaterSubmited()) {//  实测验证一下可行性 实现 allScorerSubmited 旧:每次计算一次总分
            selfRef.computeFinalResult(cycleEval, userEval);
            new ThisStageEnded(cycleEval, userEval, TalentStatus.SCORING).publish();
            return;
        }
        //定向评分结束不需要做分发处理
        if (event.getScoreNode() == ScoringNodeEnum.SCORING_AUTO) {
            selfRef.dispatchCustomRaters(cycleEval, userEval);
        }
        return;
    }

    //360简易自定的一级事件
    @Transactional
    public void handleScoreNodeEnd(ScoreNodeEnd event) {
        event.startProcess();
        EvalUser userEval = event.getTaskUser();
        CycleEval cycleEval = event.getCycleEval();
        //标记已完成评分
        selfRef.markScoreNodeFinish(userEval, event.getScoreNode());
        if (userEval.allScorerSubmited()) {//  实测验证一下可行性 实现 allScorerSubmited 旧:每次计算一次总分
            selfRef.computeFinalResult(cycleEval, userEval);

            //开启评分后可以继续录入完成值， 最后一个人提交评分，完成值还未提交，不能进入下一个阶段
            CompanyConf companyConf = companyDao.findCompanyConf(userEval.getCompanyId());
            if (companyConf.canResSubmitInputFinish() && finishValueNotSubmit(userEval)) {
                return;
            }

            new ThisStageEnded(cycleEval, userEval, TalentStatus.SCORING).publish();
            return;
        }
        if (event.getScoreNode() == ScoringNodeEnum.SCORING_APPOINT) {
            return;
        }
        selfRef.nextScoreNode(cycleEval, userEval, event.getScoreNode());
        //  解析展开所有评分人信息并更新到scorer表,后续就可以不再解析了 .refact优化
    }

    private boolean finishValueNotSubmit(EvalUser userEval) {
        Boolean exist = itemDao.existNoInputValue(userEval.getTaskId(), userEval.getEmpId());
        if (exist) {
            log.info("自动计算指标还未全部提交，还是评分中");
            return true;
        }
        return false;
    }

    //360简易:单个评分环节下的子结点完成(单人评分完成) 子流程控制器
    @Transactional
    public void handleRaterScoreNodeEnd(RaterScoreNodeEnd event) {
        event.startProcess();
        EvalUser userEval = event.getUserEval();
        CycleEval cycleEval = event.getCycleEval();
        ScoringNodeEnum scoringNodeEnum = event.getScoringNodeEnum();
        /**如果该企业开启了评分环节，自动评分发送录入完成待办*/
        CompanyConf companyConf = companyDao.findCompanyConf(userEval.getCompanyId());
        if (companyConf.openInputOnScoring()) {
            new SendTodoForInputOnScoreEvent(userEval.getCompanyId(), userEval.getId(), cycleEval.getTaskName(), companyConf.isEnableResultInputSendMsg()).publish();
        }
        if (scoringNodeEnum.isSelfScoreNode()) {
            new ScoreNodeEnd(cycleEval, userEval, scoringNodeEnum).publish();
            return;
        }
        if (scoringNodeEnum.isSuperScoreNode()) {
            ScoreNodeFlow supNodeFlow = event.getSupNodeFlow();
            if (!supNodeFlow.mainFlowShouldEnd()) {//上级评分没有全部完成
                new CancelTodoEvent(userEval.getCompanyId(), supNodeFlow.curSubNodeRaterIds(),
                        userEval.getId(), SubScoreNodeEnum.SUPERIOR_SCORE.todoScenes()).publish();
                selfRef.nextSuperSubNodes(cycleEval, event.getUserEval(), event.getSupNodeFlow());
                return;
            }
            new ScoreNodeEnd(cycleEval, userEval, scoringNodeEnum).publish();
        }
        if (scoringNodeEnum.isItemScoreNode()) {
            ScoreNodeFlow itemFlow = event.getSupNodeFlow();
            if (!itemFlow.itemSubNodePassed()) {//定向评分没有全部完成
                return;
            }
            new ScoreNodeEnd(cycleEval, userEval, scoringNodeEnum).publish();
        }

        if (scoringNodeEnum.isPeerScoreNode()) {
            ScoreNodeFlow supNodeFlow = event.getSupNodeFlow();
            if (!supNodeFlow.mainFlowShouldEnd()) {//上级评分没有全部完成
                selfRef.nextPeerSubNodes(cycleEval, event.getUserEval(), event.getSupNodeFlow());
                return;
            }
            new ScoreNodeEnd(cycleEval, userEval, scoringNodeEnum).publish();
        }
    }


    @Transactional
    public void fixSubmitScore(CycleEval cycleEval, TenantId companyId, String taskUserId) {
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        EmpEvalMerge evalMerge = ruleRepo.getEmpEvalMerge(companyId, taskUser.getId(), EmpEvalMerge.all);
        List<EvalScoreResult> passedResults = taskDao.listPassedEvalScoreResults(companyId, taskUserId);
        BigDecimal fullScore = cycleEval.getCustomFullScore() == null ? companyDao.findFullScoreValue(cycleEval.getCompanyId()) : cycleEval.getCustomFullScore();
        taskUser.setScoreResults(passedResults);
        //if ("true".equals(taskUser.getResultAuditFlag())) {
        //    return;
        //}
        boolean computed = taskUser.tryComputeAutoScore(cycleEval.isTypeWeightOpen(), fullScore, evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.openItemAutoScoreMultiplWeight());
        if (computed) {
            userRepo.updateAutoScore(taskUser);
        }
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.SELF_SCORE);
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.ITEM_SCORE);
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.PEER_SCORE);
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.SUB_SCORE);
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.SUPERIOR_SCORE);
        fixComputeItemScore(cycleEval, taskUser, passedResults, SubScoreNodeEnum.APPOINT_SCORE);
        selfRef.computeFinalResult(cycleEval, taskUser);

    }

    private void fixComputeItemScore(CycleEval cycleEval, EvalUser taskUser,
                                     List<EvalScoreResult> submited,
                                     SubScoreNodeEnum scoreNodeEnum) {
        List<EvalScoreResult> scoreTypes = submited.stream().filter(esrs -> esrs.getScorerType().equals(scoreNodeEnum.getScene()))
                .collect(Collectors.toList());
        ScoreNodeFlow supNodeFlow = userRepo.getScoreNodeFlow(cycleEval.getCompanyId(), taskUser.getId(), scoreNodeEnum, true);
        supNodeFlow.setCheckSubmited(false);
        if (CollUtil.isEmpty(scoreTypes)) {
            return;
        }
        String scorerId = "10001";
        supNodeFlow.submitScoreResults(new EmpId(scorerId), new EvalScoreResult(), scoreTypes);
        SimpleComputeDmSvc scoreDmSvc = new SimpleComputeDmSvc(cycleEval, taskUser, scoreNodeEnum);
        if (!cycleEval.totalScore(scoreNodeEnum)) {
            scoreDmSvc.computeItemFinalScore(scoreTypes, new EvalScoreResult());
            userRepo.batchUpdateScoreResult(scoreTypes);
            return;
        }
        log.info("打总分");
        List<EvalScoreResult> noPlusOrSubItemResult = scoreTypes.stream().filter(score -> score.notPlusItemOrSubItem()).collect(Collectors.toList());
        if (CollUtil.isEmpty(noPlusOrSubItemResult)) {
            log.info("全是加减分指标");
            return;
        }
        Map<String, List<EvalScoreResult>> scorers =
                noPlusOrSubItemResult.stream().collect(Collectors.groupingBy(scorer -> scorer.getScorerId()));
        scorers.keySet().forEach(scorer -> {
            scoreDmSvc.computeItemFinalScore(scorers.get(scorer), new EvalScoreResult(scorers.get(scorer)));
            userRepo.batchUpdateScoreResult(scorers.get(scorer));
        });
    }

    //提交自定评分,可以有所有结点
    @Transactional
    public void fixComputeItemScoreCustom(CycleEval cycleEval, TenantId companyId, String taskUserId) {
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        EmpEvalMerge evalMerge = ruleRepo.getEmpEvalMerge(companyId, taskUser.getId(), EmpEvalMerge.all);
        List<EvalScoreResult> passedResults = taskDao.listPassedEvalScoreResults(companyId, taskUserId);
        BigDecimal fullScore = cycleEval.getCustomFullScore() == null ? companyDao.findFullScoreValue(cycleEval.getCompanyId()) : cycleEval.getCustomFullScore();
        taskUser.setScoreResults(passedResults);
        boolean computed = taskUser.tryComputeAutoScore(cycleEval.isTypeWeightOpen(), fullScore, evalMerge.getScoreValueConf().isFullScoreRange(), evalMerge.openItemAutoScoreMultiplWeight());
        if (computed) {
            userRepo.updateAutoScore(taskUser);
        }
        List<EvalScoreResult> ress = taskUser.getScoreResults();
        CustomComputeDmSvc computeDmSvc = new CustomComputeDmSvc(cycleEval.isTypeWeightOpen(), cycleEval.getTemplItemJson() == null ? new ArrayList<>() : cycleEval.getTemplItemJson(), taskUser);
        //无人员评分直接计算最后得分
        if (CollUtil.isEmpty(ress)) {
            selfRef.computeFinalResult(cycleEval, taskUser);
            return;
        }
        //有需要人为评分的指标才计算
        Boolean itemWeightOpened = cycleEval.isFullScoreRange();
        computeDmSvc.computeItemFinalScore(ress, itemWeightOpened);
        userRepo.batchUpdateScoreResult(computeDmSvc.getCurSubNodes());
        if (computeDmSvc.allCustomScorerPassed()) {//自定直接结束
            selfRef.computeFinalResult(cycleEval, taskUser);
        }

    }

    //查询多级上级评分
    public EvalMultistageSupScorePo listEvalScoreForSuper(TenantId companyId, String taskUserId) {
        return taskDao.queryMultistageSupScore(companyId, taskUserId);
    }

    //查询可以修改的打分记录 taskBiz[7339]
    public EnbledSaveScorePo listEnabledScoreRs(String companyId, String taskUserId, String scorerType, String opEmpId) {
        List<ScoreResultWithItemPo> withItems = taskDao.listPassedScoreResultWithItem(companyId, taskUserId, scorerType, opEmpId);
        EnbledSaveScorePo scorePo = new EnbledSaveScorePo(withItems);
        scorePo.filterTotal();
        //如果是上级评分和指定评分，需要展示自评分数
        if (scorePo.getNeedSelfScore()) {
            taskDao.loadSelfScore(companyId, taskUserId, scorePo.getItemScoreList());
        }
        return scorePo;
    }

    // add by xzl @ 2022/12/27
    @Transactional
    public void endScoreStage(EndScoreStageCmd cmd) {
        EvalUser user = userRepo.getCustomTaskUser(cmd.getTenantId(), cmd.getCurUserId());
        CycleEval cycleEval = taskRepo.getCycleEval(cmd.getTenantId(), new TaskId(user.getTaskId()));
        Boolean onlyAppointScore = taskUserDao.onlyAppointScore(cmd.getTenantId(), user.getTaskId(), user.getEmpId());
        //能否结束评分
        user.checkEndScore(cycleEval.isCustom(), onlyAppointScore);
        //结束评分逻辑
        user.endAppointScore();
        //重新计算一下得分
        user.setScoreResults(user.getItemResults());
        CustomComputeDmSvc computeDmSvc = new CustomComputeDmSvc(cycleEval.isTypeWeightOpen(), cycleEval.getTemplItemJson(), user);
        computeDmSvc.computeItemFinalScore(user.getScoreResults(), cycleEval.isFullScoreRange());
        selfRef.computeFinalResult(cycleEval, user);
        userRepo.batchUpdateScoreResult(user.getScoreResults());
        //记录日志
        String desc = CollUtil.join(Arrays.asList(EvaluateAuditSceneEnum.APPOINT_SCORE.getScene()), ",");
        opLogDao.addSumitScoreLog(user.getCompanyId(), user.getId(), cmd.getOpEmpId(), OperationLogSceneEnum.MANUAL_END_SCORE.getScene(), desc, null);
        //取消指标评分的代办
        new CancelTodoEvent(cmd.getTenantId(), user.getAppointRaters(), user.getId(), MsgSceneEnum.TASK_ALL_SCORE.getType()).publish();
        //指定评分结束
        new ScoreNodeEnd(cycleEval, user, ScoringNodeEnum.SCORING_APPOINT).publish();
    }


//    public void replaceScoreRule(String companyId, String taskUserIds, String taskId, String ruleId) {
//        List<EvalUser> users = taskUserDao.listEvalUser(companyId, taskId, taskUserIds);
//        for (EvalUser evalUser : users) {
//            updateEvalUser(companyId, evalUser, ruleId);
//        }
//    }

//    @Transactional
//    public void updateEvalUser(String companyId, EvalUser evalUser, String ruleId) {
//        ScoreRule empScoreRule = gradeDao.getEmpScoreRuleFix(new TenantId(companyId), evalUser.getEmpId(), ruleId);
//        evalUser.scoreRanges(empScoreRule.getRanges());
//        if (null != evalUser.getFinalScore() && !TalentStatus.statusOf(evalUser.getTaskStatus()).before(TalentStatus.RESULTS_AUDITING)) {
//            evalUser.replaceScoreRule(empScoreRule);
//        }
//        userRepo.updateTaskUser(evalUser);
//    }


    public void submitAllV1(TenantId companyId, List<SubmitScoreCmd> cmds) {
        SubmitScoreCmd cmd = cmds.get(0);
        if (cmd.isReCommit()) {
            CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId());
            if (!cycleEval.isCustom()) {
                selfRef.reSubmitScenesScore(cycleEval, cmds);
            }
            if (cycleEval.isCustom()) {
                SubmitScoreCmd customCmd = merge2Custom(cmds);
                selfRef.reSubmitCustomScore(cycleEval, customCmd);
            }
            return;
        }

        CycleEval cycleEval = taskRepo.getMergeCycleEval(companyId, cmd.getTaskUserId());
        if (!cycleEval.isCustom()) {//旧数据s3串行
            selfRef.submitScenesScore(cycleEval, cmds);
        } else if (cycleEval.isCustom()) {//旧数据cutom并行
            SubmitScoreCmd customCmd = merge2Custom(cmds);
            selfRef.submitCustomScore(cycleEval, customCmd);
        }
    }

    @Async
    public void batchSubmitScenesScoreV1(BatchSubmitScoreCmd batchCmd) {
        batchCmd.trace();
        for (BatchSubmitScoreCmd.CmdWrap nodeScore : batchCmd.getScoreCmdWraps()) {
            this.submitAllV1(new TenantId(batchCmd.getCompanyId()), nodeScore.getNodeScores());
        }
    }
}
