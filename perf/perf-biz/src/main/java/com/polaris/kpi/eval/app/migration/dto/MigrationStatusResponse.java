package com.polaris.kpi.eval.app.migration.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.time.Duration;

/**
 * 数据迁移状态响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationStatusResponse {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 迁移状态
     */
    private String status;

    /**
     * 进度百分比
     */
    private Double progressPercentage;

    /**
     * 已处理记录数
     */
    private Long processedRecords;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 成功处理数量
     */
    private Long successCount;

    /**
     * 失败处理数量
     */
    private Long failureCount;

    /**
     * 跳过处理数量
     */
    private Long skipCount;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 当前处理位置
     */
    private String currentPosition;

    /**
     * 统计信息
     */
    private String statistics;

    /**
     * 是否找到记录
     */
    private Boolean found;

    /**
     * 预估剩余时间（分钟）
     */
    private Long estimatedRemainingMinutes;

    /**
     * 平均处理速度（记录/秒）
     */
    private Double averageProcessingSpeed;

    /**
     * 已运行时间（分钟）
     */
    private Long elapsedMinutes;

    /**
     * 创建未找到的响应
     * 
     * @param sessionId 会话ID
     * @return 响应对象
     */
    public static MigrationStatusResponse notFound(String sessionId) {
        return MigrationStatusResponse.builder()
                .sessionId(sessionId)
                .found(false)
                .status("NOT_FOUND")
                .progressPercentage(0.0)
                .processedRecords(0L)
                .totalRecords(0L)
                .successCount(0L)
                .failureCount(0L)
                .skipCount(0L)
                .build();
    }

    /**
     * 创建成功响应
     * 
     * @return 当前对象（用于链式调用）
     */
    public MigrationStatusResponse success() {
        this.found = true;
        return this;
    }

    /**
     * 计算并设置派生字段
     * 
     * @return 当前对象（用于链式调用）
     */
    public MigrationStatusResponse calculateDerivedFields() {
        // 计算已运行时间
        if (startTime != null) {
            LocalDateTime endTime = completeTime != null ? completeTime : LocalDateTime.now();
            Duration elapsed = Duration.between(startTime, endTime);
            this.elapsedMinutes = elapsed.toMinutes();
        }

        // 计算平均处理速度
        if (elapsedMinutes != null && elapsedMinutes > 0 && processedRecords != null && processedRecords > 0) {
            double elapsedSeconds = elapsedMinutes * 60.0;
            this.averageProcessingSpeed = processedRecords / elapsedSeconds;
        }

        // 计算预估剩余时间
        if (averageProcessingSpeed != null && averageProcessingSpeed > 0 
                && totalRecords != null && processedRecords != null 
                && processedRecords < totalRecords) {
            long remainingRecords = totalRecords - processedRecords;
            double remainingSeconds = remainingRecords / averageProcessingSpeed;
            this.estimatedRemainingMinutes = (long) Math.ceil(remainingSeconds / 60.0);
        }

        return this;
    }

    /**
     * 获取成功率
     * 
     * @return 成功率百分比
     */
    public Double getSuccessRate() {
        if (processedRecords == null || processedRecords == 0) {
            return 0.0;
        }
        if (successCount == null) {
            return 0.0;
        }
        return (double) successCount / processedRecords * 100.0;
    }

    /**
     * 获取失败率
     * 
     * @return 失败率百分比
     */
    public Double getFailureRate() {
        if (processedRecords == null || processedRecords == 0) {
            return 0.0;
        }
        if (failureCount == null) {
            return 0.0;
        }
        return (double) failureCount / processedRecords * 100.0;
    }

    /**
     * 获取跳过率
     * 
     * @return 跳过率百分比
     */
    public Double getSkipRate() {
        if (processedRecords == null || processedRecords == 0) {
            return 0.0;
        }
        if (skipCount == null) {
            return 0.0;
        }
        return (double) skipCount / processedRecords * 100.0;
    }

    /**
     * 检查是否正在运行
     * 
     * @return 是否正在运行
     */
    public Boolean isRunning() {
        return "RUNNING".equals(status);
    }

    /**
     * 检查是否已完成
     * 
     * @return 是否已完成
     */
    public Boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    /**
     * 检查是否失败
     * 
     * @return 是否失败
     */
    public Boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查是否暂停
     * 
     * @return 是否暂停
     */
    public Boolean isPaused() {
        return "PAUSED".equals(status);
    }

    /**
     * 检查是否可以恢复
     * 
     * @return 是否可以恢复
     */
    public Boolean canResume() {
        return isPaused() || isFailed();
    }

    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case "PENDING":
                return "待开始";
            case "RUNNING":
                return "运行中";
            case "PAUSED":
                return "已暂停";
            case "COMPLETED":
                return "已完成";
            case "FAILED":
                return "失败";
            case "CANCELLED":
                return "已取消";
            case "NOT_FOUND":
                return "未找到";
            default:
                return status;
        }
    }

    /**
     * 获取详细的进度信息
     * 
     * @return 进度信息
     */
    public String getDetailedProgress() {
        StringBuilder sb = new StringBuilder();
        
        sb.append(String.format("状态: %s", getStatusDescription()));
        
        if (progressPercentage != null) {
            sb.append(String.format(", 进度: %.2f%%", progressPercentage));
        }
        
        if (processedRecords != null && totalRecords != null) {
            sb.append(String.format(", 记录: %d/%d", processedRecords, totalRecords));
        }
        
        if (successCount != null) {
            sb.append(String.format(", 成功: %d", successCount));
        }
        
        if (failureCount != null && failureCount > 0) {
            sb.append(String.format(", 失败: %d", failureCount));
        }
        
        if (skipCount != null && skipCount > 0) {
            sb.append(String.format(", 跳过: %d", skipCount));
        }
        
        if (averageProcessingSpeed != null) {
            sb.append(String.format(", 速度: %.2f记录/秒", averageProcessingSpeed));
        }
        
        if (estimatedRemainingMinutes != null && estimatedRemainingMinutes > 0) {
            sb.append(String.format(", 预估剩余: %d分钟", estimatedRemainingMinutes));
        }
        
        return sb.toString();
    }

    /**
     * 获取简要摘要
     * 
     * @return 简要摘要
     */
    public String getSummary() {
        if (!Boolean.TRUE.equals(found)) {
            return String.format("会话 %s 未找到", sessionId);
        }
        
        return String.format("会话 %s: %s, 进度 %.2f%%, 处理 %d/%d 条记录",
                sessionId, getStatusDescription(), 
                progressPercentage != null ? progressPercentage : 0.0,
                processedRecords != null ? processedRecords : 0,
                totalRecords != null ? totalRecords : 0);
    }
}
