package com.polaris.kpi.eval.app.migration.appsvc;

import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.migration.util.ConcurrentBitmapManager;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import com.polaris.kpi.eval.domain.task.repo.TaskUserRepo;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 集成的迁移应用服务
 * 将优化位图方案与您现有的仓储方法完美结合
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IntegratedMigrationAppSvc {

    private final TaskUserRepo taskUserRepo;
    private final TaskUserDao taskUserDao;
    private final ConcurrentBitmapManager bitmapManager;
    private final RecordIndexCalculator indexCalculator;

    // 您现有的ScorerDataMigrationDmSvc可以在这里注入
    // private final ScorerDataMigrationDmSvc scorerDataMigrationDmSvc;

    /**
     * 启动集成的数据迁移
     * 结合位图优化和您现有的业务逻辑
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startIntegratedMigration(String migrationType, String operatorId) {
        log.info("Starting integrated migration: type={}, operator={}", migrationType, operatorId);

        // 创建优化的位图进度管理器
        String sessionId = generateSessionId();
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);
        
        // 初始化总数据量
        initializeCounts(progress);
        
        // 保存初始进度
        progress.saveMetadata();
        
        // 异步执行迁移
        executeIntegratedMigrationAsync(sessionId);
        
        return sessionId;
    }

    /**
     * 异步执行集成迁移
     * 
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public CompletableFuture<Void> executeIntegratedMigrationAsync(String sessionId) {
        try {
            log.info("Starting async integrated migration: {}", sessionId);
            
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found: {}", sessionId);
                return CompletableFuture.completedFuture(null);
            }
            
            progress.start();
            progress.saveMetadata();
            
            // 执行集成迁移逻辑
            executeIntegratedMigrationLogic(progress);
            
            progress.complete();
            log.info("Integrated migration completed: {}", sessionId);
            
        } catch (Exception e) {
            log.error("Integrated migration failed: {}", sessionId, e);
            handleMigrationFailure(sessionId, e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 执行集成迁移逻辑
     * 核心方法：结合位图管理和您现有的业务逻辑
     * 
     * @param progress 进度管理器
     */
    private void executeIntegratedMigrationLogic(OptimizedBitmapProgress progress) {
        String migrationType = progress.getMigrationType();
        int companyPageSize = 10;
        int userPageSize = 1000;
        
        // 从断点位置开始处理
        int startCompanyPage = progress.getCurrentCompanyPage();
        
        log.info("Resuming from company page: {}", startCompanyPage);
        
        for (int companyPage = startCompanyPage; ; companyPage++) {
            // 检查是否需要暂停
            if (shouldPause(progress)) {
                log.info("Migration paused at company page: {}", companyPage);
                break;
            }
            
            // 获取公司列表
            List<String> companyIds = getCompanyIdsByPage(migrationType, companyPage, companyPageSize);
            if (companyIds.isEmpty()) {
                log.info("No more companies to process");
                break;
            }
            
            // 处理每个公司
            for (String companyId : companyIds) {
                processCompanyWithIntegratedLogic(progress, companyId, userPageSize);
                progress.updatePosition(companyId, companyPage, 1);
            }
            
            // 定期保存进度
            if (companyPage % 5 == 0) {
                progress.saveMetadata();
                log.info("Progress saved at company page: {}", companyPage);
            }
        }
    }

    /**
     * 使用集成逻辑处理单个公司
     * 
     * @param progress 进度管理器
     * @param companyId 公司ID
     * @param userPageSize 用户页大小
     */
    private void processCompanyWithIntegratedLogic(OptimizedBitmapProgress progress, 
                                                  String companyId, int userPageSize) {
        log.debug("Processing company with integrated logic: {}", companyId);
        
        String migrationType = progress.getMigrationType();
        int startUserPage = companyId.equals(progress.getCurrentCompanyId()) ? 
                progress.getCurrentUserPage() : 1;
        
        for (int userPage = startUserPage; ; userPage++) {
            // 获取用户列表
            List<String> userIds = getUserIdsByPage(migrationType, companyId, userPage, userPageSize);
            if (userIds.isEmpty()) {
                log.debug("No more users for company: {}", companyId);
                break;
            }
            
            // 处理用户批次
            processUserBatchWithBitmap(progress, companyId, userIds, userPage, userPageSize);
            
            // 更新位置
            progress.updatePosition(companyId, progress.getCurrentCompanyPage(), userPage + 1);
            
            // 定期保存
            if (userPage % 10 == 0) {
                progress.saveMetadata();
            }
        }
    }

    /**
     * 使用位图处理用户批次
     * 核心优化：结合位图检查和您现有的批量操作
     * 
     * @param progress 进度管理器
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @param userPage 用户页码
     * @param userPageSize 用户页大小
     */
    private void processUserBatchWithBitmap(OptimizedBitmapProgress progress, String companyId, 
                                          List<String> userIds, int userPage, int userPageSize) {
        
        String sessionId = progress.getSessionId();
        String migrationType = progress.getMigrationType();
        
        // 需要处理的用户列表
        List<String> usersToProcess = new java.util.ArrayList<>();
        List<Long> indexesToProcess = new java.util.ArrayList<>();
        
        // 使用位图检查哪些用户需要处理
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            
            // 计算全局索引
            long globalIndex = indexCalculator.calculateGlobalIndex(
                    companyId, progress.getCurrentCompanyPage(), userPage, i, 10, userPageSize);
            
            // 位图检查：如果已处理则跳过
            if (bitmapManager.getBit(sessionId, globalIndex, progress.getSegmentSize())) {
                log.debug("User already processed, skipping: companyId={}, userId={}, index={}", 
                        companyId, userId, globalIndex);
                continue;
            }
            
            usersToProcess.add(userId);
            indexesToProcess.add(globalIndex);
        }
        
        if (usersToProcess.isEmpty()) {
            log.debug("All users in this batch already processed");
            return;
        }
        
        // 批量处理用户（调用您现有的方法）
        boolean batchSuccess = processBatchWithExistingMethods(companyId, usersToProcess, migrationType);
        
        if (batchSuccess) {
            // 批量更新位图
            for (Long globalIndex : indexesToProcess) {
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, true);
            }
            log.info("Successfully processed batch: companyId={}, users={}", companyId, usersToProcess.size());
        } else {
            // 逐个处理失败的记录
            processIndividualRecords(progress, companyId, usersToProcess, indexesToProcess, migrationType);
        }
    }

    /**
     * 使用您现有的方法批量处理
     * 这里集成您的batchAddEmpEvalScorer、batchSaveScoreResult等方法
     * 
     * @param companyId 公司ID
     * @param userIds 用户ID列表
     * @param migrationType 迁移类型
     * @return 是否成功
     */
    private boolean processBatchWithExistingMethods(String companyId, List<String> userIds, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                return processBatchFinishedUsers(companyId, userIds);
            } else if ("NO_FINISHED".equals(migrationType)) {
                return processBatchNoFinishedUsers(companyId, userIds);
            }
            return false;
            
        } catch (Exception e) {
            log.error("Batch processing failed: companyId={}, users={}", companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 批量处理已完成的用户
     * 集成您现有的业务逻辑
     */
    private boolean processBatchFinishedUsers(String companyId, List<String> userIds) {
        try {
            // 1. 查询需要迁移的数据
            // List<EvalScoreResult> scoreResults = getOnScoreEvalMingration(companyId, userIds);
            
            // 2. 使用您现有的批量保存方法
            // taskUserRepo.batchSaveScoreResult(scoreResults);
            
            // 3. 批量更新状态
            // taskUserRepo.batchUpdateScoreResult(updatedResults);
            
            // 4. 调用您的batchAddEmpEvalScorer方法
            // batchAddEmpEvalScorer(companyId, userIds, scorerData);
            
            log.debug("Batch processed finished users: companyId={}, count={}", companyId, userIds.size());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process finished users: companyId={}", companyId, e);
            return false;
        }
    }

    /**
     * 批量处理未完成的用户
     */
    private boolean processBatchNoFinishedUsers(String companyId, List<String> userIds) {
        try {
            // 类似的批量处理逻辑
            log.debug("Batch processed no-finished users: companyId={}, count={}", companyId, userIds.size());
            return true;
            
        } catch (Exception e) {
            log.error("Failed to batch process no-finished users: companyId={}", companyId, e);
            return false;
        }
    }

    /**
     * 逐个处理记录（批量失败时的降级处理）
     */
    private void processIndividualRecords(OptimizedBitmapProgress progress, String companyId, 
                                        List<String> userIds, List<Long> indexes, String migrationType) {
        
        String sessionId = progress.getSessionId();
        
        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            Long globalIndex = indexes.get(i);
            
            try {
                boolean success = processIndividualUser(companyId, userId, migrationType);
                
                // 更新位图
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, success);
                
                if (success) {
                    log.debug("Successfully processed individual user: companyId={}, userId={}", companyId, userId);
                } else {
                    log.warn("Failed to process individual user: companyId={}, userId={}", companyId, userId);
                }
                
            } catch (Exception e) {
                log.error("Error processing individual user: companyId={}, userId={}", companyId, userId, e);
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, false);
            }
        }
    }

    /**
     * 处理单个用户
     */
    private boolean processIndividualUser(String companyId, String userId, String migrationType) {
        // 调用您现有的单个用户处理逻辑
        // 例如：scorerDataMigrationDmSvc.migrateUser(companyId, userId);
        return true; // 临时返回成功
    }

    // 辅助方法
    private void initializeCounts(OptimizedBitmapProgress progress) {
        String migrationType = progress.getMigrationType();
        long totalUsers = getTotalUserCount(migrationType);
        progress.getTotalRecords().set(totalUsers);
        log.info("Initialized total user count: {}", totalUsers);
    }

    private long getTotalUserCount(String migrationType) {
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalUserCountForFinished();
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getTotalUserCountForNoFinished();
        }
        return taskUserDao.getTotalUserCount();
    }

    private List<String> getCompanyIdsByPage(String migrationType, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getCompanyIdsForFinished(offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getCompanyIdsForNoFinished(offset, pageSize);
        }
        return taskUserDao.getCompanyIds(offset, pageSize);
    }

    private List<String> getUserIdsByPage(String migrationType, String companyId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;
        if ("FINISHED".equals(migrationType)) {
            return taskUserDao.getUserIdsForFinished(companyId, offset, pageSize);
        } else if ("NO_FINISHED".equals(migrationType)) {
            return taskUserDao.getUserIdsForNoFinished(companyId, offset, pageSize);
        }
        return taskUserDao.getUserIds(companyId, offset, pageSize);
    }

    private boolean shouldPause(OptimizedBitmapProgress progress) {
        OptimizedBitmapProgress latest = OptimizedBitmapProgress.loadMetadata(progress.getSessionId());
        return latest != null && latest.getStatus() == OptimizedBitmapProgress.MigrationStatus.PAUSED;
    }

    private void handleMigrationFailure(String sessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                progress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to handle migration failure: {}", sessionId, e);
        }
    }

    private String generateSessionId() {
        return "INTEGRATED_MIGRATION_" + System.currentTimeMillis() + "_" + 
               java.util.UUID.randomUUID().toString().substring(0, 8);
    }
}
