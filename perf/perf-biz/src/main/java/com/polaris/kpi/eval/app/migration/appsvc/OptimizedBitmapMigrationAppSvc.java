package com.polaris.kpi.eval.app.migration.appsvc;

import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.migration.dmsvc.OptimizedBitmapMigrationDmSvc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 优化的位图迁移应用服务
 * 解决断点恢复和内存膨胀问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OptimizedBitmapMigrationAppSvc {

    private final OptimizedBitmapMigrationDmSvc optimizedBitmapMigrationDmSvc;

    /**
     * 启动优化的位图迁移任务
     * 
     * @param migrationType 迁移类型
     * @return 会话ID
     */
    public String startOptimizedMigration(String migrationType) {
        log.info("Starting optimized bitmap migration for type: {}", migrationType);

        // 生成会话ID
        String sessionId = generateSessionId();
        
        // 创建优化的位图进度管理器
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);
        
        // 保存初始元数据
        progress.saveMetadata();
        
        // 异步执行迁移
        executeOptimizedMigrationAsync(sessionId);
        
        return sessionId;
    }

    /**
     * 恢复中断的位图迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeOptimizedMigration(String sessionId) {
        log.info("Attempting to resume optimized migration: {}", sessionId);
        
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null) {
            log.error("Migration progress not found for session: {}", sessionId);
            return false;
        }
        
        if (!progress.canResume()) {
            log.warn("Migration cannot be resumed, current status: {}", progress.getStatus());
            return false;
        }
        
        // 恢复迁移
        progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        progress.saveMetadata();
        
        // 异步继续执行
        executeOptimizedMigrationAsync(sessionId);
        
        return true;
    }

    /**
     * 暂停位图迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseOptimizedMigration(String sessionId) {
        log.info("Pausing optimized migration: {}", sessionId);
        
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null || progress.getStatus() != OptimizedBitmapProgress.MigrationStatus.RUNNING) {
            return false;
        }
        
        progress.pause();
        
        return true;
    }

    /**
     * 获取位图迁移状态
     * 
     * @param sessionId 会话ID
     * @return 迁移状态
     */
    public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId) {
        return OptimizedBitmapProgress.loadMetadata(sessionId);
    }

    /**
     * 异步执行优化的迁移任务
     * 
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public CompletableFuture<Void> executeOptimizedMigrationAsync(String sessionId) {
        try {
            log.info("Starting async optimized migration execution for session: {}", sessionId);
            
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return CompletableFuture.completedFuture(null);
            }
            
            // 开始迁移
            if (progress.getStatus() == OptimizedBitmapProgress.MigrationStatus.PENDING) {
                progress.start();
                progress.saveMetadata();
            }
            
            // 执行优化的位图迁移
            optimizedBitmapMigrationDmSvc.executeBitmapMigration(progress);
            
            log.info("Optimized migration completed successfully for session: {}", sessionId);
            
        } catch (Exception e) {
            log.error("Optimized migration failed for session: {}", sessionId, e);
            handleMigrationFailure(sessionId, e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理迁移失败
     * 
     * @param sessionId 会话ID
     * @param errorMessage 错误信息
     */
    private void handleMigrationFailure(String sessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                progress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to update migration failure status for session: {}", sessionId, e);
        }
    }

    /**
     * 清理已完成的迁移文件
     * 
     * @param sessionId 会话ID
     * @return 是否成功清理
     */
    public boolean cleanupMigrationFiles(String sessionId) {
        log.info("Cleaning up migration files for session: {}", sessionId);
        
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.cleanup();
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("Failed to cleanup migration files for session: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取迁移统计信息
     * 
     * @param sessionId 会话ID
     * @return 统计信息
     */
    public String getMigrationStatistics(String sessionId) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        return progress != null ? progress.getStatistics() : "Migration not found";
    }

    /**
     * 生成会话ID
     * 
     * @return 会话ID
     */
    private String generateSessionId() {
        return "BITMAP_MIGRATION_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 检查迁移是否存在
     * 
     * @param sessionId 会话ID
     * @return 是否存在
     */
    public boolean migrationExists(String sessionId) {
        return OptimizedBitmapProgress.loadMetadata(sessionId) != null;
    }

    /**
     * 强制保存进度
     * 
     * @param sessionId 会话ID
     * @return 是否成功
     */
    public boolean forceSaveProgress(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.saveMetadata();
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("Failed to force save progress for session: {}", sessionId, e);
            return false;
        }
    }
}
