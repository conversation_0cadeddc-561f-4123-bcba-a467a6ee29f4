package com.polaris.kpi.eval.app.migration.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 数据迁移配置类
 * 配置异步执行器和相关参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Configuration
@EnableAsync
public class MigrationConfig {

    /**
     * 迁移任务执行器
     * 专门用于执行数据迁移任务的线程池
     * 
     * @return 线程池执行器
     */
    @Bean("migrationTaskExecutor")
    public Executor migrationTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：建议根据CPU核心数设置
        executor.setCorePoolSize(2);
        
        // 最大线程数：避免过多线程导致上下文切换开销
        executor.setMaxPoolSize(4);
        
        // 队列容量：限制队列大小避免内存溢出
        executor.setQueueCapacity(10);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Migration-");
        
        // 拒绝策略：调用者运行策略，避免任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }

    /**
     * 批处理执行器
     * 用于执行批量数据处理任务
     * 
     * @return 线程池执行器
     */
    @Bean("batchProcessExecutor")
    public Executor batchProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 批处理可以使用更多线程
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("BatchProcess-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setKeepAliveSeconds(30);
        executor.setAllowCoreThreadTimeOut(true);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }

    /**
     * 监控执行器
     * 用于执行监控和统计任务
     * 
     * @return 线程池执行器
     */
    @Bean("monitoringExecutor")
    public Executor monitoringExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 监控任务通常较轻量，使用较少线程
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(2);
        executor.setQueueCapacity(20);
        executor.setThreadNamePrefix("Monitoring-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        executor.setKeepAliveSeconds(120);
        executor.setAllowCoreThreadTimeOut(true);
        executor.setWaitForTasksToCompleteOnShutdown(false);
        executor.setAwaitTerminationSeconds(10);
        
        executor.initialize();
        return executor;
    }
}
