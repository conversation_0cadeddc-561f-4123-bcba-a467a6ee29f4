package com.polaris.kpi.eval.app.migration.config;

import com.polaris.kpi.eval.domain.migration.util.ConcurrentBitmapManager;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 优化迁移配置类
 * 配置位图管理和异步执行相关的Bean
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Configuration
@EnableAsync
@Slf4j
public class OptimizedMigrationConfig {

    /**
     * 并发位图管理器
     * 只有在启用位图优化时才创建
     */
    @Bean
    @ConditionalOnProperty(name = "scorer.migration.use.bitmap", havingValue = "true")
    public ConcurrentBitmapManager concurrentBitmapManager() {
        log.info("Creating ConcurrentBitmapManager for optimized migration");
        return new ConcurrentBitmapManager();
    }

    /**
     * 记录索引计算器
     * 只有在启用位图优化时才创建
     */
    @Bean
    @ConditionalOnProperty(name = "scorer.migration.use.bitmap", havingValue = "true")
    public RecordIndexCalculator recordIndexCalculator() {
        log.info("Creating RecordIndexCalculator for optimized migration");
        return new RecordIndexCalculator();
    }

    /**
     * 迁移任务执行器
     * 专用于迁移任务的线程池
     */
    @Bean("migrationTaskExecutor")
    public Executor migrationTaskExecutor() {
        log.info("Creating migration task executor");
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数
        executor.setMaxPoolSize(4);
        
        // 队列容量
        executor.setQueueCapacity(10);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Migration-");
        
        // 拒绝策略：由调用者线程执行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("Migration task executor initialized: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
