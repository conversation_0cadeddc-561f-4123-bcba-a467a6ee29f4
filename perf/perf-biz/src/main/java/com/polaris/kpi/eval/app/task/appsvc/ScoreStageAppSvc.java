package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.company.GlobalConfEnum;
import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.com.polaris.kpi.company.TenantSysConf;
import cn.com.polaris.kpi.eval.Name;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.perf.www.common.em.CompanyMsgActionEnum;
import com.polaris.acl.msg.domain.FinishWorkReq;
import com.polaris.acl.msg.face.MsgAcl;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.app.task.dto.sumit.*;
import com.polaris.kpi.eval.domain.cycle.entity.EvalAtSnap;
import com.polaris.kpi.eval.domain.task.acl.ScoreMsgAcl;
import com.polaris.kpi.eval.domain.task.dmsvc.*;
import com.polaris.kpi.eval.domain.task.entity.*;
import com.polaris.kpi.eval.domain.task.entity.admineval.AdminTask;
import com.polaris.kpi.eval.domain.task.entity.calibrated.ResultRankInstance;
import com.polaris.kpi.eval.domain.task.entity.empeval.*;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.ScoreSortConf;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ChainNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.ecfg.chain.ScoreNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.score.TransferScorer;
import com.polaris.kpi.eval.domain.task.entity.empeval.vealResult.BatchSubmitScoreResult;
import com.polaris.kpi.eval.domain.task.entity.msg.MsgTodoAggregate;
import com.polaris.kpi.eval.domain.task.event.ChangedAskScoreEvent;
import com.polaris.kpi.eval.domain.task.event.SendTodoForInputOnScoreEvent;
import com.polaris.kpi.eval.domain.task.event.ThisStageEnded;
import com.polaris.kpi.eval.domain.task.event.msg.CancelRemoteTodoEvent;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.event.talent.ScoreSummaryEvent;
import com.polaris.kpi.eval.domain.task.event.talent.scoring.*;
import com.polaris.kpi.eval.domain.task.ext.IOkrAclSvc;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.domain.task.type.EvalScoreSummary;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.*;
import com.polaris.kpi.eval.infr.task.ppojo.*;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore2Po;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EmpEvalTaskScore3Po;
import com.polaris.kpi.eval.infr.task.ppojo.admintask.EvalScorerTypeScoresPo;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.EmpEvalForScoreV3Po;
import com.polaris.kpi.eval.infr.task.ppojo.empeval.SubmitScoreCacheDo;
import com.polaris.kpi.eval.infr.task.repimpl.RuleSnapOnEmpRepoImpl;
import com.polaris.kpi.eval.infr.task.repimpl.RuleSnapOnTaskRepoImpl;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.domain.dept.entity.CompanyMsgCenter;
import com.polaris.kpi.org.domain.dept.repo.MsgCenterRepo;
import com.polaris.kpi.org.domain.dept.type.TaskId;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.company.dao.CompanyMsgCenterDao;
import com.polaris.kpi.org.infr.company.dao.TenantSysConfDao;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.kpi.org.type.TraceKey;
import com.polaris.kpi.setting.domain.entity.ScorerTodoSummary;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: lufei
 * @CreateTime: 2022-11-27  05:56
 * @Description: 评分应用层服务 2.0
 * @Version: 1.0
 */
@Service
@Slf4j
public class ScoreStageAppSvc {
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private TaskKpiItemDao itemDao;
    @Autowired
    private TaskKpiRepo taskKpiRepo;
    @Autowired
    private EmpEvalRuleRepo evalRuleRepo;
    @Autowired
    private EvaluateTaskDao taskDao;
    @Autowired
    private OpLogDao opLogDao;
    @Autowired
    private IOkrAclSvc evalOkrAcl;
    @Autowired
    private EmpEvalDao empEvalDao;
    @Autowired
    private MsgCenterRepo centerRepo;
    @Resource
    private EvalScorerNodeRepo evalScorerNodeRepo;
    @Autowired
    private TaskUserDao userDao;
    @Autowired
    private AdminTaskDao adminTaskDao;
    @Autowired
    private CompanyMsgCenterDao centerDao;
    @Lazy
    @Autowired
    private ScoreStageAppSvc self;
    @Autowired
    private BatchScoreEvalMergeDao evalMergeDao;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private ScoreMsgAcl scoreMsgAcl;
    @Autowired
    private RuleSnapOnEmpRepoImpl onEmpRepo;

    @Autowired
    private RuleSnapOnTaskRepoImpl onTaskRepo;

    @Resource
    private CycleDao cycleDao;
    @Autowired
    private RankRuleSnapDao rankRuleSnapDao;
    @Autowired
    private ScorerSummaryTodoRepo scorerSummaryTodoRepo;
    @Autowired
    private MsgAcl msgAcl;

    @Autowired
    private EmpEvalScorerDao empEvalScorerDao;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private EmpEvalScorerNodeRepo empEvalScorerNodeRepo;
    @Autowired
    private EmpEvalScorerNodeDao scorerNodeDao;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;

    //层级中子结点评分结束
    @Transactional
    public void handleScoreNodeOfChainEnd(ScoreNodeOfChainEnd event) {
        event.startAny();
        EmpEvalMerge eval = event.getEvalRuleMerge();
        ScoreNode scoreNode = event.getScoreNode();

        while (scoreNode.hasNextSuper()) {
            ScoreNode nextSuper = scoreNode.getNextSuper();
            ChainDispatchRs scoreRsOfNode = eval.dispatchSuperOfChainNode(nextSuper, event.getTaskUser().getEmpId());
            if (scoreRsOfNode.getItemResults().isEmpty() && scoreRsOfNode.getTypeResults().isEmpty()) {
                scoreNode = scoreNode.getNextSuper();
                continue;
            }
            userRepo.saveDispatchNode(Arrays.asList(nextSuper), scoreRsOfNode);
            empEvalScorerRepo.saveDispatchNode(scoreRsOfNode.getEmpEvalScorers());//保存上级评分分发的分配结果
            EvalUser taskUser = event.getTaskUser();

            // 如果当前分发的考核人在上级节点已经评分了，则系统进行打分
            List<String> wasEvalScorerIds = this.handleTransferResults(scoreRsOfNode.getItemResults().getDatas());
            updateReviewers(scoreRsOfNode, eval, taskUser, wasEvalScorerIds);
            return;
        }

        if (!scoreNode.hasNextSuper() && eval.chainNodeIsEnd(scoreNode.getNode(), scoreNode.getOrder())) {
            ChainNode current = eval.current(scoreNode.getNode(), scoreNode.getOrder());
            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(eval, event.getTaskUser(), current);
            scoreNodeEnd.publish();
            return;
        }


        //ScoreNode nextSuper = scoreNode.getNextSuper();
        //ChainDispatchRs scoreRsOfNode = eval.dispatchSuperOfChainNode(nextSuper, event.getTaskUser().getEmpId());
        //if (scoreRsOfNode.getItemResults().isEmpty() && scoreRsOfNode.getTypeResults().isEmpty()) {
        //    new ScoreNodeOfChainEnd(eval, event.getTaskUser(), nextSuper).publish();
        //    return;
        //}
        //userRepo.saveDispatchNode(Arrays.asList(nextSuper), scoreRsOfNode);
        //EvalUser taskUser = event.getTaskUser();
        //updateReviewers(scoreRsOfNode, eval, taskUser);
    }

    public void handleScoreNodeOfChainEndV3(ScoreNodeOfChainEnd event) {
        event.startAny();
        EmpEvalMerge eval = event.getEvalRuleMerge();
        EvalUser taskUser = event.getTaskUser();
        ScoreNode scoreNode = event.getScoreNode();
        log.info("handleScoreNodeOfChainEndV3.ChainNodeEnd event event:{}", JSONObject.toJSONString(event));
        while (scoreNode.hasNextSuper()) {
            ScoreNode nextSuper = scoreNode.getNextSuper();
            ChainDispatchRs scoreRsOfNode = eval.dispatchSuperOfChainNodeV3(nextSuper);//只分发未分发的,已分发的不再发 上级的|指定多层级依次的分发
            if (scoreRsOfNode.hasNoneRsV3()) {//当前环节无需要分发,并且已完成评分
                scoreNode = scoreNode.getNextSuper();
                continue;
            }
            log.info("scoreRsOfNode:{}",JSONUtil.toJsonStr(scoreRsOfNode));
            List<EmpEvalScorerNode> sysAutoSubmitSn = eval.handleTransferAutoSubmit(scoreRsOfNode);//处理转交后需系统自动提交的节点
            tx.runTran(() -> {
                empEvalScorerRepo.saveDispatchNode(scoreRsOfNode.getEmpEvalScorers());//保存上级评分分发的分配结果
            });
            //异步处理 系统自动提交评分 和发送待办
            scoreRsOfNode.distinctMsgerV3();
            DispatchNextChainNodeEvent nextChainNodeEvent = new DispatchNextChainNodeEvent(taskUser.getCompanyId(), taskUser, eval, scoreRsOfNode, sysAutoSubmitSn, true);
            nextChainNodeEvent.fire();
            return;
        }

        if (!scoreNode.hasNextSuper() && eval.chainNodeIsEndV3(scoreNode.getNode(), scoreNode.getOrder())) {
            ChainNode current = eval.current(scoreNode.getNode(), scoreNode.getOrder());
            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(eval, event.getTaskUser(), current);
            scoreNodeEnd.publish();
            return;
        }


        //ScoreNode nextSuper = scoreNode.getNextSuper();
        //ChainDispatchRs scoreRsOfNode = eval.dispatchSuperOfChainNode(nextSuper, event.getTaskUser().getEmpId());
        //if (scoreRsOfNode.getItemResults().isEmpty() && scoreRsOfNode.getTypeResults().isEmpty()) {
        //    new ScoreNodeOfChainEnd(eval, event.getTaskUser(), nextSuper).publish();
        //    return;
        //}
        //userRepo.saveDispatchNode(Arrays.asList(nextSuper), scoreRsOfNode);
        //EvalUser taskUser = event.getTaskUser();
        //updateReviewers(scoreRsOfNode, eval, taskUser);
    }

    private SubmitRaterScore buildScoreResults(String wasEvalScorerId, List<EvalScoreResult> results, Map<String, PerfEvaluateTaskScoreResultPo> itemScoreRes) {

        Set<String> itemIds = itemScoreRes.keySet();
        results = results.stream().filter(s -> itemIds.contains(s.getKpiItemId())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(results)) {
            EvalScoreResult evalResult = results.get(0);
            List<EvalScoreResult> itemScoreList = new ArrayList<>();
            List<EvalScoreSummary> scoreSummarys = new ArrayList<>();
            EvalScoreResult evalScoreResult;
            results = results.stream().filter(s -> StrUtil.equals(wasEvalScorerId, s.getScorerId())).collect(Collectors.toList());

            for (EvalScoreResult rs : results) {
                evalScoreResult = new EvalScoreResult();
                evalScoreResult.setId(rs.getId());
                evalScoreResult.setKpiTypeId(rs.getKpiTypeId());
                evalScoreResult.setKpiItemId(rs.getKpiItemId());
                evalScoreResult.setApprovalOrder(rs.getApprovalOrder());
                evalScoreResult.setScore(itemScoreRes.get(rs.getKpiItemId()).getScore());
                evalScoreResult.setScoreLevel(itemScoreRes.get(rs.getKpiItemId()).getScoreLevel());
                evalScoreResult.setVetoFlag(itemScoreRes.get(rs.getKpiItemId()).getVetoFlag());
                evalScoreResult.setScorerType(rs.getScorerType());
                itemScoreList.add(evalScoreResult);
            }

            EvalScoreSummary scoreSummary = new EvalScoreSummary();
            scoreSummary.setCompanyId(evalResult.getCompanyId().getId());
            scoreSummary.setTaskUserId(evalResult.getTaskUserId());
            scoreSummary.setScoreType(evalResult.getScorerType());
            scoreSummarys.add(scoreSummary);

            SubmitRaterScore submitRaterScore = new SubmitRaterScore();
            submitRaterScore.setTaskUserId(evalResult.getTaskUserId());
            submitRaterScore.setIsReCommit(false);
            submitRaterScore.setItemScoreList(itemScoreList);
            submitRaterScore.setScoreSummarys(scoreSummarys);
            submitRaterScore.setScorerType(evalResult.getScorerType());
            submitRaterScore.setCompanyId(evalResult.getCompanyId());
            return submitRaterScore;
        } else {
            return null;
        }
    }


    private List<String> handleTransferResults(List<EvalScoreResult> results) {

        Map<String, List<String>> scorerIdItemMap = results
                .stream()
                .collect(Collectors.groupingBy(
                        EvalScoreResult::getScorerId, Collectors.mapping(EvalScoreResult::getKpiItemId, Collectors.toList())));

        EvalScoreResult evalScoreResult = results.get(0);
        List<String> wasEvalScorerIds = new ArrayList<>();
        for (String scorerId : scorerIdItemMap.keySet()) {
            // 查询已经评分的数据
            List<PerfEvaluateTaskScoreResultPo> resultPos = userDao
                    .findWasEvalScoreResults(scorerId, evalScoreResult.getScorerType(),
                            evalScoreResult.getCompanyId(), scorerIdItemMap.get(scorerId), evalScoreResult.getTaskId(), evalScoreResult.getTaskUserId());

            if (CollUtil.isNotEmpty(resultPos)) {
                Map<String, PerfEvaluateTaskScoreResultPo> itemScoreRes = resultPos
                        .stream()
                        .collect(Collectors.toMap(PerfEvaluateTaskScoreResultPo::getKpiItemId, Function.identity(), (a, b) -> a));

                SubmitRaterScore submitRaterScore = this.buildScoreResults(scorerId, results, itemScoreRes);
                if (ObjectUtil.isNotNull(submitRaterScore)) {
                    SubmitScoreCmd submitScoreCmd = new SubmitScoreCmd();
                    BeanUtils.copyProperties(submitRaterScore, submitScoreCmd);
                    submitScoreCmd.setWasAddLog(false);
                    submitScoreCmd.setWasTransferRater(true);
                    this.submitAnyWrap(submitScoreCmd);
                }
                // 如果被分发人所有指标在上级已经评分，则加入wasEvalScorerIds 方便下面处理不发待办业务
                if (itemScoreRes.keySet().size() == scorerIdItemMap.get(scorerId).size()) {
                    wasEvalScorerIds.add(scorerId);
                }
            }
        }
        return wasEvalScorerIds;
    }

    private void updateReviewers(ChainDispatchRs dispatchRs, EmpEvalMerge eval, EvalUser taskUser, List<String> wasEvalScorerIds) {
        List<String> scorerIds = new ArrayList<>();
        //  发送通知与待办
        dispatchRs.distinctMsger().forEach((scoreType, reveiverEmpIds) -> {
            MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(eval.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreType);
            log.info("reveiverEmpIds:{},toScene:{}", reveiverEmpIds, toScene);
            log.info("wasEvalScorerIds:{}", wasEvalScorerIds);
            if (CollUtil.isNotEmpty(reveiverEmpIds)) {
                scorerIds.addAll(reveiverEmpIds);
                if (CollUtil.isNotEmpty(wasEvalScorerIds)) {
                    reveiverEmpIds.removeAll(wasEvalScorerIds);
                }
                log.info("*****待发送消息接收人：reveiverEmpIds:{}", reveiverEmpIds);
                MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(eval.getCompanyId(), taskUser.getTaskId(), eval.taskName(), taskUser.getEmpId(), taskUser.getId())
                        .addExtTempValue("empName", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                        .addTodoItem("msg.task.emp", taskUser.getEvalOrgName() == null ? taskUser.getEmpName() : taskUser.getEvalOrgName())
                        .useScene(toScene)
                        .addRecEmpId(reveiverEmpIds);
                msgTodoAggregate.sendExtTodo().sendExtMsg().addCenterMsg();
                msgTodoAggregate.publish();
            }
        });
        // 更新负责人信息
        //做加入，而不是覆盖
        taskUser.addReviewers(kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds));
        userRepo.updateTaskUser(taskUser);
    }


    public void fixSendScoreToDo(TenantId companyId, String taskUserId) {
        if (StrUtil.isEmpty(taskUserId) || Objects.isNull(companyId)) {
            return;
        }

        EvalUser evalUser = empEvalDao.getBaseUser(companyId.getId(), taskUserId);
        EmpEvalMerge empEvalMerge = evalRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.type);
        if (Objects.isNull(evalUser) || Objects.isNull(empEvalMerge)) {
            return;
        }
        // //先查询 score rs auditStatus is null
        List<EvalScoreResult> results = userDao.listResultByScoreTypeAndAuditStatus(companyId, taskUserId);
        if (CollUtil.isEmpty(results)) {
            return;
        }
        List<String> scoreIds = results.stream().map(EvalScoreResult::getScorerId).collect(Collectors.toList());
        List<CompanyMsgCenter> msgCenters = centerDao.listNotHandleMsg(companyId, taskUserId);
        if (CollUtil.isNotEmpty(msgCenters)) {
            msgCenters.stream().map(CompanyMsgCenter::getEmpId).forEach(scoreIds::remove);
        }
        for (EvalScoreResult scoreResult : results) {
            if (!scoreIds.contains(scoreResult.getScorerId())) {
                continue;
            }
            MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(empEvalMerge.isCustom() ? SubScoreNodeEnum.CUSTOM.getScene() : scoreResult.getScorerType());
            log.info("reveiverEmpIds:{},toScene:{}", scoreResult.getScorerId(), toScene);
            if (Objects.isNull(toScene)) {
                return;
            }
            MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(companyId, evalUser.getTaskId(), empEvalMerge.taskName(), evalUser.getEmpId(), evalUser.getId())
                    .useScene(toScene)
                    .addRecEmpId(Collections.singletonList(scoreResult.getScorerId()));
            msgTodoAggregate.sendExtTodo().sendExtMsg().addCenterMsg();
            msgTodoAggregate.publish();
        }
    }

    //层级评分结束
    @Transactional
    public void handleChainNodeEnd(ChainNodeEnd event) {
        event.startAny();
        EmpEvalMerge eval = event.getEvalRuleMerge();
        ChainNode next = null == event.getChainNode() ? null : event.getChainNode().getNext();
        next = eval.skipFinishedChainNode(next);//跳过已完成chainNode
        log.info("skipToEndChain next= {}", next == null ? null : JSONObject.toJSONString(next));
        EvalUser taskUser = event.getTaskUser();
        if (next == null && eval.allScorePassed()) {//流程完成,直接结束评分阶段
            log.info("流程完成,直接结束评分阶段");
//            加减分类指标得分计算
            eval.computePlusSubMaxScore();
            List<EvalScoreResult> evalScoreResults = eval.recomputeUseDelMinMaxOpt(taskUser.isOpenAvgWeightCompute());
            userRepo.batchUpdateScoreResult(evalScoreResults);
            FinalWeightSumScore weightSumScore = eval.computeFinalScore(taskUser.getFinalItemAutoScore(), taskUser.isOpenAvgWeightCompute());
            taskUser.computeLevel(weightSumScore, eval.getFinalScore(), eval.needComputeLevel());
            if (event.isEndStage()) {
                taskUser.setScoreEndFlag(true);
                taskUser.handleAskScore(eval.listAsk360Types());
                //是否关联问卷，且是否问卷完成，若未完成，不能进入下一阶段。
                if (taskUser.isRefAsk360Flag() && !taskUser.isHasAskEndScore()) {
                    userRepo.updateTaskUser(taskUser);
                    return;
                }
            }
            userRepo.updateEvalScore(taskUser);
            if (eval.inputOnScoring() && itemDao.needInputForAutoItem(event.getTenantId(), eval.getEmpEvalId())) {
                return;
            }
            if (event.isEndStage()) {
                //taskDao.renewalRefEval(taskUser.getCompanyId().getId(),taskUser.getId());
                new ThisStageEnded(eval, event.getTaskUser(), TalentStatus.SCORING).publish();
            }
            return;
        }
        ChainDispatchRs typeDispatchRs = eval.dispatchChainNode(next, taskUser.getEmpId(), taskUser.isOpenAvgWeightCompute());//只分发未分发的,已分发的不再发
        if (typeDispatchRs.hasNoneRs()) {//当前环节无需要分发,并且已完成评分
            return;
        }
        userRepo.saveDispatchNode(next.getScoreNodes(), typeDispatchRs);
        empEvalScorerRepo.saveDispatchNode(typeDispatchRs.getEmpEvalScorers());//分发评分人评分环节
        // 判断当前分发的评分 在上级是否评分过
        List<EvalScoreResult> results = typeDispatchRs.getItemResults().getDatas();
        List<String> wasEvalScorerIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(results)) {
            wasEvalScorerIds = this.handleTransferResults(results);
        }

        taskUser.setTaskName(eval.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(eval, taskUser);
        if (!todoDmSvc.support() || !typeDispatchRs.getTotalResults().isEmpty()) {
            //如果还是按照人员发送的，按原有逻辑直接发
            //  发送通知与待办
            updateReviewers(typeDispatchRs, eval, taskUser, wasEvalScorerIds);
        } else {

            //使用独立的事务让更新提交
            Set<String> scorerIds = scorerSummaryTodoRepo.dispatchScorerInstance(typeDispatchRs, taskUser, eval);
            new ScoreSummaryEvent(eval.getCompanyId().getId(), scorerIds, eval, taskUser).publish();
            Set<String> newSet = new HashSet<>();
            newSet.addAll(scorerIds);
            if (hasSelfScore(typeDispatchRs)) {//如果有自评，正常发
                String scene;
                if (eval.isCustom()) {
                    scene = SubScoreNodeEnum.CUSTOM.getScene();
                } else {
                    scene = SubScoreNodeEnum.SELF_SCORE.getScene();
                }
                MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(scene);
                MsgTodoAggregate msgTodoAggregate = new MsgTodoAggregate(eval.getCompanyId(), taskUser.getTaskId(), eval.taskName(), taskUser.getEmpId(), taskUser.getId())
                        .useScene(toScene)
                        .addRecEmpId(taskUser.getEmpId());
                msgTodoAggregate.sendExtTodo().sendExtMsg().addCenterMsg();
                msgTodoAggregate.publish();
                newSet.add(taskUser.getEmpId());
            }
            // 更新负责人信息
            List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), newSet);
            taskUser.reviewers(raters);
            userRepo.updateTaskUser(taskUser);
        }
    }


    //层级评分结束
    public void handleChainNodeEndV3(ChainNodeEnd event) {
        event.startAny();
        EmpEvalMerge eval = event.getEvalRuleMerge();
        ChainNode next = null == event.getChainNode() ? null : event.getChainNode().getNext();
        log.info("handleChainNodeEndV3.ChainNodeEnd event event:{}", JSONObject.toJSONString(event));
        next = eval.skipFinishedChainNode(next);//跳过已完成chainNode
        log.info("handleChainNodeEndV3.skipToEndChain next:{}", next == null ? null : JSONObject.toJSONString(next));
        EvalUser taskUser = event.getTaskUser();
        if (next == null && eval.allScorePassedV3()) {//流程完成,直接结束评分阶段
            log.info("[handleChainNodeEndV3]流程完成,直接结束评分阶段");
            Boolean isNeedInputForAutoItem = itemDao.needInputForAutoItem(event.getTenantId(), eval.getEmpEvalId());
            AllScorePassedDmSvc dmSvc = new AllScorePassedDmSvc(eval, taskUser, event.isEndStage(), event.getTenantId(), isNeedInputForAutoItem);
            dmSvc.reComputeFinalScore();//重新计算总分
            //重新计算后保存结果
            tx.runTran(() -> {
                onScoreEvalRepo.saveEvalNodeScore(dmSvc.getTaskUser(), dmSvc.getEval().getKpiTypes(), null, dmSvc.getEval().getEvalScorersWrap());
            });
            dmSvc.flowEnd();//流程结束
            return;
        }

        ChainDispatchRs typeDispatchRs = eval.dispatchChainNodeV3(next, taskUser.isOpenAvgWeightCompute());//只分发未分发的,已分发的不再发
        if (typeDispatchRs.hasNoneRsV3()) {//当前环节无需要分发,并且已完成评分
            return;
        }
        log.info("typeDispatchRs:{}",JSONUtil.toJsonStr(typeDispatchRs));
        List<EmpEvalScorerNode> sysAutoSubmitSn = eval.handleTransferAutoSubmit(typeDispatchRs);//处理转交后需系统自动提交的节点
        tx.runTran(() -> {
            empEvalScorerRepo.saveDispatchNode(typeDispatchRs.getEmpEvalScorers());//分发评分人评分环节
        });
        //异步处理 系统自动提交评分 和发送待办
        typeDispatchRs.distinctMsgerV3();
        DispatchNextChainNodeEvent nextChainNodeEvent = new DispatchNextChainNodeEvent(taskUser.getCompanyId(), taskUser, eval, typeDispatchRs, sysAutoSubmitSn, false);
        nextChainNodeEvent.fire();
    }

    private boolean hasSelfScore(ChainDispatchRs typeDispatchRs) {

        Map<String, Set<String>> stringSetMap = typeDispatchRs.distinctMsger();
        //只要存在自评的key，就认为有自评
        return stringSetMap.keySet().stream().anyMatch(key -> SubScoreNodeEnum.SELF_SCORE.getScene().equals(key));

    }

    //变更问卷分数
    @Transactional
    public void changedAskScore(ChangedAskScoreEvent event) {
        String taskUserId = userRepo.getAskRefTaskUserId(new TenantId(event.getCompanyId()), event.getAsk360EvalId());
        if (StrUtil.isBlank(taskUserId)) {
            return;
        }
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(new TenantId(event.getCompanyId()), taskUserId, EmpEvalMerge.all);
        evalMerge.setAskEvalScore(event.getAsk360EvalId(), event.getAsk360EvalScore(), event.getAsk360EvalTotalScore());
        EvalUser evalUser = userRepo.getTaskUser(new TenantId(event.getCompanyId()), taskUserId);
        //变更问卷维度分数
        taskKpiRepo.changedAskTypeScore(evalMerge.listAsk360Types(), event.getCompanyId(), event.getOpEmpId());
        //评分未结束，不处理
        if (!evalUser.getScoreEndFlag()) {
            return;
        }
        //关联的问卷是否全部完成
        if (!evalMerge.hasAskAllEndScore()) {
            return;
        }
        evalUser.computeAskEvalScore(evalMerge.listAsk360Types());
        userRepo.updateTaskUser(evalUser);
        //进入下一阶段
        new ThisStageEnded(evalMerge, evalUser, TalentStatus.SCORING).publish();
    }


    //变更问卷分数V3
    public void changedAskScoreV3(ChangedAskScoreEvent event) {
        String taskUserId = userRepo.getAskRefTaskUserId(new TenantId(event.getCompanyId()), event.getAsk360EvalId());
        if (StrUtil.isBlank(taskUserId)) {
            return;
        }
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(new TenantId(event.getCompanyId()), taskUserId, EmpEvalMerge.all);
        evalMerge.setAskEvalScore(event.getAsk360EvalId(), event.getAsk360EvalScore(), event.getAsk360EvalTotalScore());
        EvalUser evalUser = userRepo.getTaskUser(new TenantId(event.getCompanyId()), taskUserId);
        tx.runTran(() -> {
            //变更问卷维度分数
            taskKpiRepo.changedAskTypeScoreV3(taskUserId, evalMerge.listAsk360Types(), event.getCompanyId(), event.getOpEmpId());
        });

        SumScorerComputeDmSvc computeDmSvc = new SumScorerComputeDmSvc(evalUser, evalMerge);
        computeDmSvc.computeSumScore();//计算总分，环节总分，维度总分，指标总分
        tx.runTran(() -> {
            this.onScoreEvalRepo.saveEvalNodeScore(computeDmSvc.getTaskUser(), computeDmSvc.getKpiTypes(), event.getOpEmpId(), computeDmSvc.getEvalMerge().getEvalScorersWrap());//保存计算后的环节分数
        });
        //评分未结束，不处理 或者  //关联的问卷未全部完成 不结束评分
        if (!evalUser.getScoreEndFlag() || !evalMerge.hasAskAllEndScore()) {
            return;
        }
        //进入下一阶段
        new ThisStageEnded(evalMerge, evalUser, TalentStatus.SCORING).publish();
    }

    //层级评分结束
    @Transactional
    public void reCompute(TenantId companyId, String taskUserId, Boolean fixItemScore) {
        log.info("v2.0,重新算分接口");
        EmpEvalMerge eval = evalRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        boolean isTypeWeightOpen = eval.isTypeWeightOpen();
        boolean computed = !eval.inputOnScoring() && taskUser.tryComputeAutoScore(isTypeWeightOpen, eval.getScoreValueConf().getCustomFullScore(), eval.getScoreValueConf().isFullScoreRange(), eval.openItemAutoScoreMultiplWeight());
        eval.computePlusSubMaxScore();
        eval.recomputeUseDelMinMaxOpt(taskUser.isOpenAvgWeightCompute());
        if (fixItemScore != null && fixItemScore) {//scoreResult.finalWeightScore分修正
            List<EvalScoreResult> scoreResults = eval.fixItemScore();
            userRepo.batchUpdateScoreResult(scoreResults);
//            userRepo.batchFixScoreResult(scoreResults);
        }
        FinalWeightSumScore weightSumScore = eval.computeFinalScore(taskUser.getFinalItemAutoScore(), taskUser.isOpenAvgWeightCompute());
        taskUser.computeLevel(weightSumScore, eval.getFinalScore(), eval.needComputeLevel());
        if (computed) {
            userRepo.updateAutoScore(taskUser);
        } else {
            userRepo.updateTaskUser(taskUser);
        }
    }

    //提交串行评分 ,人员结点结束->所有人员结点结束+主结点派发完成 立刻消失,当前责任人.
    @Transactional
    public void submitAnyWrap(List<SubmitScoreCmd> cmds) {

        SubmitScoreCmd cmd = buildSubmitCmd(cmds);
        TenantId companyId = cmd.getCompanyId();
        userRepo.lockTaskUser(cmd.getCompanyId(), cmd.getTaskUserId());//锁定考核主表,并行提交时进行事务排队
        EvalUser taskUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        List<String> scorerIds = new ArrayList<>();
        List<String> msgScenes = new ArrayList<>();
        List<SubmitScoreCmd> notEndCmds = new ArrayList<>();
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(companyId, cmd.getTaskUserId(), EmpEvalMerge.all);
        ChainNode current = evalMerge.current(cmd.getScoreNode(), cmd.getNodeOrder());
        log.info("=====开始处理submitAnyWrap.TaskUserId:{},scornode:{},nodeOrder:{}", cmd.getTaskUserId(), cmd.getScoreNode(), cmd.getNodeOrder());
        for (SubmitScoreCmd submitCmd : cmds) {
            //检测是否或签及重复提交评分
            if (!submitCmd.isReCommit()) {
                evalMerge.checkReSubmited(submitCmd.getItemScoreList(), submitCmd.getTotal());
            }
            List<EvalScoreResult> computedRs = evalMerge.submitAndComputeItem(submitCmd.getItemScoreList(), submitCmd.getTotal());
            List<PerfEvalTypeResult> computedTypes = evalMerge.submitAndComputeTypeLevel(submitCmd.getTypeScores());
            //检验转交提交
            this.checkTransferScoreSubmit(evalMerge, submitCmd);
            //筛选出同级主结点或签方式子结点
            List<EvalScoreResult> orModeRss = evalMerge.markOrModeScoreRs(submitCmd.getScoreNode(), submitCmd.getItemScoreList());
            List<String> orScorerIds = orModeRss.stream().map(EvalScoreResult::getScorerId).collect(Collectors.toList());
            List<String> orFinishedScorerIds = evalMerge.orFinishedScorer(orScorerIds);
            log.info("=====submitAnyWrap.orFinishedScorerIds:{}", orFinishedScorerIds);
            userRepo.saveSubmitScore(evalMerge, computedTypes, computedRs, submitCmd.getTotal(), submitCmd.getScoreSummarys(),
                    submitCmd.isReCommit(), cmd.isSystemSkip(), submitCmd.getSignatureUrl(), orFinishedScorerIds, submitCmd.getWasAddLog());
            //更新同级主结点或签方式子结点
            userRepo.batchUpdateScoreResult(orModeRss);
            scorerIds.addAll(orFinishedScorerIds);
            scorerIds.addAll(submitCmd.scoreIds());
            log.info("当前scorerIds： scorerIds:{}", scorerIds);
            //取消当前评分人的代办
            String msgScene = evalMerge.isCustom() ? MsgSceneEnum.TASK_ALL_SCORE.getType() : submitCmd.getScoreNode().todoScenes();
            msgScenes.add(msgScene);
            if (StrUtil.equals(msgScene, MsgSceneEnum.TASK_SELF_SCORE.getType())) {
                scorerIds.add(taskUser.getEmpId());//如果当前待办是自评，需增加当前被考核人作为评分人，因为跳过责任人会丢失，待办也需要清除
            }

            //评分层级未完成-评分结点是否结束
            if (evalMerge.nodeIsEnd(submitCmd.getScoreNode(), submitCmd.getNodeOrder())) {
                log.info("当前评分节点结束： " + submitCmd.getScoreNode());
//                userRepo.finishedScoreFlag(companyId.getId(), evalMerge.getEmpEvalId(), submitCmd.getScoreNode());
                if (CollUtil.isNotEmpty(orModeRss)) {
                    List<String> orFilterWithOutFinishScorers = evalMerge.orFilterWithOutFinishScorer(orScorerIds);
                    log.info("当前orFilterWithOutFinishScorers：{} ", orFilterWithOutFinishScorers);
                    scorerIds.addAll(orFilterWithOutFinishScorers);
                }
                scorerIds.addAll(computedTypes.stream().map(PerfEvalTypeResult::getScorerId).collect(Collectors.toList()));
            }
            if (!evalMerge.subFlowIsEnd(submitCmd.getScoreNode(), submitCmd.getNodeOrder())) {
                notEndCmds.add(submitCmd);
            }
        }
        List<CompanyMsgCenter> progressTodos = centerRepo.finishByQuery(companyId, taskUser.getId(), MsgSceneEnum.TASK_SUBMIT_PROGRESS.getType(), null);
        scorerIds.addAll(progressTodos.stream().map(CompanyMsgCenter::getEmpId).collect(Collectors.toList()));
        taskUser.removeReviewers(scorerIds);
        userRepo.updateTaskUser(taskUser);
        //submitCurScorerScore(cmd,cmds,taskUser,evalMerge);//保存评分人数据
        // 此处需添加转交评分的判断，如果或签的其他评分人在其他主节点，则不删除待办
        if (CollUtil.isNotEmpty(scorerIds) && evalMerge.getWasTransferRater() && CollUtil.isNotEmpty(evalMerge.getTotalEvalRs())) {
            List<String> transferScorerIds = evalMerge.transferScorerIds(scorerIds, cmd.getEmpId());
            log.info("当前transferScorerIds:{}", transferScorerIds);
            if (CollUtil.isNotEmpty(transferScorerIds)) {
                scorerIds.removeAll(transferScorerIds);
            }
        }
        log.info("最终scorerIds:{}", scorerIds);
        List<CompanyMsgCenter> todos = new ArrayList<>();
        if (CollUtil.isNotEmpty(scorerIds)) {
            todos = centerRepo.finishByQuery(companyId, taskUser.getId(), msgScenes, scorerIds);
        }


        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalMerge, taskUser);
        if (!todoDmSvc.support()) {
            CancelRemoteTodoEvent cancelRemoteTodoEvent = new CancelRemoteTodoEvent(companyId, todos);
            cancelRemoteTodoEvent.addTodos(progressTodos);
            cancelRemoteTodoEvent.publish();
        } else {
            //单独处理下自评，自评需要关闭钉钉待办
            if (submitSelfScore(cmds)) {
                String scene;
                if (evalMerge.isCustom()) {
                    scene = MsgSceneEnum.TASK_ALL_SCORE.getType();
                } else {
                    scene = MsgSceneEnum.TASK_SELF_SCORE.getType();
                }
                List<CompanyMsgCenter> collect = todos.stream().filter(todo -> StrUtil.equals(todo.getBusinessScene(), scene)).collect(Collectors.toList());
                CancelRemoteTodoEvent cancelRemoteTodoEvent = new CancelRemoteTodoEvent(companyId, collect);
                cancelRemoteTodoEvent.addTodos(progressTodos);
                cancelRemoteTodoEvent.publish();
            }
            List<CompanyMsgCenter> msgCenters = new ArrayList<>();
            CancelRemoteTodoEvent cancelRemoteTodoEvent = new CancelRemoteTodoEvent(companyId, msgCenters);
            cancelRemoteTodoEvent.addTodos(progressTodos);
            cancelRemoteTodoEvent.publish();
            //进行汇总评分下的处理
            //判断下是不是自评跳过
            if (!selfScoreSkip(cmds)) {
                HashSet<String> scorers = new HashSet<>(scorerIds);
                scorerSummaryTodoRepo.updateFinishScoreStatus(companyId.getId(), taskUser.getId(), scorers);
                todoDmSvc.setRepo(scorerSummaryTodoRepo);
                todoDmSvc.refreshSummary(scorers);
                List<ScorerTodoSummary> updateSummaries = todoDmSvc.getUpdateSummaries();
                for (ScorerTodoSummary updateSummary : updateSummaries) {
                    if (updateSummary.readyToClose()) { //关闭钉钉待办
                        FinishWorkReq req = new FinishWorkReq(taskUser.getCompanyId(), new EmpId(updateSummary.getScorerId()), updateSummary.getThirdMsgId());
                        boolean isSuccess = false;
                        try {
                            msgAcl.finishTodoWork(req);
                            isSuccess = true;
                        } catch (Exception e) {
                            log.error("完成待办失败:" + e.getMessage(), e);
                        }
                        if (isSuccess) {
                            updateSummary.setTodoStatus(2);
                        }
                        updateSummary.setUpdatedTime(new Date());
                    }
                }
                scorerSummaryTodoRepo.batchUpdateSummaries(updateSummaries);
            }
        }
        if (evalMerge.inputOnScoring()) {
            /**如果该企业开启了评分环节，自动评分发送录入完成待办*/
            new SendTodoForInputOnScoreEvent(companyId, taskUser.getId(), new Name(evalMerge.getTaskName()), evalMerge.sendInputMsg()).publish();
        }

        if (!notEndCmds.isEmpty()) {
            log.info("上级评分依次分发");
            for (SubmitScoreCmd notEndCmd : notEndCmds) {
                log.info(" scoreNode = " + notEndCmd.getScoreNode() + " nodeOrder =" + notEndCmd.getNodeOrder());
                ScoreNode scoreNode = evalMerge.currentScoreNoe(notEndCmd.getScoreNode(), notEndCmd.getNodeOrder());
                new ScoreNodeOfChainEnd(evalMerge, taskUser, scoreNode).fire();
            }
            return;
        }

        //层级是否结束
        if (evalMerge.chainNodeIsEnd(cmd.getScoreNode(), cmd.getNodeOrder())) {
            log.info("评分层级结束" + cmd.getScoreNode());
            //下一个主层级
            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, current);
            scoreNodeEnd.fire();
        }
//        if (evalMerge.isAnyNotDispactched(current)) {//补偿一下, 历史数据中有未分发的环节再补分发一次
//            log.info("通过AUTO补偿一下分发,有未分发的环节再补分发一次 {}", taskUser.getId());
//            ChainNode auto = evalMerge.current(SubScoreNodeEnum.AUTO, 1);
//            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, auto);
//            scoreNodeEnd.fire();
//        }
    }

    private boolean submitSelfScore(List<SubmitScoreCmd> cmds) {
        return cmds.stream()
                .anyMatch(cmd -> SubScoreNodeEnum.isSelfScore(cmd.getScoreNode().getScene()));
    }

    private boolean selfScoreSkip(List<SubmitScoreCmd> cmds) {
        return cmds.size() == 1 && cmds.stream()
                .anyMatch(cmd -> SubScoreNodeEnum.isSelfScore(cmd.getScoreNode().getScene()));
    }


    public BatchSubmitScoreResult batchSubmitScenesScoreV3(String tid, TenantId tenantId, EmpId opEmpId, List<SubmitScoreV3Cmd> cmds) {
        MDC.put(TraceKey.TID, tid);
        return batchSubmitScenesScoreV3(tenantId, opEmpId, cmds);
    }

    public BatchSubmitScoreResult batchSubmitScenesScoreV3(TenantId tenantId, EmpId opEmpId, List<SubmitScoreV3Cmd> cmds) {
        CompanyConf conf = companyDao.findCompanyConf(tenantId);
        List<String> taskUserIds = CollUtil.map(cmds, SubmitScoreV3Cmd::getTaskUserId, true);
        ListWrap<EvalOnScoreStage> onScoreEvals = onScoreEvalRepo.listOnScoreEval(tenantId, taskUserIds);
        BatchSubmitScoreDmSvc valueDmSvc = new BatchSubmitScoreDmSvc(opEmpId, conf, onScoreEvals);
        for (SubmitScoreV3Cmd cmd : cmds) {
            log.info(" 开始进行单个提交 opEmpId:{},taskUserId：{}", opEmpId,cmd.getTaskUserId());
            valueDmSvc.submitScoreOne(cmd.getTaskUserId(), cmd.getNodeScores());//提交评分
        }
        tx.runTran(() -> {
            onScoreEvalRepo.batchSaveScore(valueDmSvc.getOks(), opEmpId.getId());//保存更新后的业务状态.
        });
        new BatchScoreStageSubmited(conf, opEmpId, valueDmSvc.getOks()).fire();  //发送批量提交评价事件
        log.info(" 批量提交评分存在无需评分任务的results：{}", JSONUtil.toJsonStr(valueDmSvc.getBatchResult()));
        return valueDmSvc.getBatchResult();
    }

    public void sendMsgForDispatchedScorer(EvalUser taskUser, EmpEvalMerge eval, ChainDispatchRs dispatchNode,
                                           List<String> wasEvalScorerIds, boolean isSuperScoreNode) {
        taskUser.setTaskName(eval.getTaskName());
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(eval, taskUser);
        tx.runTran(() -> {
            //发送通知与待办
            Set<String> scorerIds = todoDmSvc.handleTodo(wasEvalScorerIds, dispatchNode, scorerSummaryTodoRepo, scoreMsgAcl, isSuperScoreNode);
            log.info("发送待办给分发的评分人，scorerIds:{},wasEvalScorerIds：{}", scorerIds, wasEvalScorerIds);
            // 更新负责人信息
            List<KpiEmp> raters = kpiEmpDao.listByEmp(taskUser.getCompanyId(), scorerIds);
            taskUser.addReviewers(raters);
            userRepo.updateTaskUser(taskUser);
        });
    }


    public void evalOnScoreOrEvalHandler(String companyId, String opEmpId, EvalOnScoreStage onScoreStage) {
        TenantId tenantId = new TenantId(companyId);
        EvalUser evalUser = onScoreStage.getEvalUser();
        EmpEvalMerge empEval = onScoreStage.getEmpEval();
        EvalScorersWrap scorersWrap = this.empEvalScorerRepo.getEmpEvalScorersWrap(companyId, evalUser.getId());
        empEval.setEvalScorersWrap(scorersWrap);//需要加载最新的，提交后数据有变化
        //或签评分人处理
        SubmitOrEvalScorerV3DmSvc orEvalScorerV3DmSvc = new SubmitOrEvalScorerV3DmSvc(tenantId, opEmpId, evalUser, empEval,
                onScoreStage.getNodeScores(),onScoreStage.getScorerIds()); //处理或签的
        orEvalScorerV3DmSvc.submitOrScorer();
        tx.runTran(() -> {
            log.info("处理或签的评分人，orEvalScorerV3DmSvc：{}", orEvalScorerV3DmSvc.getOrFinishedScorerIds());
            this.onScoreEvalRepo.saveEvalOrScorer(orEvalScorerV3DmSvc);//保存或签评分人提交
            //需要清除或签人员的待办  //先清理本地的待办,再异步清远端的.
            if (CollUtil.isNotEmpty(orEvalScorerV3DmSvc.getOrFinishedScorerIds())) {
                List<CompanyMsgCenter> todos = this.centerRepo.finishByQuery(tenantId, evalUser.getId(), onScoreStage.getMsgScenes(), orEvalScorerV3DmSvc.getOrFinishedScorerIds());
                onScoreStage.accpCancelMsg(todos);
            }
        });
        onScoreStage.accpScoreIds(orEvalScorerV3DmSvc.getScorerIds());
        log.info("onScoreStage.scorerIds:{}", onScoreStage.getScorerIds());
    }


    public void evalOnScoreStageFlowChange(String companyId, String opEmpId, EvalUser evalUser, EvalOnScoreStage onScoreStage) {
        TenantId tenantId = new TenantId(companyId);
        EmpEvalMerge empEval = onScoreStage.getEmpEval();
        //流程结束处理
        SubmitScoreFlowV3DmSvc flowV3DmSvc = new SubmitScoreFlowV3DmSvc(tenantId, opEmpId, evalUser, empEval, onScoreStage.getNodeScores());
        flowV3DmSvc.flowNodeEnd();
        log.info("evalOnScoreStageFlowChange end.taskUserId:{}", evalUser.getId());
    }

    public EvalUser computeEvalNodeScore(String companyId, String opEmpId, String taskUserId) {
        TenantId tenantId = new TenantId(companyId);
        EvalOnScoreStage onScoreStage = onScoreEvalRepo.getOnScoreEval(tenantId, taskUserId);
        SumScorerComputeDmSvc computeDmSvc = new SumScorerComputeDmSvc(onScoreStage.getEvalUser(), onScoreStage.getEmpEval());
        computeDmSvc.computeSumScore();//计算总分，环节总分，维度总分，指标总分
        tx.runTran(() -> {
            this.onScoreEvalRepo.saveEvalUserNodeScore(computeDmSvc.getTaskUser(), computeDmSvc.getKpiTypes(), opEmpId);//保存计算后的环节分数
        });
        return computeDmSvc.getTaskUser();
    }

    public void computeEmpEvalSNScoreByTaskUserId(String companyId, String taskUserId) {
        TenantId tenantId = new TenantId(companyId);
        EvalOnScoreStage onScoreStage = onScoreEvalRepo.getOnScoreEval(tenantId, taskUserId);
        EmpEvalMerge empEvalMerge = onScoreStage.getEmpEval();
        empEvalMerge.initTypeWeight();//init 维度权重
        empEvalMerge.getEvalScorersWrap().computeScorerNodeScore(empEvalMerge, onScoreStage.getEvalUser());//计算
        tx.runTran(() -> {
            this.onScoreEvalRepo.saveEmpEvalSNScoreByTaskUserId(companyId, taskUserId, empEvalMerge.getEvalScorersWrap());//保存计算后的环节分数
        });
    }


    public void reComputeFinalScore(String companyId, String taskUserId) {
        TenantId tenantId = new TenantId(companyId);
        EvalOnScoreStage onScoreStage = onScoreEvalRepo.getOnScoreEval(tenantId, taskUserId);
        EmpEvalMerge empEvalMerge = onScoreStage.getEmpEval();
        EvalUser evalUser = onScoreStage.getEvalUser();
        AllScorePassedDmSvc dmSvc = new AllScorePassedDmSvc(empEvalMerge, evalUser, false, companyId, false);
        dmSvc.reComputeFinalScore();//重新计算总分
        //重新计算后保存结果
        tx.runTran(() -> {
            onScoreEvalRepo.saveEvalNodeScore(dmSvc.getTaskUser(), dmSvc.getEval().getKpiTypes(), null, dmSvc.getEval().getEvalScorersWrap());
        });
    }


    public void clearSummaryScorerRemoteMsg(String companyId, EvalOnScoreStage submitScoreStage) {
        TenantId tenantId = new TenantId(companyId);
        EvalUser user = submitScoreStage.getEvalUser();
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(submitScoreStage.getEmpEval(), user);
        //不清除汇总待办场景：1.不支持评分汇总通知，2.自评跳过
        if (!todoDmSvc.support() || submitScoreStage.selfScoreSkip()) {
            scoreMsgAcl.clearScoreMsgTodo(tenantId, submitScoreStage, true);
            return;
        }
        //先清除需要清理的，再去处理汇总通知需要清除的待办
        if (CollUtil.isNotEmpty(submitScoreStage.getNeedCancelRemoteMsg())) {
            scoreMsgAcl.clearScoreMsgTodo(tenantId, submitScoreStage, false);
        }
        //进行汇总评分下的处理
        HashSet<String> scorers = new HashSet<>(submitScoreStage.getScorerIds());
        tx.runTran(() -> {
            todoDmSvc.clearSummary(scorerSummaryTodoRepo, scoreMsgAcl, tenantId, scorers);
        });
    }

    public void transferScorerSummarToDo(String toEmpId, String fromEmpId, EmpEvalScorerNode fromScoreNodeOne, Set<String> msgScenes,
                                         List<CompanyMsgCenter> toMsgCenters, EvalUser evalUser, EmpEvalMerge evalRule) {
        //发送新待办、通知
        ScorerTodoDmSvc todoDmSvc = new ScorerTodoDmSvc(evalRule, evalUser);
        tx.runTran(() -> {
            todoDmSvc.tsScorerSummarToDo(toEmpId, fromEmpId, msgScenes, toMsgCenters, fromScoreNodeOne, scorerSummaryTodoRepo, scoreMsgAcl);
            // 更新负责人信息
            List<KpiEmp> raters = kpiEmpDao.listByEmp(evalUser.getCompanyId(), Collections.singletonList(toEmpId));
            evalUser.addReviewers(raters);
            userRepo.updateTaskUser(evalUser);
        });
    }

    private void publishReSendEvent(List<SubmitScoreCmd> notEndCmds, EmpEvalMerge evalMerge, EvalUser taskUser) {
        for (SubmitScoreCmd notEndCmd : notEndCmds) {
            log.info(" V3 scoreNode = " + notEndCmd.getScoreNode() + " nodeOrder =" + notEndCmd.getNodeOrder());
            ScoreNode scoreNode = evalMerge.currentScoreNoe(notEndCmd.getScoreNode(), notEndCmd.getNodeOrder());
            new ScoreNodeOfChainEnd(evalMerge, taskUser, scoreNode).fire();
        }
    }

    private void checkTransferScoreSubmit(EmpEvalMerge evalMerge, SubmitScoreCmd cmd) {
        if (!cmd.getWasTransferRater()) {
            TransferOrSkipRaters transferOrSkipRaters = TransferOrSkipRaters.builder()
                    .taskUserId(cmd.getTaskUserId()).scorerId(cmd.getEmpId()).scorerType(cmd.getScorerType())
                    .itemScoreList(cmd.getItemScoreList()).tenantId(cmd.getCompanyId()).build();
            if (evalMerge.hasTransferScores(cmd.getItemScoreList(), transferOrSkipRaters)) {
                evalMerge.setWasTransferRater(true);
                new TransferScoreSubmit(evalMerge, transferOrSkipRaters).publish();
            }
        }
    }

    public void fixDispatchSubOfChainNode(TenantId companyId, String taskUserId, SubScoreNodeEnum node, Integer approvalOrder) {
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
        ScoreNode scoreNode = new ScoreNode(node, approvalOrder);
        ChainDispatchRs scoreRsOfNode = evalMerge.dispatchSuperOfChainNode(scoreNode, taskUserId);
        userRepo.saveDispatchNode(Arrays.asList(scoreNode), scoreRsOfNode);
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        updateReviewers(scoreRsOfNode, evalMerge, taskUser, null);
        userRepo.updateStage(companyId, taskUserId, TalentStatus.SCORING);
    }

    private SubmitScoreCmd buildSubmitCmd(List<SubmitScoreCmd> cmds) {
        //这里是处理同一个人同时占互评和定向评两个环节，前端提交过来的顺序定向评可能在前面，提交定向评是不触发评分环节流程的，所以当定向评分和其他环节评分一起提交过来的时候，以其他环节的scorerType提交为准。
        if (cmds.size() > 1 && cmds.get(0).getScorerType().equals("item_score")) {
            return cmds.get(1);
        }
        return cmds.get(0);
    }

    //提交串行评分 ,人员结点结束->所有人员结点结束+主结点派发完成 立刻消失,当前责任人.
    @Transactional
    public void submitAnyWrap(SubmitScoreCmd cmd) {
        submitAnyWrap(Arrays.asList(cmd));
    }


    //打总等级
    @Transactional
    public void submitTotalLevel(TotalLevelCmd result) {
        result.checkParamNullAndBuild();
        result.setAuditStatus("pass");
        userRepo.saveSubmitTotalLevel(result, result.getStepId());
        //清理总评代办
        new CancelTodoEvent(result.getCompanyId(), Arrays.asList(result.getOpEmpId().getId()), result.getTaskUserId(), CompanyMsgActionEnum.CUSTOM_SCORE.getMsgType()).publish();
        //下一个主层级
        EvalUser taskUser = userRepo.getTaskUser(result.getCompanyId(), result.getTaskUserId());
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(result.getCompanyId(), result.getTaskUserId(), EmpEvalMerge.all);
        if (evalMerge.totalLevelScoreIsEnd()) {
            ChainNode current = evalMerge.current(result.getScoreNode(), 1);
            ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, current);
            scoreNodeEnd.publish();
        }
    }

    @Transactional
    public void fixChainNodeEnd(TenantId companyId, SubScoreNodeEnum scoreNode,
                                String taskUserId, Integer approvalOrder) {
        userRepo.lockTaskUser(companyId, taskUserId);//锁定考核主表,并行提交时进行事务排队
        EvalUser taskUser = userRepo.getTaskUser(companyId, taskUserId);
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(companyId, taskUserId, EmpEvalMerge.all);
        //层级是否结束
        log.info("评分层级结束" + scoreNode);
        //下一个主层级
        ChainNode current = evalMerge.current(scoreNode, approvalOrder);
        ChainNodeEnd scoreNodeEnd = new ChainNodeEnd(evalMerge, taskUser, current);
        scoreNodeEnd.publish();
    }


    //提交串行评分 ,人员结点结束->所有人员结点结束+主结点派发完成 立刻消失,当前责任人.
    @Transactional
    public void reSubmitAnyWrap(List<SubmitScoreCmd> cmds) {
        SubmitScoreCmd cmd = cmds.get(0);
        TenantId companyId = cmd.getCompanyId();
        EmpEvalMerge evalMerge = evalRuleRepo.getEmpEvalMerge(companyId, cmd.getTaskUserId(), EmpEvalMerge.all);
        List<String> scorerIds = new ArrayList<>();
        for (SubmitScoreCmd submitCmd : cmds) {
            List<EvalScoreResult> computedRs = evalMerge.submitAndComputeItem(submitCmd.getItemScoreList(), submitCmd.getTotal());
            List<PerfEvalTypeResult> typeResults = evalMerge.submitAndComputeTypeLevel(submitCmd.getTypeScores());
            userRepo.saveSubmitScore(evalMerge, typeResults, computedRs, submitCmd.getTotal(),
                    submitCmd.getScoreSummarys(), submitCmd.isReCommit(), submitCmd.isSystemSkip(), submitCmd.getSignatureUrl(), submitCmd.getWasAddLog());
            scorerIds.addAll(submitCmd.scoreIds());

            for (EvalScoreResult scoreResult : submitCmd.getItemScoreList()) {
                EvalScorerNode evalScorerNode = evalScorerNodeRepo.getEvalScorerNode(companyId, submitCmd.getTaskUserId(),
                        submitCmd.getOpEmpId().getId(), submitCmd.getScorerType(), scoreResult.getApprovalOrder());
                if (Objects.nonNull(evalScorerNode)) {
                    evalScorerNode.updateScoreStatus();
                    evalScorerNodeRepo.saveEvalScorerNode(evalScorerNode);
                }
            }
        }

        EvalUser taskUser = userRepo.getTaskUser(companyId, cmd.getTaskUserId());
        FinalWeightSumScore weightSumScore = evalMerge.computeFinalScore(taskUser.getFinalItemAutoScore(), taskUser.isOpenAvgWeightCompute());
        taskUser.computeLevel(weightSumScore, evalMerge.getFinalScore(), evalMerge.needComputeLevel());
        taskUser.removeReviewers(scorerIds);
        userRepo.updateTaskUser(taskUser);

        //重新打开总评
        List<BaseScoreResult> totalLevelRs = empEvalDao.listAllTotalLevelRs(companyId.getId(), cmd.getTaskUserId());
        if (evalMerge.allScorePassed() && !totalLevelRs.isEmpty()) {
            userRepo.openTotalLevelScoreResult(companyId, cmd.getTaskUserId());
            List<String> totalLevelScorerIds = totalLevelRs.stream().map(baseScoreResult -> baseScoreResult.getScorerId()).collect(Collectors.toList());
            MsgSceneEnum toScene = SubScoreNodeEnum.todoScene(SubScoreNodeEnum.CUSTOM.getScene());
            new MsgTodoAggregate(companyId, taskUser.getTaskId(), evalMerge.taskName(), taskUser.getEmpId(), taskUser.getId())
                    .useScene(toScene).sendExtTodo().sendExtMsg().addCenterMsg()
                    .addRecEmpId(totalLevelScorerIds).publish();
        }

        if (evalMerge.inputOnScoring() && itemDao.needInputForAutoItem(companyId.getId(), evalMerge.getEmpEvalId())) {
            return;
        }
        new CancelTodoEvent(companyId, scorerIds, evalMerge.getEmpEvalId()).publish();


        if (evalMerge.allScorePassed() && evalMerge.needComputeLevel() && taskUser.getTaskStatus().equals(TalentStatus.SCORING.getStatus())) {
            new ThisStageEnded(evalMerge, taskUser, TalentStatus.SCORING).publish();
            return;
        }
    }

    public EmpEvalTaskScore2Po scoreDetail(TenantId tenantId, String taskUserId, String opEmpId) {
        EmpEvalMerge evalRule = evalRuleRepo.getScoreEmpEvalDetail(tenantId, taskUserId, opEmpId);
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        //FinalWeightSumScore sumScore = taskDao.sumFinalScore(tenantId, taskUserId);
        List<PerfEvaluateTaskScoreResultDo> resultDos = empEvalDao.listScoreRs(tenantId.getId(), taskUserId, null, opEmpId);
        Set<String> scoreTypes = resultDos.stream().map(s -> s.getScorerType()).collect(Collectors.toSet());//页面需要校准的类型
        FinalWeightSumScore sumScore = evalRule.computeFinalScore(taskUser.getFinalItemAutoScore(), EvalScoreResult::getFinalScore, taskUser.isOpenAvgWeightCompute());
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, evalRule.getKpiTypes().scoreIds());
        evalOkrAcl.loadOkrIf(tenantId, evalRule.okrItems(), taskUser.getEmpId());
        CycleEval taskBaseModel = taskDao.getMergeTaskBase(tenantId, taskUserId);
        PerfEvaluateTaskScoreResultDo calibration = empEvalDao.getTaskResultIndexCalibration(tenantId.getId(), taskUserId);
        BindEvalResult bindEvalResult = taskDao.getBindEvalResult(tenantId.getId(), taskUserId);
        taskUser.formatPerfCoefficient(); //格式化系数
        //Set<String> scorerTypes =  evalRule.scorerTypes(opEmpId);
        EmpEvalTaskScore2Po score2Po = new EmpEvalTaskScore2Po(taskUser, evalRule, empMap, taskBaseModel, sumScore, scoreTypes, calibration, bindEvalResult);
        for (EmpEvalTaskScore2Po.KpiTypeScore scoreType : score2Po.getScoreResult().getKpiTypes()) {
            for (EmpEvalTaskScore2Po.TaskUserKpiItemScore kpiItemScore : scoreType.getKpiItemScores()) {
                List<PerfEvaluateTaskItemDynamicPo> dynamicPos = opLogDao.listItemDynamic(tenantId.getId(), taskUserId, kpiItemScore.getKpiItemId());
                kpiItemScore.setInputChangeRecord(dynamicPos);
                if (scoreType.isAskType()) {
                    scoreType.computeAsk360TypeWeight();
                }
            }
        }

        //判断周期是按人员还是按任务
        Cycle cycle = cycleDao.find(tenantId.getId(), taskBaseModel.getCycleId());
        if (cycle.wasV1Mod()) {//1.0的使用原来数据不进行替换成周期中的评分等级组
            return score2Po;
        }
        ResultRankInstance rankInstance;
        ScoreRuleSnapRepo ruleSnapRepo = cycle.wasOnEmpMod() ? onEmpRepo : onTaskRepo;//按任务与人员模式分别进行替换成周期中的评分等级组
        List<EvalAtSnap> evalAtSnaps = ruleSnapRepo.listEvalAtSnap(tenantId, Collections.singletonList(taskUser.getId()));
        if (CollUtil.isNotEmpty(evalAtSnaps)){
            rankInstance = ruleSnapRepo.getResultRankInstance(tenantId, evalAtSnaps.get(0).getSnapId());
            score2Po.setLevelRateConf(rankInstance == null ? null : rankInstance.getLevelRateConf());
        }
        return score2Po;
    }

    public EmpEvalTaskScore3Po scoreDetailV3(TenantId tenantId, String taskUserId, String opEmpId) {
        EmpEvalMerge evalRule = evalRuleRepo.getScoreEmpEvalDetailV3(tenantId, taskUserId, opEmpId);
        EvalUser taskUser = userRepo.getBaseTaskUser(tenantId, taskUserId);
        Map<String, KpiEmp> empMap = kpiEmpDao.listByEmpAsMap(tenantId, evalRule.getKpiTypes().typeScoreIds());
        evalOkrAcl.loadOkrIf(tenantId, evalRule.okrItems(), taskUser.getEmpId());
        CycleEval taskBaseModel = taskDao.getMergeTaskBase(tenantId, taskUserId);
        PerfEvaluateTaskScoreResultDo calibration = empEvalDao.getTaskResultIndexCalibration(tenantId.getId(), taskUserId);
        BindEvalResult bindEvalResult = taskDao.getBindEvalResult(tenantId.getId(), taskUserId);
        taskUser.formatPerfCoefficient(); //格式化系数
        ListWrap<PerfEvaluateTaskItemDynamicPo> itemDynamiclistWrap = opLogDao.listItemDynamic(tenantId.getId(), taskUserId, evalRule.listKpiItemId());
        return new EmpEvalTaskScore3Po(opEmpId, taskUser, evalRule, empMap, taskBaseModel, calibration, bindEvalResult, itemDynamiclistWrap);
    }

    public List<EvalScorerTypeScoresPo> listScorerNodeScore(TenantId tenantId, String taskUserId) {
        AdminTask task = adminTaskDao.getAdminTaskMerge(tenantId, taskUserId);
        EmpEvalRule rule = evalRuleRepo.getBaseEmpEvalRule(tenantId, taskUserId);
        ScoreSortConf scoreConf = task.getScoreSortConf();
        if (Objects.nonNull(rule)){
            scoreConf = rule.getScoreSortConf();
        }
        List<EvalScorerTypeScoresPo> scorerNodeScorePos = new ArrayList<>();
        List<EmpEvalScorerNode> allScorerNodes = scorerNodeDao.listEmpEvalScorerNode(tenantId.getId(), taskUserId);
        if (CollUtil.isEmpty(allScorerNodes)) {
            return scorerNodeScorePos;
        }
        ListWrap<EmpEvalScorerNode> scorerNodeScorePoListWrap = new ListWrap<>(allScorerNodes).groupBy(EmpEvalScorerNode::getScorerType);
        for (String scoreType : scorerNodeScorePoListWrap.groupKeySet()) {
            EvalScorerTypeScoresPo scorerNodeScorePo = new EvalScorerTypeScoresPo(scoreType);
            scorerNodeScorePo.setNodeOrder(scoreConf.getSort(scoreConf.sceneOrderAtSortType(scoreType)));
            List<EmpEvalScorerNode> scorerNodes = scorerNodeScorePoListWrap.groupGet(scoreType);
            scorerNodeScorePo.acceptScorerNodes(scorerNodes);
            scorerNodeScorePos.add(scorerNodeScorePo);
        }
        //根据 nodeSort 排序 升序
        scorerNodeScorePos.sort(Comparator.comparingInt(EvalScorerTypeScoresPo::getNodeOrder));
        return scorerNodeScorePos;
    }

    //评分暂存
    @Transactional
    public void cacheScenesScore(String companyId, String opEmpId, BatchCacheScoreCmd.CmdWrap cacheCmd) {
        String taskUserId = cacheCmd.getTaskUserId();
        SubmitScoreCacheDo cacheDo = new SubmitScoreCacheDo();
        cacheDo.setCompanyId(companyId);
        cacheDo.setTaskUserId(taskUserId);
        cacheDo.setOwnerEmpId(opEmpId);
        cacheDo.setTotalLevelRs(cacheCmd.getTotalLevelRs());
        cacheDo.setItemScores(cacheCmd.getNodeScores());
        cacheDo.setPredictionScore(cacheCmd.getPredictionScore());
        cacheDo.setCreatedUser(opEmpId);
        cacheDo.setUpdatedUser(opEmpId);
        taskDao.cacheScenesScore(cacheDo);
    }

    @Async
    public void batchCacheScenesScore(BatchCacheScoreCmd batchCmd) {
        batchCmd.trace();
        for (BatchCacheScoreCmd.CmdWrap nodeScore : batchCmd.getScoreCmdWraps()) {
            self.cacheScenesScore(batchCmd.getCompanyId(), batchCmd.getOpEmpId(), nodeScore);
        }
    }

    public Set<BatchSubmitScoreCmd> splitAndMarkV1Score(String companyId, String empId, List<BatchSubmitScoreCmd.CmdWrap> cmdWraps) {
        List<String> taskUserIds = cmdWraps.stream().map(cmdWrap -> cmdWrap.getTaskUserId()).collect(Collectors.toList());
        List<BatchSubmitScoreCmd.CmdWrap> v1s = new ArrayList<>();
        List<BatchSubmitScoreCmd.CmdWrap> v2s = new ArrayList<>();
        ListWrap<BatchSubmitScoreCmd.CmdWrap> map = new ListWrap<>(cmdWraps).asMap(cmdWrap -> cmdWrap.getTaskUserId());
        List<PerfEvaluateTaskUserDo> userDos = userDao.listTaskUserByIds(companyId, taskUserIds);
        for (PerfEvaluateTaskUserDo userDo : userDos) {
            BatchSubmitScoreCmd.CmdWrap cmdWrap = map.mapGet(userDo.getId());
            if (userDo.getTempTask() == 1) {
                v1s.add(cmdWrap);
            } else {
                v2s.add(cmdWrap);
            }
        }
        Set<BatchSubmitScoreCmd> scoreCmds = new HashSet<>();
        if (CollUtil.isNotEmpty(v1s)) {
            scoreCmds.add(new BatchSubmitScoreCmd(companyId, empId, v1s));
        }
        if (CollUtil.isNotEmpty(v2s)) {
            scoreCmds.add(new BatchSubmitScoreCmd(companyId, empId, v2s));
        }
        return scoreCmds;
    }


    public List<SubmitScoreV3Cmd> buildBatchSubmitScoreCmd(String companyId, String opEmpId, List<BatchSubmitScoreCmd3.CmdWrap> cmdWraps) {
        List<String> taskUserIds = cmdWraps.stream().map(BatchSubmitScoreCmd3.CmdWrap::getTaskUserId).collect(Collectors.toList());
        //查询 当前操作人在taskUserIds任务评分关系
        ListWrap<EmpEvalScorer> evalScorerGroup = empEvalScorerDao.listEmpEvalScorerByUserIds(companyId, taskUserIds, opEmpId);
        List<SubmitScoreV3Cmd> newCmds = new ArrayList<>();
        for (BatchSubmitScoreCmd3.CmdWrap nodeScore : cmdWraps) {
            EmpEvalScorer scorer = evalScorerGroup.mapGet(nodeScore.getTaskUserId());
            SubmitScoreV3Cmd scoreV3Cmd = nodeScore.buildEmpEvalScoreNodes(companyId, opEmpId, scorer);
            if (null == scoreV3Cmd) {
                continue;
            }
            newCmds.add(scoreV3Cmd); //build
        }
        return newCmds;
    }

    @Async
    public void batchSubmitScenesScore(BatchSubmitScoreCmd batchCmd) {
        batchCmd.trace();
        List<EvalUser> evalUsers = new ArrayList<>();
        for (BatchSubmitScoreCmd.CmdWrap nodeScore : batchCmd.getScoreCmdWraps()) {
            if (CollUtil.isNotEmpty(nodeScore.getNodeScores())) {
                self.submitAnyWrap(nodeScore.getNodeScores());
            } else if (nodeScore.getTotalLevelRs() != null) {
                self.submitTotalLevel(nodeScore.getTotalLevelRs());
            }
        }
    }

    public List<EmpEvalForScoreV3Po> listEmpEvalForScoreV2(TenantId companyId, List<String> taskUserIds, String opEmpId) {
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(companyId);
        CompanyConf conf = companyDao.findCompanyConf(companyId);

        List<EmpEvalForScoreV3Po> evalForScoreV3Pos = evalMergeDao.listEmpEvalMergeForScoreV3(companyId, taskUserIds, opEmpId, fullScoreValue, conf);
        ListWrap<EmpEvalForScoreV3Po> byCycleGroups = new ListWrap<>(evalForScoreV3Pos).groupBy(EmpEvalForScoreV3Po::getCycleId);
        List<String> cycleIds = CollUtil.map(evalForScoreV3Pos, EmpEvalForScoreV3Po::getCycleId, true);
        Map<String, Cycle> cycleMap = cycleDao.listCycleAsMap(companyId.getId(), cycleIds);

        List<ResultRankInstance> instanceGroups = rankRuleSnapDao.listResultRankInstance(companyId, cycleIds);
        ListWrap<ResultRankInstance> instanceMap = new ListWrap<>(instanceGroups).asMap(ResultRankInstance::getId);

        byCycleGroups.getGroups().forEach((cycleId, evals) -> {
            Cycle cycle = cycleMap.get(cycleId);
            if (cycle.wasV1Mod()) {//1.0的使用原来数据不进行替换成周期中的评分等级组
                return;
            }
            ScoreRuleSnapRepo ruleSnapRepo = cycle.wasOnEmpMod() ? onEmpRepo : onTaskRepo;//按任务与人员模式分别进行替换成周期中的评分等级组
            List<EvalAtSnap> evalAtSnaps = ruleSnapRepo.listEvalAtSnap(companyId, taskUserIds);
            ListWrap<EvalAtSnap> snapMap = new ListWrap<>(evalAtSnaps).asMap(EvalAtSnap::getTaskUserId);
            for (EmpEvalForScoreV3Po eval : evals) {//人员所在快照
                EvalAtSnap rangeStep = snapMap.mapGet(eval.getTaskUserId());
                if (Objects.isNull(rangeStep)) {
                    continue;
                }
                ResultRankInstance rankInstance = instanceMap.mapGet(rangeStep.getSnapId());
                if (Objects.isNull(rankInstance)) {
                    continue;
                }
                eval.setScoreRanges(rankInstance.getScoreRanges());
            }
        });
        return evalForScoreV3Pos;
    }

}
