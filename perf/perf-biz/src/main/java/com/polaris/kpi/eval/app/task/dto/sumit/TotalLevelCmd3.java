package com.polaris.kpi.eval.app.task.dto.sumit;

import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.annotation.JSONField;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.BaseScoreResult;
import com.polaris.sdk.type.EmpId;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.dto.sumit
 * @Author: suxiaoqiu
 * @CreateTime: 2025-05-21  16:12
 * @Version: 2.0
 */
@Setter
@Getter
@NoArgsConstructor
public class TotalLevelCmd3 extends EmpEvalScorerNode {
    private SubScoreNodeEnum scoreNode = SubScoreNodeEnum.TOTAL_LEVEL;
    private String logScene;
    private String stepId;
    private EmpId opEmpId;
    @JSONField(serialize = false)
    private boolean isReCommit = false;//是否二次提交

    public void checkParamNullAndBuild() {
        Assert.notBlank(getTaskUserId(), "参数:taskUserId 必传");
        Assert.notEmpty(getScoreLevel(), "参数:scoreLevel 必传");
        Assert.notEmpty(stepId, "参数:stepId 必传");
        //Assert.notEmpty(getScorerId(), "参数:scorerId 必传");
    }

    public EmpId opEmpId() {
        return opEmpId;
    }

    public void submitScoreLog(TenantId companyId, String opEmpId) {
        this.checkParamNullAndBuild();
        super.setCompanyId(companyId);
        this.logScene = scoreNode.asLogScene();
        this.opEmpId = new EmpId(opEmpId);
        this.scorerId = opEmpId;
    }

}
