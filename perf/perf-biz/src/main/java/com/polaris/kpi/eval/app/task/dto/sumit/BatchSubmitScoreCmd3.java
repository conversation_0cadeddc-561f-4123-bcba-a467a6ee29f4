package com.polaris.kpi.eval.app.task.dto.sumit;

import cn.com.polaris.kpi.eval.ScoreAttUrl;
import cn.com.polaris.kpi.eval.SubScoreNodeEnum;
import cn.com.polaris.kpi.eval.TotalAttUrl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorerNode;
import com.polaris.kpi.eval.domain.task.entity.empeval.V3SubmitedEvalNodeScore;
import com.polaris.sdk.type.ListWrap;
import com.polaris.sdk.type.TenantId;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.dto.sumit
 * @Author: suxiaoqiu
 * @CreateTime: 2025-05-21  09:52
 * @Description: 批量批分
 * @Version: 2.0
 */
@Getter
@Setter
public class BatchSubmitScoreCmd3 {
    private String tid;
    private String companyId;
    private String opEmpId;
    private List<CmdWrap> scoreCmdWraps;


    public BatchSubmitScoreCmd3(String companyId, String opEmpId, List<CmdWrap> scoreCmdWraps) {
        this.companyId = companyId;
        this.opEmpId = opEmpId;
        this.scoreCmdWraps = scoreCmdWraps;
    }

    public void trace() {
        MDC.put("tid", tid);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class CmdWrap {
        private String taskUserId;
        private List<SubmitScoreCmd3> nodeScores;
        private TotalLevelCmd3 totalLevelRs;


        public CmdWrap(String taskUserId, TotalLevelCmd3 totalLevelRs) {
            this.taskUserId = taskUserId;
            this.totalLevelRs = totalLevelRs;
        }

        private boolean reCommit() {
            if (Objects.nonNull(totalLevelRs)) {
                return totalLevelRs.isReCommit();
            }
            return CollUtil.isNotEmpty(nodeScores) && nodeScores.get(0).isReCommit();
        }

        public SubmitScoreV3Cmd buildEmpEvalScoreNodes(String companyId, String opEmpId,EmpEvalScorer scorer){
            TenantId id = new TenantId(companyId);
            SubmitScoreV3Cmd cmd = new SubmitScoreV3Cmd();
            List<V3SubmitedEvalNodeScore> submitNodes = new ArrayList<>();
            boolean isReCommit = reCommit();
            List<EmpEvalScorerNode> waiSubmits;
            if (isReCommit){
                waiSubmits = scorer.listSubmitedScoreNode();//重复提交，筛选已提交的
            }else{
                waiSubmits = scorer.listWaitSubmitScoreNode(); //普通提交，筛选待提交的
            }
            if (CollUtil.isEmpty(waiSubmits)){
                return null;
            }
            V3SubmitedEvalNodeScore total =  buildTotalLevelNode(waiSubmits);//打总等级
            if (null != total){
                submitNodes.add(total);
            }

            if (CollUtil.isNotEmpty(nodeScores)) {
                ListWrap<EmpEvalScorerNode> nodeGroup = new ListWrap<>(waiSubmits).groupBy(EmpEvalScorerNode::getScorerType);
                //同一个环节
                nodeScores.forEach(cmd3 -> {
                    List<V3SubmitedEvalNodeScore> submitSameNodes = new ArrayList<>();
                    List<EmpEvalScorerNode> sameNodes = nodeGroup.groupGet(cmd3.getScorerType());
                    sameNodes.forEach(scorerNode -> {
                        scorerNode.accpScorerSubmitScoreNodeData(cmd3);
                        V3SubmitedEvalNodeScore submitNode = new V3SubmitedEvalNodeScore();
                        BeanUtil.copyProperties(scorerNode, submitNode);
                        submitNode.setNodeEnum(SubScoreNodeEnum.fromStr(scorerNode.getScorerType()));
                        submitSameNodes.add(submitNode);
                    });

                    //同时提交当前环节的多个node，只对第一个记录日志
                    for (int i = 0; i < submitSameNodes.size(); i++) {
                        V3SubmitedEvalNodeScore submitSameNode = submitSameNodes.get(i);
                        submitSameNode.setWasAddLog(i == 0);
                    }
                    submitNodes.addAll(submitSameNodes);
                });
            }
            cmd.setReCommit(isReCommit);
            cmd.setTaskUserId(taskUserId);
            cmd.setNodeScores(submitNodes);
            cmd.submitScoreLog(id, opEmpId);
            cmd.checkParamNullAndBuild();
            return cmd;
        }


        private V3SubmitedEvalNodeScore buildTotalLevelNode( List<EmpEvalScorerNode> waiSubmits){
            if (CollUtil.isEmpty(waiSubmits) || Objects.isNull(totalLevelRs)){
                return null;
            }

            EmpEvalScorerNode totalNode = null;
            for(EmpEvalScorerNode scorerNode:waiSubmits){
               if (SubScoreNodeEnum.isTotalLevelScore(scorerNode.getScorerType())){
                   totalNode = scorerNode;
                   break;
               }
            }
            if (null == totalNode){
                return null;
            }
            //totalNode 不为空,则吧提交的值赋值给totalNode
            totalNode.accpScorerSubmitTotalLevelData(totalLevelRs);
            V3SubmitedEvalNodeScore nodeScore = new V3SubmitedEvalNodeScore();
            nodeScore.setNodeEnum(SubScoreNodeEnum.TOTAL_LEVEL);
            BeanUtil.copyProperties(totalNode,nodeScore);
            nodeScore.setStepId(totalLevelRs.getStepId());
            return nodeScore;
        }

        public void checkAndBuild(String companyId, String empId) {

            //if (CollUtil.isEmpty(nodeScores)) {
            //    throw new IllegalArgumentException("nodeScores不能为空 ");
            //}

            if (StrUtil.isBlank(taskUserId)) {
                this.taskUserId = nodeScores.get(0).getTaskUserId();
            }
            if(CollUtil.isNotEmpty(nodeScores)){
                for (SubmitScoreCmd3 nodeScore : nodeScores) {
                    nodeScore.submitScoreLog(new TenantId(companyId), empId);
                    nodeScore.checkParamNullAndBuild();
                }
            }

            if (totalLevelRs != null) {
                totalLevelRs.setTaskUserId(this.getTaskUserId());
                totalLevelRs.submitScoreLog(new TenantId(companyId), empId);
                totalLevelRs.checkParamNullAndBuild();
            }
        }
    }
}
