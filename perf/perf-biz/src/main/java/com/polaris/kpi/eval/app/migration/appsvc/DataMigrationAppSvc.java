package com.polaris.kpi.eval.app.migration.appsvc;

import com.polaris.kpi.eval.app.migration.dto.MigrationRequest;
import com.polaris.kpi.eval.app.migration.dto.MigrationStatusResponse;
import com.polaris.kpi.eval.domain.task.entity.MigrationProgress;
import com.polaris.kpi.eval.domain.migration.dmsvc.BatchMigrationDmSvc;
import com.polaris.kpi.eval.domain.migration.repository.MigrationProgressRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 数据迁移应用服务 - 基于现有ScorerDataMingrationAppSvc设计优化
 * 支持大规模数据分批处理和断点续传
 *
 * 注意：这个类应该重命名为ScorerDataMingrationAppSvc或者在现有ScorerDataMingrationAppSvc中集成这些功能
 *
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataMigrationAppSvc {

    private final BatchMigrationDmSvc batchMigrationDmSvc;
    private final MigrationProgressRepository migrationProgressRepository;

    // 集成您现有的组件
    // private final ScorerDataMingrationDmSvc scorerDataMingrationDmSvc;
    // private final OnScoreEvalRepo onScoreEvalRepo;

    /**
     * 启动数据迁移任务
     * 
     * @param request 迁移请求
     * @return 迁移会话ID
     */
    @Transactional
    public String startMigration(MigrationRequest request) {
        log.info("Starting migration for type: {}, estimated records: {}", 
                request.getMigrationType(), request.getEstimatedRecords());

        // 生成会话ID
        String sessionId = generateSessionId();
        
        // 创建迁移进度实体
        MigrationProgress progress = new MigrationProgress(sessionId, request.getMigrationType());
        progress.getBatchConfig().setCompanyPageSize(request.getCompanyPageSize());
        progress.getBatchConfig().setUserPageSize(request.getUserPageSize());
        progress.getBatchConfig().setMaxRetryCount(request.getMaxRetryCount());
        progress.getBatchConfig().setBatchIntervalMs(request.getBatchIntervalMs());
        progress.getBatchConfig().setMemoryThresholdMb(request.getMemoryThresholdMb());
        progress.getBatchConfig().setAutoSaveInterval(request.getAutoSaveInterval());
        
        // 保存初始进度
        migrationProgressRepository.save(progress);
        
        // 异步执行迁移
        executeMigrationAsync(sessionId);
        
        return sessionId;
    }

    /**
     * 恢复中断的迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    @Transactional
    public boolean resumeMigration(String sessionId) {
        log.info("Attempting to resume migration: {}", sessionId);
        
        MigrationProgress progress = migrationProgressRepository.findBySessionId(sessionId);
        if (progress == null) {
            log.error("Migration progress not found for session: {}", sessionId);
            return false;
        }
        
        if (!progress.canResume()) {
            log.warn("Migration cannot be resumed, current status: {}", progress.getStatus());
            return false;
        }
        
        // 恢复迁移
        progress.resume();
        migrationProgressRepository.save(progress);
        
        // 异步继续执行
        executeMigrationAsync(sessionId);
        
        return true;
    }

    /**
     * 暂停迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    @Transactional
    public boolean pauseMigration(String sessionId) {
        log.info("Pausing migration: {}", sessionId);
        
        MigrationProgress progress = migrationProgressRepository.findBySessionId(sessionId);
        if (progress == null || !progress.isRunning()) {
            return false;
        }
        
        progress.pause();
        migrationProgressRepository.save(progress);
        
        return true;
    }

    /**
     * 获取迁移状态
     * 
     * @param sessionId 会话ID
     * @return 迁移状态响应
     */
    public MigrationStatusResponse getMigrationStatus(String sessionId) {
        MigrationProgress progress = migrationProgressRepository.findBySessionId(sessionId);
        if (progress == null) {
            return MigrationStatusResponse.notFound(sessionId);
        }
        
        return MigrationStatusResponse.builder()
                .sessionId(sessionId)
                .status(progress.getStatus().name())
                .progressPercentage(progress.getProgressPercentage())
                .processedRecords(progress.getProcessedUsers().get())
                .totalRecords(progress.getTotalUsers().get())
                .successCount(progress.getSuccessCount().get())
                .failureCount(progress.getFailureCount().get())
                .skipCount(progress.getSkipCount().get())
                .startTime(progress.getStartTime())
                .lastUpdateTime(progress.getLastUpdateTime())
                .completeTime(progress.getCompleteTime())
                .errorMessage(progress.getErrorMessage())
                .currentPosition(String.format("Company: %s, Page: %d/%d", 
                        progress.getCurrentCompanyId(), 
                        progress.getCurrentCompanyPage(),
                        progress.getCurrentUserPage()))
                .statistics(progress.getStatistics())
                .build();
    }

    /**
     * 异步执行迁移任务
     * 
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public CompletableFuture<Void> executeMigrationAsync(String sessionId) {
        try {
            log.info("Starting async migration execution for session: {}", sessionId);
            
            MigrationProgress progress = migrationProgressRepository.findBySessionId(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return CompletableFuture.completedFuture(null);
            }
            
            // 开始迁移
            if (progress.getStatus() == MigrationProgress.MigrationStatus.PENDING) {
                progress.start();
                migrationProgressRepository.save(progress);
            }
            
            // 执行批量迁移
            batchMigrationDmSvc.executeBatchMigration(progress);
            
            log.info("Migration completed successfully for session: {}", sessionId);
            
        } catch (Exception e) {
            log.error("Migration failed for session: {}", sessionId, e);
            handleMigrationFailure(sessionId, e.getMessage());
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理迁移失败
     * 
     * @param sessionId 会话ID
     * @param errorMessage 错误信息
     */
    private void handleMigrationFailure(String sessionId, String errorMessage) {
        try {
            MigrationProgress progress = migrationProgressRepository.findBySessionId(sessionId);
            if (progress != null) {
                progress.fail(errorMessage);
                migrationProgressRepository.save(progress);
            }
        } catch (Exception e) {
            log.error("Failed to update migration failure status for session: {}", sessionId, e);
        }
    }

    /**
     * 生成会话ID
     * 
     * @return 会话ID
     */
    private String generateSessionId() {
        return "MIGRATION_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 获取所有活跃的迁移任务
     * 
     * @return 活跃任务列表
     */
    public java.util.List<MigrationStatusResponse> getActiveMigrations() {
        return migrationProgressRepository.findActiveMigrations()
                .stream()
                .map(progress -> getMigrationStatus(progress.getSessionId()))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 清理已完成的迁移记录
     * 
     * @param daysToKeep 保留天数
     * @return 清理的记录数
     */
    @Transactional
    public int cleanupCompletedMigrations(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        return migrationProgressRepository.deleteCompletedBefore(cutoffTime);
    }
}
