package com.polaris.kpi.eval.app.migration.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据迁移请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrationRequest {

    /**
     * 迁移类型（FINISHED/NO_FINISHED）
     */
    @NotBlank(message = "迁移类型不能为空")
    private String migrationType;

    /**
     * 预估记录数量
     */
    @Min(value = 1, message = "预估记录数量必须大于0")
    private Long estimatedRecords;

    /**
     * 公司分页大小
     */
    @Builder.Default
    @Min(value = 1, message = "公司分页大小必须大于0")
    private Integer companyPageSize = 10;

    /**
     * 用户分页大小
     */
    @Builder.Default
    @Min(value = 1, message = "用户分页大小必须大于0")
    private Integer userPageSize = 500;

    /**
     * 最大重试次数
     */
    @Builder.Default
    @Min(value = 0, message = "最大重试次数不能为负数")
    private Integer maxRetryCount = 3;

    /**
     * 批次处理间隔（毫秒）
     */
    @Builder.Default
    @Min(value = 0, message = "批次处理间隔不能为负数")
    private Long batchIntervalMs = 100L;

    /**
     * 内存阈值（MB）
     */
    @Builder.Default
    @Min(value = 50, message = "内存阈值不能小于50MB")
    private Integer memoryThresholdMb = 200;

    /**
     * 自动保存进度间隔（处理多少条记录后保存一次）
     */
    @Builder.Default
    @Min(value = 1, message = "自动保存间隔必须大于0")
    private Integer autoSaveInterval = 100;

    /**
     * 是否启用内存监控
     */
    @Builder.Default
    private Boolean enableMemoryMonitoring = true;

    /**
     * 是否强制重新开始（忽略已存在的进度）
     */
    @Builder.Default
    private Boolean forceRestart = false;

    /**
     * 迁移描述
     */
    private String description;

    /**
     * 操作人员ID
     */
    @NotBlank(message = "操作人员ID不能为空")
    private String operatorId;

    /**
     * 操作人员姓名
     */
    private String operatorName;

    /**
     * 创建默认的迁移请求
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 迁移请求
     */
    public static MigrationRequest createDefault(String migrationType, String operatorId) {
        return MigrationRequest.builder()
                .migrationType(migrationType)
                .operatorId(operatorId)
                .estimatedRecords(1310000L) // 131万条记录
                .companyPageSize(10)
                .userPageSize(1000) // 针对大数据量优化
                .maxRetryCount(3)
                .batchIntervalMs(50L) // 减少间隔提高效率
                .memoryThresholdMb(300) // 根据内存计算调整
                .autoSaveInterval(500) // 每500条保存一次
                .enableMemoryMonitoring(true)
                .forceRestart(false)
                .description("大规模数据迁移任务")
                .build();
    }

    /**
     * 创建高性能迁移请求（适用于内存充足的环境）
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 迁移请求
     */
    public static MigrationRequest createHighPerformance(String migrationType, String operatorId) {
        return MigrationRequest.builder()
                .migrationType(migrationType)
                .operatorId(operatorId)
                .estimatedRecords(1310000L)
                .companyPageSize(20)
                .userPageSize(2000) // 更大的批次
                .maxRetryCount(3)
                .batchIntervalMs(10L) // 更短的间隔
                .memoryThresholdMb(500) // 更高的内存阈值
                .autoSaveInterval(1000) // 更大的保存间隔
                .enableMemoryMonitoring(true)
                .forceRestart(false)
                .description("高性能数据迁移任务")
                .build();
    }

    /**
     * 创建保守迁移请求（适用于内存受限的环境）
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 迁移请求
     */
    public static MigrationRequest createConservative(String migrationType, String operatorId) {
        return MigrationRequest.builder()
                .migrationType(migrationType)
                .operatorId(operatorId)
                .estimatedRecords(1310000L)
                .companyPageSize(5)
                .userPageSize(200) // 更小的批次
                .maxRetryCount(5)
                .batchIntervalMs(200L) // 更长的间隔
                .memoryThresholdMb(100) // 更低的内存阈值
                .autoSaveInterval(50) // 更频繁的保存
                .enableMemoryMonitoring(true)
                .forceRestart(false)
                .description("保守数据迁移任务")
                .build();
    }

    /**
     * 验证请求参数
     * 
     * @return 验证结果
     */
    public boolean isValid() {
        return migrationType != null && !migrationType.trim().isEmpty()
                && operatorId != null && !operatorId.trim().isEmpty()
                && estimatedRecords != null && estimatedRecords > 0
                && companyPageSize != null && companyPageSize > 0
                && userPageSize != null && userPageSize > 0
                && maxRetryCount != null && maxRetryCount >= 0
                && batchIntervalMs != null && batchIntervalMs >= 0
                && memoryThresholdMb != null && memoryThresholdMb >= 50
                && autoSaveInterval != null && autoSaveInterval > 0;
    }

    /**
     * 获取预估的批次数量
     * 
     * @return 批次数量
     */
    public long getEstimatedBatchCount() {
        if (estimatedRecords == null || userPageSize == null || userPageSize == 0) {
            return 0;
        }
        return (long) Math.ceil((double) estimatedRecords / userPageSize);
    }

    /**
     * 获取预估的处理时间（分钟）
     * 基于批次数量和间隔时间计算
     * 
     * @return 预估处理时间（分钟）
     */
    public long getEstimatedProcessingTimeMinutes() {
        long batchCount = getEstimatedBatchCount();
        if (batchCount == 0 || batchIntervalMs == null) {
            return 0;
        }
        
        // 假设每批次处理时间为间隔时间的10倍（包含实际处理时间）
        long totalTimeMs = batchCount * (batchIntervalMs * 10);
        return totalTimeMs / (1000 * 60); // 转换为分钟
    }
}
