package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.com.polaris.kpi.eval.ConcurrentBitMap;
import cn.com.polaris.kpi.eval.RecordIndexCalculator;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.EvalUser;
import com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.task.entity.TaskBitmapWithLog;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.eval.infr.task.repimpl.EmpEvalRuleRepoImpl;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.TenantId;
import jdk.nashorn.internal.objects.annotations.Setter;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-03  15:25
 * @Description: 评分人数据迁移服务
 * @Version: 2.0
 */
@Service
@Slf4j
public class ScorerDataMingrationAppSvc {

    /**
     * 传统位图处理方式（已废弃）
     * @deprecated 建议使用新的OptimizedBitmapProgress + ConcurrentBitmapManager方案
     * 新方案提供：
     * 1. 内存使用从200MB降到50KB（99.9%节省）
     * 2. 精确到记录级别的断点恢复
     * 3. 并发安全的分段处理
     * 4. 完整的失败记录跟踪和重试机制
     * 5. 分离式任务执行（FINISHED和NO_FINISHED独立处理）
     */
    @Deprecated
    @Autowired
    private TaskBitmapWithLog bitmapWithLog;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private EmpEvalRuleRepoImpl empRuleRepo;
    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserRepo userRepo;
    @Autowired
    private TaskUserDao taskUserDao;

    // 新增：优化位图管理组件
    @Autowired(required = false)
    private ConcurrentBitMap bitMap;
    @Autowired(required = false)
    private RecordIndexCalculator indexCalculator;


    private static final Integer VERSION = 2000000;

    /**
     * 是否启用位图优化（默认开启）
     */
    @Value("${scorer.migration.use-bitmap:true}")
    private boolean useBitmapOptimization;

    /**
     * 是否启用增量检测（默认开启）
     */
    @Value("${scorer.migration.incremental.enabled:true}")
    private boolean enableIncrementalDetection;

    /**
     * 增量检测间隔（分钟）
     */
    @Value("${scorer.migration.incremental.check-interval-minutes:30}")
    private long incrementalCheckIntervalMinutes;

    /**
     * 是否启用任务分离（默认开启）
     */
    @Value("${scorer.migration.task-separation.enabled:true}")
    private boolean enableTaskSeparation;

    /**
     * 是否启用动态扩展（默认开启）
     */
    @Value("${scorer.migration.dynamic-expansion.enabled:true}")
    private boolean enableDynamicExpansion;

    /**
     * 动态检查间隔（分钟）
     */
    @Value("${scorer.migration.dynamic-expansion.check-interval-minutes:15}")
    private long dynamicCheckIntervalMinutes;

    private static final String TASK_BITMAP_FILE_PATH = "task_bitmap_with_log.ser";

    @PostConstruct
    public void init() {
        // 优先使用新的优化方案，传统方案作为后备
        if (useBitmapOptimization) {
            log.info("Using optimized bitmap migration approach");
            // 检查并初始化必要的组件
            initializeOptimizedComponents();
        } else {
            log.info("Using traditional TaskBitmapWithLog approach");
            // 从文件中加载任务状态信息（传统方案）
            loadTaskStatusFromFile();
        }
    }

    /**
     * 初始化优化迁移所需的组件
     */
    private void initializeOptimizedComponents() {
        try {
            // 如果组件为null，尝试手动初始化
            if (bitMap == null) {
                log.warn("BitmapManager is null, attempting manual initialization");
                bitMap = new cn.com.polaris.kpi.eval.ConcurrentBitMap();
                log.info("Successfully initialized ConcurrentBitMap manually");
            }

            if (indexCalculator == null) {
                log.warn("IndexCalculator is null, attempting manual initialization");
                indexCalculator = new cn.com.polaris.kpi.eval.RecordIndexCalculator();
                log.info("Successfully initialized RecordIndexCalculator manually");
            }

            log.info("Optimized migration components initialized successfully: bitmapManager={}, indexCalculator={}",
                    bitMap != null, indexCalculator != null);

        } catch (Exception e) {
            log.error("Failed to initialize optimized migration components", e);
            // 如果初始化失败，禁用位图优化
            useBitmapOptimization = false;
            log.warn("Disabled bitmap optimization due to component initialization failure");
        }
    }
    /**
     * 加载任务状态从文件（传统方案）
     * @deprecated 建议使用新的OptimizedBitmapProgress方案，此方法将在未来版本中移除
     */
    @Deprecated
    private void loadTaskStatusFromFile() {
        File file = new File(TASK_BITMAP_FILE_PATH);
        if (file.exists()) {
            try {
                bitmapWithLog = TaskBitmapWithLog.loadFromFile(TASK_BITMAP_FILE_PATH);
                log.info("[DEPRECATED] Loaded task status from file: {}", TASK_BITMAP_FILE_PATH);
            } catch (IOException | ClassNotFoundException e) {
                log.error("Failed to load task status from file: {}", TASK_BITMAP_FILE_PATH, e);
                // 初始化一个新的 TaskBitmapWithLog 对象
                bitmapWithLog = new TaskBitmapWithLog(0, 0);
            }
        } else {
            log.info("[DEPRECATED] Task status file not found: {}. Initializing a new TaskBitmapWithLog object.", TASK_BITMAP_FILE_PATH);
            bitmapWithLog = new TaskBitmapWithLog(0, 0);
        }
    }

    /**
     * 迁移已完成的考核数据（传统方案）
     * @deprecated 建议使用 startOptimizedMigration() 方法，此方法将在未来版本中移除
     */
    @Deprecated
    public void migrateFinished(String tid,String companyId, String taskUserId) {
        MDC.put("tid", tid);
        //已完成考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskFinished(); // 处理已完成的，rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())){
                // 更新 TaskBitmapWithLog 的状态为已完成但失败
                saveTaskFinishedFailure(tenantId,taskUserId);
                log.warn("Task finished with failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            // 更新 TaskBitmapWithLog 的状态为已完成且成功
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.info("Task finished successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            // 更新 TaskBitmapWithLog 的状态为已完成但失败
            saveTaskFinishedFailure(tenantId, taskUserId);
            log.error("Task finished with failure for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }

    // 任务ID生成方法（关键：保证全局唯一性）
    private String generateTaskId(String companyId, String taskUserId) {
        return companyId + "_" + taskUserId + "_" + System.currentTimeMillis();
    }
    @Transactional
    public void migrateFinishedOne(String companyId, String taskUserId) {
        //已完成考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        dmSvc.handlerTaskFinished(); // 处理已完成的，rs数据合并+计算
        if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
            // 更新 TaskBitmapWithLog 的状态为已完成但失败
            log.warn("migrateFinishedOne  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }

        List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
        Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId), scorerIds);
        dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
        tx.runTran(() -> {
            empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
            onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
            empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
        });
        log.info("  migrateFinishedOneTask successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
    }

    @Transactional
    public void migrateNoFinishOne(String companyId, String taskUserId) {
        //未完成的考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskNoFinished(); // 处理未完成的，解析+rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
                log.warn("Task no finished  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
            Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId), scorerIds);
            dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            log.info("Task no finished for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            log.error("Task  no finished for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }

    private void saveTaskFinishedFailure( TenantId tenantId, String taskUserId) {
        // 更新 TaskBitmapWithLog 的状态为已完成但失败
        bitmapWithLog.setTaskFinishedFailure(tenantId, taskUserId);
        // 持久化任务状态到文件
        saveTaskStatusToFile();
    }

    /**
     * 迁移未完成的考核数据（传统方案）
     * @deprecated 建议使用 startOptimizedMigration() 方法，此方法将在未来版本中移除
     */
    @Deprecated
    public void migrateNoFinish(String tid, String companyId, String taskUserId) {
        MDC.put("tid", tid);
        //未完成的考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskNoFinished(); // 处理未完成的，解析+rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
                // 更新 TaskBitmapWithLog 的状态为已完成但失败
                log.warn("Task no finished  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
            Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId),scorerIds);
            dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            // 记录 TaskBitmapWithLog 的状态为已完成
            bitmapWithLog.setTaskNoFinishedSuccess(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.info("Task no finished for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            // 记录 TaskBitmapWithLog 的状态为未执行成功
            bitmapWithLog.setTaskNoFinishedFailure(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.error("Task  no finished for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }
    /**
     * 保存任务状态到文件（传统方案）
     * @deprecated 建议使用OptimizedBitmapProgress的saveMetadata()方法
     */
    @Deprecated
    private void saveTaskStatusToFile() {
        try {
            bitmapWithLog.saveToFile(TASK_BITMAP_FILE_PATH);
            log.info("[DEPRECATED] Saved task status to file: {}", TASK_BITMAP_FILE_PATH);
        } catch (IOException e) {
            log.error("Failed to save task status to file: {}", TASK_BITMAP_FILE_PATH, e);
        }
    }

    /**
     * 记录任务状态（传统方案）
     * @deprecated 建议使用 getOptimizedMigrationStatistics() 方法
     */
    @Deprecated
    public void logTaskStatus() {
        log.info("[DEPRECATED] Finished success tasks count: {}", bitmapWithLog.getFinishedSuccessTaskCount());
        log.info("[DEPRECATED] Finished failure tasks count: {}", bitmapWithLog.getFinishedFailureTaskCount());
        log.info("[DEPRECATED] No finished tasks count: {}", bitmapWithLog.getNoFinishedTaskCount());
        log.info("[DEPRECATED] No finished failure tasks count: {}", bitmapWithLog.getNoFinishedFailureTaskCount());
        List<String> noFinishedFailurTasks = bitmapWithLog.getNoFinishedFailureTasks();
        if (!noFinishedFailurTasks.isEmpty()) {
            log.info("[DEPRECATED] Not Finished tasks: {}", noFinishedFailurTasks);
        }
        List<String> finishedFailureTasks = bitmapWithLog.getFinishedFailureTasks();
        if (!finishedFailureTasks.isEmpty()) {
            log.info("[DEPRECATED] Finished failure tasks: {}", finishedFailureTasks);
        }
    }

    // ==================== 新增的优化方法 ====================

    /**
     * 启动优化的大规模数据迁移
     * 支持131万条记录的分批处理和断点续传
     *
     * @param migrationType 迁移类型（FINISHED/NO_FINISHED）
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startOptimizedMigration( String tid,String migrationType, String operatorId) {
        log.info("Starting optimized migration: tid:{},type={}, operator={}, useBitmap={}, taskSeparation={}",
                tid,migrationType, operatorId, useBitmapOptimization, enableTaskSeparation);
        MDC.put("tid", tid);
        if (!useBitmapOptimization || bitMap == null || indexCalculator == null) {
            log.warn("Bitmap optimization not enabled or components not available, falling back to original method");
            return startOriginalMigration(migrationType, operatorId);
        }

        try {
            // 检查是否启用任务分离
            if (enableTaskSeparation) {
                return startSeparatedTasks(tid,migrationType, operatorId);
            } else {
                return startSingleTask(tid,migrationType, operatorId);
            }

        } catch (Exception e) {
            log.error("Failed to start optimized migration: type={}, operator={}", migrationType, operatorId, e);
            throw new RuntimeException("启动优化迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启动分离式任务（推荐方式）
     * 将FINISHED和NO_FINISHED分为两个独立任务
     *
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 主任务会话ID
     */
    public String startSeparatedTasks(String tid,String migrationType, String operatorId) {
        log.info("Starting separated tasks: type={}, operator={}", migrationType, operatorId);
        MDC.put("tid", tid);
        try {
            // 生成两个任务的会话ID
            String finishedSessionId = generateOptimizedSessionId("FINISHED");
            String noFinishedSessionId = generateOptimizedSessionId("NO_FINISHED");

            // 创建FINISHED任务
            OptimizedBitmapProgress finishedProgress = new OptimizedBitmapProgress(finishedSessionId, "FINISHED");
            finishedProgress.enableTaskSeparation(OptimizedBitmapProgress.TaskType.FINISHED_ONLY, noFinishedSessionId);

            // 创建NO_FINISHED任务
            OptimizedBitmapProgress noFinishedProgress = new OptimizedBitmapProgress(noFinishedSessionId, "NO_FINISHED");
            noFinishedProgress.enableTaskSeparation(OptimizedBitmapProgress.TaskType.NO_FINISHED_ONLY, finishedSessionId);

            // 启用增量检测
            if (enableIncrementalDetection) {
                finishedProgress.enableIncrementalDetection(incrementalCheckIntervalMinutes);
                noFinishedProgress.enableIncrementalDetection(incrementalCheckIntervalMinutes);
                log.info("Enabled incremental detection for separated tasks: interval={} minutes", incrementalCheckIntervalMinutes);
            }

            // 启用动态扩展
            if (enableDynamicExpansion) {
                finishedProgress.enableDynamicExpansion(dynamicCheckIntervalMinutes);
                noFinishedProgress.enableDynamicExpansion(dynamicCheckIntervalMinutes);
                log.info("Enabled dynamic expansion for separated tasks: interval={} minutes", dynamicCheckIntervalMinutes);
            }

            // 初始化数据量
            initializeOptimizedMigrationCounts(finishedProgress);
            initializeOptimizedMigrationCounts(noFinishedProgress);

            // 保存元数据
            finishedProgress.saveMetadata();
            noFinishedProgress.saveMetadata();

            // 异步执行两个任务
            executeOptimizedMigrationAsync(tid,finishedSessionId);
            executeOptimizedMigrationAsync(tid,noFinishedSessionId);

            // 根据原始请求类型返回对应的会话ID
            String primarySessionId = "FINISHED".equals(migrationType) ? finishedSessionId : noFinishedSessionId;

            log.info("Separated tasks started successfully: finishedSession={}, noFinishedSession={}, primary={}",
                    finishedSessionId, noFinishedSessionId, primarySessionId);

            return primarySessionId;

        } catch (Exception e) {
            log.error("Failed to start separated tasks: type={}, operator={}", migrationType, operatorId, e);
            throw new RuntimeException("启动分离式任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启动单一任务（传统方式）
     *
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    private String startSingleTask(String tid,String migrationType, String operatorId) {
        MDC.put("tid", tid);
        // 生成会话ID
        String sessionId = generateOptimizedSessionId();

        // 创建优化的位图进度管理器
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);

        // 启用增量检测（如果配置了）
        if (enableIncrementalDetection) {
            progress.enableIncrementalDetection(incrementalCheckIntervalMinutes);
            log.info("Enabled incremental detection for session: {}, interval: {} minutes",
                    sessionId, incrementalCheckIntervalMinutes);
        }

        // 初始化总数据量
        initializeOptimizedMigrationCounts(progress);

        // 保存初始进度
        progress.saveMetadata();

        // 异步执行迁移
        executeOptimizedMigrationAsync(tid,sessionId);

        log.info("Single task started successfully: sessionId={}", sessionId);
        return sessionId;
    }

    /**
     * 恢复中断的优化迁移任务
     *
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeOptimizedMigration(String tid,String sessionId) {
        log.info("Attempting to resume optimized migration: {}", sessionId);

        if (!useBitmapOptimization || bitMap == null) {
            log.warn("Bitmap optimization not enabled, cannot resume optimized migration");
            return false;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return false;
            }

            if (!progress.canResume()) {
                log.warn("Migration cannot be resumed, current status: {}", progress.getStatus());
                return false;
            }

            // 恢复迁移
            progress.resume();

            // 异步继续执行
            executeOptimizedMigrationAsync(tid,sessionId);

            log.info("Optimized migration resumed successfully: {}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("Failed to resume optimized migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 暂停优化迁移任务
     *
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseOptimizedMigration(String sessionId) {
        log.info("Pausing optimized migration: {}", sessionId);

        if (!useBitmapOptimization) {
            log.warn("Bitmap optimization not enabled, cannot pause optimized migration");
            return false;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null || !progress.isRunning()) {
                return false;
            }

            progress.pause();
            log.info("Optimized migration paused successfully: {}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("Failed to pause optimized migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取优化迁移状态
     *
     * @param sessionId 会话ID
     * @return 迁移状态
     */
    public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId) {
        if (!useBitmapOptimization) {
            return null;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                log.debug("Optimized migration status: {}", progress.getStatistics());
            }
            return progress;

        } catch (Exception e) {
            log.error("Failed to get optimized migration status: {}", sessionId, e);
            return null;
        }
    }

    /**
     * 异步执行优化的迁移任务
     *
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public void executeOptimizedMigrationAsync(String tid,String sessionId) {
        MDC.put("tid", tid);
        try {
            log.info("Starting async optimized migration execution for session: {}", sessionId);

            // 增加重试机制，因为异步执行可能在保存完成前开始
            OptimizedBitmapProgress progress = loadMetadataWithRetry(sessionId, 3, 1000);
            if (progress == null) {
                log.error("Migration progress not found for session after retries: {}", sessionId);
                return;
            }

            // 开始迁移
            if (progress.getStatus() == OptimizedBitmapProgress.MigrationStatus.PENDING) {
                progress.start();
                progress.saveMetadata();
            }

            // 执行优化的迁移逻辑
            executeOptimizedMigrationLogic(progress);

            log.info("Optimized migration completed successfully for session: {}", sessionId);

        } catch (Exception e) {
            log.error("Optimized migration failed for session: {}", sessionId, e);
            handleOptimizedMigrationFailure(sessionId, e.getMessage());
        } finally {
            MDC.remove("tid");
        }
    }

    /**
     * 带重试机制的元数据加载
     *
     * @param sessionId 会话ID
     * @param maxRetries 最大重试次数
     * @param retryIntervalMs 重试间隔（毫秒）
     * @return 进度对象
     */
    private OptimizedBitmapProgress loadMetadataWithRetry(String sessionId, int maxRetries, long retryIntervalMs) {
        for (int i = 0; i <= maxRetries; i++) {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                log.info("Successfully loaded metadata for session: {} on attempt {}", sessionId, i + 1);
                return progress;
            }

            if (i < maxRetries) {
                log.warn("Failed to load metadata for session: {} on attempt {}, retrying in {}ms",
                        sessionId, i + 1, retryIntervalMs);
                try {
                    Thread.sleep(retryIntervalMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Interrupted while waiting to retry metadata loading");
                    break;
                }
            }
        }

        log.error("Failed to load metadata for session: {} after {} attempts", sessionId, maxRetries + 1);
        return null;
    }

    /**
     * 测试页码递增逻辑（调试用）
     */
    public void testCompanyPageIncrement(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Progress not found for sessionId: {}", sessionId);
                return;
            }

            log.info("🧪 测试页码递增 - 初始页码: {}", progress.getCurrentCompanyPage());

            for (int i = 1; i <= 5; i++) {
                int currentPage = progress.getCurrentCompanyPage();
                int nextPage = currentPage + 1;

                log.info("🧪 第{}次测试: {} -> {}", i, currentPage, nextPage);
                progress.updatePosition(null, nextPage, 1);
                progress.saveMetadata();

                // 重新加载验证
                OptimizedBitmapProgress reloaded = OptimizedBitmapProgress.loadMetadata(sessionId);
                log.info("🧪 保存后重新加载: {}", reloaded.getCurrentCompanyPage());
            }

        } catch (Exception e) {
            log.error("测试页码递增失败", e);
        }
    }

    /**
     * 启动原有的迁移方法（保持向后兼容）
     */
    private String startOriginalMigration(String migrationType, String operatorId) {
        // 这里可以调用您原有的迁移逻辑
        log.info("Starting original migration: type={}, operator={}", migrationType, operatorId);
        return generateOptimizedSessionId();
    }

    /**
     * 生成优化的会话ID
     */
    private String generateOptimizedSessionId() {
        return "OPTIMIZED_SCORER_MIGRATION_" + System.currentTimeMillis() + "_" +
               java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 生成优化的会话ID（带类型）
     */
    private String generateOptimizedSessionId(String type) {
        return "OPTIMIZED_" + type + "_MIGRATION_" + System.currentTimeMillis() + "_" +
               java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 初始化优化迁移数据量
     */
    private void initializeOptimizedMigrationCounts(OptimizedBitmapProgress progress) {
        try {
            String migrationType = progress.getMigrationType();

            // 使用估算的数据量（您可以根据实际情况调整）
            long totalRecords = estimateTotalRecords(migrationType);
            progress.getTotalRecords().set(totalRecords);

            log.info("Initialized optimized migration counts for session: {}, type: {}, total: {}",
                    progress.getSessionId(), migrationType, totalRecords);

        } catch (Exception e) {
            log.error("Failed to initialize optimized migration counts for session: {}", progress.getSessionId(), e);
            throw new RuntimeException("初始化优化迁移数据量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 估算总记录数（基于您的业务逻辑）
     */
    private long estimateTotalRecords(String migrationType) {
        // 您可以根据实际业务情况调整这些估算值
        if ("FINISHED".equals(migrationType)) {
            return 1310000L; // 131万条记录
        } else if ("NO_FINISHED".equals(migrationType)) {
            return 500000L; // 50万条记录
        }
        return 100000L; // 默认10万条记录
    }

    /**
     * 模拟分页获取公司ID列表
     * TODO: 请根据您的实际业务逻辑实现此方法
     */
    private List<String> getCompanyIdsByPageSimulated(int page, int pageSize) {
        // 这是一个模拟实现，您需要根据实际业务逻辑替换
        List<String> companyIds = new ArrayList<>();
        //分页查询V2所有公司列表
        PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, page, pageSize);
        for (Company company : companies) {
            companyIds.add(company.getId());
        }

        log.debug("Simulated company IDs for page:{},pageSize:{}, companyIds.size:{}", page,pageSize, companyIds.size());
        return companyIds;
    }

    /**
     * 模拟分页获取用户ID列表
     * TODO: 请根据您的实际业务逻辑实现此方法
     */
    private List<String> getUserIdsByPageSimulated(String migrationType, String companyId, int page, int pageSize) {
        // 这是一个模拟实现，您需要根据实际业务逻辑替换
        boolean finished = StrUtil.equals("FINISHED", migrationType);
        PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, null, finished, page, pageSize);
        log.debug("Simulated user IDs for,migrationType:{}, company {} page {}: {}", migrationType, companyId, page, taskUserIds.size());
        return taskUserIds.getData();
    }

    /**
     * 处理优化迁移失败
     */
    private void handleOptimizedMigrationFailure(String sessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                progress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to update optimized migration failure status for session: {}", sessionId, e);
        }
    }

    /**
     * 执行优化的迁移逻辑（核心方法）
     */
    private void executeOptimizedMigrationLogic(OptimizedBitmapProgress progress) {
        String sessionId = progress.getSessionId();

        // 配置参数
        int companyPageSize = 10;
        int userPageSize = 1000;

        // 从断点位置开始处理
        int startCompanyPage = progress.getCurrentCompanyPage();

        log.info("Resuming optimized migration from company page: {}", startCompanyPage);

        for (int companyPage = startCompanyPage; ; companyPage++) {
            // 🔍 调试日志：跟踪companyPage变化
            log.debug("🔍 Processing company page: {}, startCompanyPage: {}, progress.currentCompanyPage: {}",
                     companyPage, startCompanyPage, progress.getCurrentCompanyPage());

            // 检查是否需要暂停
            if (shouldPauseOptimized(progress)) {
                log.info("Optimized migration paused at company page: {}", companyPage);
                break;
            }

            // 检查是否需要进行增量检测
            if (progress.shouldPerformIncrementalCheck()) {
                log.info("Performing incremental detection check at company page: {}", companyPage);
                performIncrementalDetection(progress);
                progress.updateLastIncrementalCheckTime();
            }

            // 检查是否需要进行动态扩展检查
            if (progress.shouldPerformDynamicCheck()) {
                log.info("Performing dynamic expansion check at company page: {}", companyPage);
                performDynamicExpansionCheck(progress);
                progress.updateLastDynamicCheckTime();
            }

            // 使用模拟的公司列表（您可以根据实际情况实现）
            List<String> companyIds = getCompanyIdsByPageSimulated(companyPage, companyPageSize);
            log.info("🔍 Retrieved {} companies for page: {}, pageSize: {}", companyIds.size(), companyPage, companyPageSize);

            if (companyIds.isEmpty()) {
                log.info("No more companies to process, breaking at page: {}", companyPage);
                break;
            }

            // 处理每个公司
            for (String companyId : companyIds) {
                processCompanyWithOptimizedBitmap(progress, companyId, companyPage, userPageSize);
                // 🔧 修复：处理完一个公司后，重置用户页为1（准备处理下一个公司）
                progress.updatePosition(companyId, companyPage, 1);
                log.debug("🔍 Completed company: {}, reset userPage to 1", companyId);
            }

            // 🔧 修复：更新当前页码到下一页，重置用户页为1（准备处理下一页公司）
            int nextPage = companyPage + 1;
            log.info("🔍 Updating progress: currentPage {} -> nextPage {}, reset userPage to 1", companyPage, nextPage);
            progress.updatePosition(null, nextPage, 1);

            // 🔧 修复：立即保存进度，确保页码更新被持久化
            progress.saveMetadata();
            log.debug("🔍 Progress saved: currentCompanyPage = {}", progress.getCurrentCompanyPage());

            // 定期保存进度
            if (companyPage % 5 == 0) {
                progress.saveMetadata();
                bitMap.flushAllSegments(sessionId);
                log.info("Optimized progress saved at company page: {}", companyPage);
            }
        }

        // 完成迁移
        if (isOptimizedAllProcessed(progress)) {
            progress.complete();
            log.info("Optimized migration completed successfully for session: {}", progress.getSessionId());
        }
    }

    /**
     * 使用优化位图处理单个公司
     */
    private void processCompanyWithOptimizedBitmap(OptimizedBitmapProgress progress, String companyId, int currentCompanyPage, int userPageSize) {
        log.debug("Processing company with optimized bitmap: companyId={}, companyPage={}", companyId, currentCompanyPage);

        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();

        // 确定起始用户页
        int startUserPage = companyId.equals(progress.getCurrentCompanyId()) ?
                progress.getCurrentUserPage() : 1;

        for (int userPage = startUserPage; ; userPage++) {
            // 使用模拟的用户列表（您可以根据实际情况实现）
            List<String> userIds = getUserIdsByPageSimulated(migrationType, companyId, userPage, userPageSize);
            if (userIds.isEmpty()) {
                log.debug("No more users for company: {}, page: {}", companyId, userPage);
                break;
            }

            // 位图检查：过滤已处理的用户
            List<String> usersToProcess = new ArrayList<>();
            List<Long> indexesToProcess = new ArrayList<>();

            for (int i = 0; i < userIds.size(); i++) {
                String userId = userIds.get(i);

                // 计算全局索引
                long globalIndex = indexCalculator.calculateGlobalIndex(
                        companyId, currentCompanyPage, userPage, i, 10, userPageSize);

                // 位图检查：如果已处理则跳过
                if (!bitMap.getBit(sessionId, globalIndex, progress.getSegmentSize())) {
                    usersToProcess.add(userId);
                    indexesToProcess.add(globalIndex);
                } else {
                    log.debug("User already processed, skipping: companyId={}, userId={}, index={}",
                            companyId, userId, globalIndex);
                }
            }

            if (!usersToProcess.isEmpty()) {
                // 批量处理用户
                processOptimizedBatchUsers(progress, companyId, usersToProcess, indexesToProcess, migrationType);
            } else {
                log.debug("All users in this page already processed: companyId={}, page={}", companyId, userPage);
            }

            // 🔧 修复：更新用户页位置，使用正确的公司页码
            progress.updatePosition(companyId, currentCompanyPage, userPage + 1);
            log.debug("🔍 Updated userPage: companyId={}, companyPage={}, userPage={}", companyId, currentCompanyPage, userPage + 1);

            // 定期保存进度
            if (userPage % 10 == 0) {
                progress.saveMetadata();
            }
        }
    }

    /**
     * 批量处理用户（优化版本）
     */
    private void processOptimizedBatchUsers(OptimizedBitmapProgress progress, String companyId,
                                          List<String> userIds, List<Long> indexes, String migrationType) {

        String sessionId = progress.getSessionId();

        try {
            log.debug("Processing optimized batch users: companyId={}, count={}, type={}",
                    companyId, userIds.size(), migrationType);

            // 尝试批量处理
            boolean batchSuccess = false;

            if ("FINISHED".equals(migrationType)) {
                batchSuccess = processOptimizedBatchFinishedUsers(companyId, userIds);
            } else if ("NO_FINISHED".equals(migrationType)) {
                batchSuccess = processOptimizedBatchNoFinishedUsers(companyId, userIds);
            }

            if (batchSuccess) {
                // 批量成功：更新所有位图
                for (int i = 0; i < indexes.size(); i++) {
                    Long globalIndex = indexes.get(i);
                    bitMap.setBit(sessionId, globalIndex, progress.getSegmentSize());
                    progress.markProcessed(globalIndex, true);
                }

                log.info("Successfully processed optimized batch users: companyId={}, count={}", companyId, userIds.size());

            } else {
                // 批量失败：逐个处理
                log.warn("Optimized batch processing failed, falling back to individual processing: companyId={}", companyId);
                processOptimizedIndividualUsers(progress, companyId, userIds, indexes, migrationType);
            }

        } catch (Exception e) {
            log.error("Error in optimized batch processing, falling back to individual processing: companyId={}", companyId, e);
            processOptimizedIndividualUsers(progress, companyId, userIds, indexes, migrationType);
        }
    }

    /**
     * 批量处理已完成用户（优化版本）
     */
    private boolean processOptimizedBatchFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Optimized batch processing finished users: companyId={}, count={}", companyId, userIds.size());

            // 批量调用您现有的migrateFinishedOne方法
            for (String userId : userIds) {
                migrateFinishedOne(companyId, userId);
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to process optimized batch finished users: companyId={}, userCount={}",
                    companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 批量处理未完成用户（优化版本）
     */
    private boolean processOptimizedBatchNoFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Optimized batch processing no-finished users: companyId={}, count={}", companyId, userIds.size());

            // 批量调用您现有的migrateNoFinishOne方法
            for (String userId : userIds) {
                migrateNoFinishOne(companyId, userId);
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to process optimized batch no-finished users: companyId={}, userCount={}",
                    companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 逐个处理用户（优化版本）
     */
    private void processOptimizedIndividualUsers(OptimizedBitmapProgress progress, String companyId,
                                               List<String> userIds, List<Long> indexes, String migrationType) {

        String sessionId = progress.getSessionId();

        log.debug("Processing optimized individual users: companyId={}, count={}", companyId, userIds.size());

        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            Long globalIndex = indexes.get(i);

            try {
                // 处理单个用户并记录失败信息
                boolean success = processOptimizedIndividualUserWithFailureTracking(progress, companyId, userId, globalIndex, migrationType);

                // 更新位图
                bitMap.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, success);

                if (success) {
                    log.debug("Successfully processed optimized individual user: companyId={}, userId={}", companyId, userId);
                } else {
                    log.warn("Failed to process optimized individual user: companyId={}, userId={}", companyId, userId);
                }

            } catch (Exception e) {
                log.error("Error processing optimized individual user: companyId={}, userId={}", companyId, userId, e);

                // 记录异常失败信息
                String errorType = getErrorType(e);
                String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
                progress.recordFailure(userId, companyId, globalIndex, errorMessage, errorType);

                // 即使失败也要标记为已处理，避免重复处理
                bitMap.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, false);
            }
        }
    }

    /**
     * 处理单个用户（优化版本）
     */
    private boolean processOptimizedIndividualUser(String companyId, String userId, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                migrateFinishedOne(companyId, userId);
                return true;
            } else if ("NO_FINISHED".equals(migrationType)) {
                migrateNoFinishOne(companyId, userId);
                return true;
            }
            return false;

        } catch (Exception e) {
            log.error("Failed to process optimized individual user: companyId={}, userId={}, type={}",
                    companyId, userId, migrationType, e);
            return false;
        }
    }

    // ==================== 失败记录跟踪和重试功能 ====================

    /**
     * 处理单个用户并记录失败信息
     */
    private boolean processOptimizedIndividualUserWithFailureTracking(OptimizedBitmapProgress progress,
                                                                     String companyId, String userId,
                                                                     long globalIndex, String migrationType) {
        try {
            boolean success = processOptimizedIndividualUser(companyId, userId, migrationType);

            if (!success) {
                // 记录失败信息
                String errorType = "BUSINESS_LOGIC_ERROR";
                String errorMessage = "业务逻辑处理失败";
                progress.recordFailure(userId, companyId, globalIndex, errorMessage, errorType);
            }

            return success;

        } catch (Exception e) {
            // 记录异常失败信息
            String errorType = getErrorType(e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            progress.recordFailure(userId, companyId, globalIndex, errorMessage, errorType);

            log.error("Failed to process user with failure tracking: companyId={}, userId={}, globalIndex={}",
                    companyId, userId, globalIndex, e);
            return false;
        }
    }

    /**
     * 获取错误类型
     */
    private String getErrorType(Exception e) {
        if (e instanceof java.sql.SQLException) {
            return "DATABASE_ERROR";
        } else if (e instanceof java.net.SocketTimeoutException) {
            return "TIMEOUT_ERROR";
        } else if (e instanceof java.lang.NullPointerException) {
            return "NULL_POINTER_ERROR";
        } else if (e instanceof java.lang.IllegalArgumentException) {
            return "INVALID_ARGUMENT_ERROR";
        } else {
            return "UNKNOWN_ERROR";
        }
    }

    /**
     * 启动失败记录重试
     *
     * @param sessionId 原始会话ID
     * @param userIds 指定要重试的用户ID列表（可选，为空则重试所有失败记录）
     * @return 重试会话ID
     */
    public String startFailureRetry(String sessionId, List<String> userIds) {
        log.info("Starting failure retry for session: {}, userIds: {}", sessionId, userIds);

        try {
            // 加载原始进度
            OptimizedBitmapProgress originalProgress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (originalProgress == null) {
                throw new RuntimeException("原始迁移会话不存在: " + sessionId);
            }

            // 获取失败记录
            List<OptimizedBitmapProgress.FailureRecord> failureRecords;
            if (userIds != null && !userIds.isEmpty()) {
                // 重试指定用户
                failureRecords = userIds.stream()
                        .map(originalProgress::getFailureRecord)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                // 重试所有失败记录
                failureRecords = originalProgress.getAllFailureRecords();
            }

            if (failureRecords.isEmpty()) {
                throw new RuntimeException("没有找到需要重试的失败记录");
            }

            // 创建重试会话
            String retrySessionId = generateRetrySessionId(sessionId);
            OptimizedBitmapProgress retryProgress = new OptimizedBitmapProgress(retrySessionId, originalProgress.getMigrationType());
            retryProgress.getTotalRecords().set(failureRecords.size());
            retryProgress.saveMetadata();

            // 异步执行重试
            executeFailureRetryAsync(retrySessionId, failureRecords, originalProgress);

            log.info("Failure retry started successfully: retrySessionId={}, retryCount={}", retrySessionId, failureRecords.size());
            return retrySessionId;

        } catch (Exception e) {
            log.error("Failed to start failure retry for session: {}", sessionId, e);
            throw new RuntimeException("启动失败重试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行失败记录重试
     */
    @Async("migrationTaskExecutor")
    public void executeFailureRetryAsync(String retrySessionId, List<OptimizedBitmapProgress.FailureRecord> failureRecords,
                                        OptimizedBitmapProgress originalProgress) {
        try {
            log.info("Starting async failure retry execution: retrySessionId={}, retryCount={}", retrySessionId, failureRecords.size());

            OptimizedBitmapProgress retryProgress = OptimizedBitmapProgress.loadMetadata(retrySessionId);
            if (retryProgress == null) {
                log.error("Retry progress not found: {}", retrySessionId);
                return;
            }

            retryProgress.start();
            retryProgress.saveMetadata();

            // 执行重试逻辑
            executeFailureRetryLogic(retryProgress, failureRecords, originalProgress);

            retryProgress.complete();
            log.info("Failure retry completed successfully: retrySessionId={}", retrySessionId);

        } catch (Exception e) {
            log.error("Failure retry failed: retrySessionId={}", retrySessionId, e);
            handleFailureRetryFailure(retrySessionId, e.getMessage());
        }
    }

    /**
     * 执行失败记录重试逻辑
     */
    private void executeFailureRetryLogic(OptimizedBitmapProgress retryProgress,
                                        List<OptimizedBitmapProgress.FailureRecord> failureRecords,
                                        OptimizedBitmapProgress originalProgress) {

        String migrationType = retryProgress.getMigrationType();
        String retrySessionId = retryProgress.getSessionId();

        for (int i = 0; i < failureRecords.size(); i++) {
            OptimizedBitmapProgress.FailureRecord failureRecord = failureRecords.get(i);

            // 检查是否需要暂停
            if (shouldPauseOptimized(retryProgress)) {
                log.info("Failure retry paused at record: {}", i);
                break;
            }

            String userId = failureRecord.getUserId();
            String companyId = failureRecord.getCompanyId();
            long globalIndex = failureRecord.getGlobalIndex();

            try {
                log.info("Retrying failed record: userId={}, companyId={}, globalIndex={}, retryCount={}",
                        userId, companyId, globalIndex, failureRecord.getRetryCount());

                // 更新原始记录的重试次数
                originalProgress.incrementRetryCount(userId);

                // 执行重试
                boolean success = processOptimizedIndividualUser(companyId, userId, migrationType);

                if (success) {
                    // 重试成功：从原始进度中移除失败记录
                    originalProgress.removeFailureRecord(userId);

                    // 更新重试进度
                    retryProgress.markProcessed(i, true);

                    log.info("Retry successful for user: userId={}, companyId={}", userId, companyId);

                } else {
                    // 重试仍然失败
                    retryProgress.recordFailure(userId, companyId, i, "重试仍然失败", "RETRY_FAILED");
                    retryProgress.markProcessed(i, false);

                    log.warn("Retry failed for user: userId={}, companyId={}", userId, companyId);
                }

            } catch (Exception e) {
                // 重试异常
                String errorType = getErrorType(e);
                String errorMessage = "重试异常: " + (e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName());

                retryProgress.recordFailure(userId, companyId, i, errorMessage, errorType);
                retryProgress.markProcessed(i, false);

                log.error("Retry exception for user: userId={}, companyId={}", userId, companyId, e);
            }

            // 定期保存进度
            if (i % 10 == 0) {
                retryProgress.saveMetadata();
                originalProgress.saveMetadata();
            }
        }

        // 最终保存
        retryProgress.saveMetadata();
        originalProgress.saveMetadata();
    }

    /**
     * 获取失败记录列表
     *
     * @param sessionId 会话ID
     * @param companyId 公司ID（可选）
     * @param errorType 错误类型（可选）
     * @return 失败记录列表
     */
    public List<OptimizedBitmapProgress.FailureRecord> getFailureRecords(String sessionId, String companyId, String errorType) {
        log.debug("Getting failure records: sessionId={}, companyId={}, errorType={}", sessionId, companyId, errorType);

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return new ArrayList<>();
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getAllFailureRecords();

            // 按公司ID过滤
            if (companyId != null && !companyId.trim().isEmpty()) {
                failureRecords = failureRecords.stream()
                        .filter(record -> companyId.equals(record.getCompanyId()))
                        .collect(Collectors.toList());
            }

            // 按错误类型过滤
            if (errorType != null && !errorType.trim().isEmpty()) {
                failureRecords = failureRecords.stream()
                        .filter(record -> errorType.equals(record.getErrorType()))
                        .collect(Collectors.toList());
            }

            return failureRecords;

        } catch (Exception e) {
            log.error("Failed to get failure records: sessionId={}", sessionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取失败记录统计信息
     *
     * @param sessionId 会话ID
     * @return 失败统计信息
     */
    public String getFailureStatistics(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return "迁移会话不存在";
            }

            return progress.getFailureStatistics();

        } catch (Exception e) {
            log.error("Failed to get failure statistics: sessionId={}", sessionId, e);
            return "获取失败统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 生成重试会话ID
     */
    private String generateRetrySessionId(String originalSessionId) {
        return "RETRY_" + originalSessionId + "_" + System.currentTimeMillis();
    }

    /**
     * 处理失败重试失败
     */
    private void handleFailureRetryFailure(String retrySessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress retryProgress = OptimizedBitmapProgress.loadMetadata(retrySessionId);
            if (retryProgress != null) {
                retryProgress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                retryProgress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to handle failure retry failure: retrySessionId={}", retrySessionId, e);
        }
    }

    /**
     * 检查是否应该暂停优化迁移
     */
    private boolean shouldPauseOptimized(OptimizedBitmapProgress progress) {
        OptimizedBitmapProgress latest = OptimizedBitmapProgress.loadMetadata(progress.getSessionId());
        return latest != null && latest.getStatus() == OptimizedBitmapProgress.MigrationStatus.PAUSED;
    }

    /**
     * 检查优化迁移是否全部处理完成
     */
    private boolean isOptimizedAllProcessed(OptimizedBitmapProgress progress) {
        return progress.getProcessedCount().get() >= progress.getTotalRecords().get();
    }

    /**
     * 清理优化迁移文件
     */
    public boolean cleanupOptimizedMigrationFiles(String sessionId) {
        log.info("Cleaning up optimized migration files for session: {}", sessionId);

        if (!useBitmapOptimization || bitMap == null) {
            return false;
        }

        try {
            bitMap.cleanupSession(sessionId);
            return true;
        } catch (Exception e) {
            log.error("Failed to cleanup optimized migration files for session: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取优化迁移统计信息
     */
    public String getOptimizedMigrationStatistics(String sessionId) {
        if (!useBitmapOptimization) {
            return "Bitmap optimization not enabled";
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            return progress != null ? progress.getStatistics() : "Migration not found";
        } catch (Exception e) {
            log.error("Failed to get optimized migration statistics for session: {}", sessionId, e);
            return "Error getting statistics: " + e.getMessage();
        }
    }

    // ==================== 增量检测和处理功能 ====================

    /**
     * 执行增量检测
     * 检测已处理数据的状态变化，并标记需要重新处理的记录
     *
     * @param progress 迁移进度
     */
    private void performIncrementalDetection(OptimizedBitmapProgress progress) {
        log.info("Starting incremental detection for session: {}", progress.getSessionId());

        try {
            String migrationType = progress.getMigrationType();

            // 获取已处理的公司列表（从进度中获取）
            List<String> processedCompanies = getProcessedCompanies(progress);

            for (String companyId : processedCompanies) {
                detectCompanyStatusChanges(progress, companyId, migrationType);
            }

            // 处理检测到的状态变化
            processDetectedStatusChanges(progress);

            log.info("Incremental detection completed for session: {}, detected changes: {}",
                    progress.getSessionId(), progress.getAllStatusChangeRecords().size());

        } catch (Exception e) {
            log.error("Failed to perform incremental detection for session: {}", progress.getSessionId(), e);
        }
    }

    /**
     * 检测单个公司的状态变化
     *
     * @param progress 迁移进度
     * @param companyId 公司ID
     * @param originalMigrationType 原始迁移类型
     */
    private void detectCompanyStatusChanges(OptimizedBitmapProgress progress, String companyId, String originalMigrationType) {
        log.debug("Detecting status changes for company: {}", companyId);

        try {
            // 获取该公司已处理的用户列表
            List<String> processedUsers = getProcessedUsersForCompany(progress, companyId);

            for (String userId : processedUsers) {
                // 检测用户状态变化
                String currentStatus = detectUserCurrentStatus(companyId, userId);

                if (currentStatus != null && !StrUtil.equals(currentStatus, originalMigrationType)) {
                    // 记录状态变化
                    progress.recordStatusChange(userId, companyId, originalMigrationType, currentStatus);

                    log.info("Detected status change: companyId={}, userId={}, {}->{}",
                            companyId, userId, originalMigrationType, currentStatus);
                }
            }

        } catch (Exception e) {
            log.error("Failed to detect status changes for company: {}", companyId, e);
        }
    }

    /**
     * 检测用户当前状态
     * TODO: 请根据您的实际业务逻辑实现此方法
     *
     * @param companyId 公司ID
     * @param userId 用户ID
     * @return 当前状态（FINISHED/NO_FINISHED）
     */
    private String detectUserCurrentStatus(String companyId, String userId) {
        try {
            // 这里需要根据您的实际业务逻辑来检测用户当前状态
            // 例如：查询数据库中的最新状态 使用您现有的方法检测状态
            EvalUser evalUser = userRepo.getBaseTaskUser(new TenantId(companyId),userId);
            evalUser.isFinished();//是否任务完成
            return evalUser.isFinished() ? "FINISHED" : "NO_FINISHED";
        } catch (Exception e) {
            log.error("Failed to detect current status for user: companyId={}, userId={}", companyId, userId, e);
            return null;
        }
    }

    /**
     * 处理检测到的状态变化
     *
     * @param progress 迁移进度
     */
    private void processDetectedStatusChanges(OptimizedBitmapProgress progress) {
        List<OptimizedBitmapProgress.StatusChangeRecord> statusChanges = progress.getStatusChangesNeedingReprocessing();

        if (statusChanges.isEmpty()) {
            log.debug("No status changes need reprocessing for session: {}", progress.getSessionId());
            return;
        }

        log.info("Processing {} status changes for session: {}", statusChanges.size(), progress.getSessionId());

        for (OptimizedBitmapProgress.StatusChangeRecord changeRecord : statusChanges) {
            try {
                // 重新处理状态变化的用户
                boolean success = reprocessStatusChangedUser(changeRecord);

                if (success) {
                    progress.markStatusChangeAsReprocessed(changeRecord.getUserId());
                    log.info("Successfully reprocessed status changed user: userId={}, {}->{}",
                            changeRecord.getUserId(), changeRecord.getOriginalStatus(), changeRecord.getCurrentStatus());
                } else {
                    changeRecord.incrementReprocessCount();
                    log.warn("Failed to reprocess status changed user: userId={}, retryCount={}",
                            changeRecord.getUserId(), changeRecord.getReprocessCount());
                }

            } catch (Exception e) {
                log.error("Error reprocessing status changed user: userId={}", changeRecord.getUserId(), e);
                changeRecord.incrementReprocessCount();
            }
        }
    }

    /**
     * 重新处理状态变化的用户
     *
     * @param changeRecord 状态变化记录
     * @return 是否成功
     */
    private boolean reprocessStatusChangedUser(OptimizedBitmapProgress.StatusChangeRecord changeRecord) {
        try {
            String userId = changeRecord.getUserId();
            String companyId = changeRecord.getCompanyId();
            String currentStatus = changeRecord.getCurrentStatus();

            log.info("Reprocessing status changed user: userId={}, companyId={}, newStatus={}",
                    userId, companyId, currentStatus);

            // 根据新状态重新处理用户
            if ("FINISHED".equals(currentStatus)) {
                migrateFinishedOne(companyId, userId);
            } else if ("NO_FINISHED".equals(currentStatus)) {
                migrateNoFinishOne(companyId, userId);
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to reprocess status changed user: {}", changeRecord, e);
            return false;
        }
    }

    /**
     * 获取已处理的公司列表
     * TODO: 根据位图信息获取已处理的公司列表
     *
     * @param progress 迁移进度
     * @return 已处理的公司ID列表
     */
    private List<String> getProcessedCompanies(OptimizedBitmapProgress progress) {
        // 这里需要根据位图信息或进度信息获取已处理的公司列表
        // 临时实现：返回当前公司ID
        List<String> processedCompanies = new ArrayList<>();
        if (progress.getCurrentCompanyId() != null) {
            processedCompanies.add(progress.getCurrentCompanyId());
        }
        return processedCompanies;
    }

    /**
     * 获取公司已处理的用户列表
     * TODO: 根据位图信息获取已处理的用户列表
     *
     * @param progress 迁移进度
     * @param companyId 公司ID
     * @return 已处理的用户ID列表
     */
    private List<String> getProcessedUsersForCompany(OptimizedBitmapProgress progress, String companyId) {
        // 这里需要根据位图信息获取已处理的用户列表
        // 临时实现：返回模拟的用户列表
        List<String> processedUsers = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            processedUsers.add("USER_" + companyId + "_" + i);
        }
        return processedUsers;
    }

    /**
     * 启动增量重新处理
     * 处理所有检测到的状态变化
     *
     * @param sessionId 会话ID
     * @return 重新处理会话ID
     */
    public String startIncrementalReprocessing(String sessionId) {
        log.info("Starting incremental reprocessing for session: {}", sessionId);

        try {
            OptimizedBitmapProgress originalProgress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (originalProgress == null) {
                throw new RuntimeException("原始迁移会话不存在: " + sessionId);
            }

            // 获取需要重新处理的状态变化记录
            List<OptimizedBitmapProgress.StatusChangeRecord> statusChanges = originalProgress.getStatusChangesNeedingReprocessing();

            if (statusChanges.isEmpty()) {
                throw new RuntimeException("没有需要重新处理的状态变化记录");
            }

            // 创建增量重新处理会话
            String incrementalSessionId = generateIncrementalSessionId(sessionId);
            OptimizedBitmapProgress incrementalProgress = new OptimizedBitmapProgress(incrementalSessionId, "INCREMENTAL");
            incrementalProgress.getTotalRecords().set(statusChanges.size());
            incrementalProgress.saveMetadata();

            // 异步执行增量重新处理
            executeIncrementalReprocessingAsync(incrementalSessionId, statusChanges, originalProgress);

            log.info("Incremental reprocessing started: incrementalSessionId={}, changeCount={}",
                    incrementalSessionId, statusChanges.size());
            return incrementalSessionId;

        } catch (Exception e) {
            log.error("Failed to start incremental reprocessing for session: {}", sessionId, e);
            throw new RuntimeException("启动增量重新处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行增量重新处理
     */
    @Async("migrationTaskExecutor")
    public void executeIncrementalReprocessingAsync(String incrementalSessionId,
                                                   List<OptimizedBitmapProgress.StatusChangeRecord> statusChanges,
                                                   OptimizedBitmapProgress originalProgress) {
        try {
            log.info("Starting async incremental reprocessing: incrementalSessionId={}, changeCount={}",
                    incrementalSessionId, statusChanges.size());

            OptimizedBitmapProgress incrementalProgress = OptimizedBitmapProgress.loadMetadata(incrementalSessionId);
            if (incrementalProgress == null) {
                log.error("Incremental progress not found: {}", incrementalSessionId);
                return;
            }

            incrementalProgress.start();
            incrementalProgress.saveMetadata();

            // 执行增量重新处理逻辑
            for (int i = 0; i < statusChanges.size(); i++) {
                OptimizedBitmapProgress.StatusChangeRecord changeRecord = statusChanges.get(i);

                try {
                    boolean success = reprocessStatusChangedUser(changeRecord);

                    if (success) {
                        originalProgress.markStatusChangeAsReprocessed(changeRecord.getUserId());
                        incrementalProgress.markProcessed(i, true);
                    } else {
                        incrementalProgress.markProcessed(i, false);
                    }

                } catch (Exception e) {
                    log.error("Error in incremental reprocessing: {}", changeRecord, e);
                    incrementalProgress.markProcessed(i, false);
                }

                // 定期保存进度
                if (i % 10 == 0) {
                    incrementalProgress.saveMetadata();
                    originalProgress.saveMetadata();
                }
            }

            incrementalProgress.complete();
            originalProgress.saveMetadata();

            log.info("Incremental reprocessing completed: incrementalSessionId={}", incrementalSessionId);

        } catch (Exception e) {
            log.error("Incremental reprocessing failed: incrementalSessionId={}", incrementalSessionId, e);
            handleIncrementalReprocessingFailure(incrementalSessionId, e.getMessage());
        }
    }

    /**
     * 获取状态变化统计信息
     *
     * @param sessionId 会话ID
     * @return 状态变化统计信息
     */
    public String getStatusChangeStatistics(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return "迁移会话不存在";
            }

            return progress.getStatusChangeStatistics();

        } catch (Exception e) {
            log.error("Failed to get status change statistics: sessionId={}", sessionId, e);
            return "获取状态变化统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 生成增量会话ID
     */
    private String generateIncrementalSessionId(String originalSessionId) {
        return "INCREMENTAL_" + originalSessionId + "_" + System.currentTimeMillis();
    }

    /**
     * 处理增量重新处理失败
     */
    private void handleIncrementalReprocessingFailure(String incrementalSessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress incrementalProgress = OptimizedBitmapProgress.loadMetadata(incrementalSessionId);
            if (incrementalProgress != null) {
                incrementalProgress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                incrementalProgress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to handle incremental reprocessing failure: incrementalSessionId={}", incrementalSessionId, e);
        }
    }

    // ==================== 分离式任务协调功能 ====================

    /**
     * 获取关联任务的会话ID
     *
     * @param sessionId 当前任务会话ID
     * @return 关联任务会话ID
     */
    public String getRelatedTaskSessionId(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null && progress.isEnableTaskSeparation()) {
                return progress.getRelatedTaskSessionId();
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to get related task session ID: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 启动跨任务增量处理
     * 处理需要在任务间转移的状态变化
     *
     * @param sourceSessionId 源任务会话ID
     * @return 跨任务处理会话ID
     */
    public String startCrossTaskReprocessing(String sourceSessionId) {
        log.info("Starting cross-task reprocessing for session: {}", sourceSessionId);

        try {
            OptimizedBitmapProgress sourceProgress = OptimizedBitmapProgress.loadMetadata(sourceSessionId);
            if (sourceProgress == null) {
                throw new RuntimeException("源任务会话不存在: " + sourceSessionId);
            }

            if (!sourceProgress.isEnableTaskSeparation()) {
                throw new RuntimeException("任务分离未启用，无法进行跨任务处理");
            }

            // 获取需要跨任务处理的状态变化记录
            List<OptimizedBitmapProgress.StatusChangeRecord> crossTaskChanges = sourceProgress.getCrossTaskStatusChanges();

            if (crossTaskChanges.isEmpty()) {
                throw new RuntimeException("没有需要跨任务处理的状态变化记录");
            }

            // 获取目标任务会话ID
            String targetSessionId = sourceProgress.getRelatedTaskSessionId();
            if (targetSessionId == null) {
                throw new RuntimeException("未找到关联的目标任务");
            }

            // 创建跨任务处理会话
            String crossTaskSessionId = generateCrossTaskSessionId(sourceSessionId, targetSessionId);
            OptimizedBitmapProgress crossTaskProgress = new OptimizedBitmapProgress(crossTaskSessionId, "CROSS_TASK");
            crossTaskProgress.getTotalRecords().set(crossTaskChanges.size());
            crossTaskProgress.saveMetadata();

            // 异步执行跨任务处理
            executeCrossTaskReprocessingAsync(crossTaskSessionId, crossTaskChanges, sourceProgress, targetSessionId);

            log.info("Cross-task reprocessing started: crossTaskSessionId={}, changeCount={}",
                    crossTaskSessionId, crossTaskChanges.size());
            return crossTaskSessionId;

        } catch (Exception e) {
            log.error("Failed to start cross-task reprocessing for session: {}", sourceSessionId, e);
            throw new RuntimeException("启动跨任务处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行跨任务重新处理
     */
    @Async("migrationTaskExecutor")
    public void executeCrossTaskReprocessingAsync(String crossTaskSessionId,
                                                 List<OptimizedBitmapProgress.StatusChangeRecord> crossTaskChanges,
                                                 OptimizedBitmapProgress sourceProgress,
                                                 String targetSessionId) {
        try {
            log.info("Starting async cross-task reprocessing: crossTaskSessionId={}, changeCount={}",
                    crossTaskSessionId, crossTaskChanges.size());

            OptimizedBitmapProgress crossTaskProgress = OptimizedBitmapProgress.loadMetadata(crossTaskSessionId);
            OptimizedBitmapProgress targetProgress = OptimizedBitmapProgress.loadMetadata(targetSessionId);

            if (crossTaskProgress == null || targetProgress == null) {
                log.error("Cross-task or target progress not found: crossTask={}, target={}",
                        crossTaskSessionId, targetSessionId);
                return;
            }

            crossTaskProgress.start();
            crossTaskProgress.saveMetadata();

            // 执行跨任务处理逻辑
            for (int i = 0; i < crossTaskChanges.size(); i++) {
                OptimizedBitmapProgress.StatusChangeRecord changeRecord = crossTaskChanges.get(i);

                try {
                    // 在目标任务中重新处理
                    boolean success = reprocessStatusChangedUser(changeRecord);

                    if (success) {
                        // 标记源任务中的状态变化为已转移
                        sourceProgress.markCrossTaskChangeAsTransferred(changeRecord.getUserId());

                        // 在目标任务中记录处理成功
                        targetProgress.incrementSuccess();

                        crossTaskProgress.markProcessed(i, true);

                        log.info("Cross-task reprocessing successful: userId={}, {}->{}",
                                changeRecord.getUserId(), changeRecord.getOriginalStatus(), changeRecord.getCurrentStatus());
                    } else {
                        crossTaskProgress.markProcessed(i, false);
                        log.warn("Cross-task reprocessing failed: userId={}", changeRecord.getUserId());
                    }

                } catch (Exception e) {
                    log.error("Error in cross-task reprocessing: {}", changeRecord, e);
                    crossTaskProgress.markProcessed(i, false);
                }

                // 定期保存进度
                if (i % 10 == 0) {
                    crossTaskProgress.saveMetadata();
                    sourceProgress.saveMetadata();
                    targetProgress.saveMetadata();
                }
            }

            crossTaskProgress.complete();
            sourceProgress.saveMetadata();
            targetProgress.saveMetadata();

            log.info("Cross-task reprocessing completed: crossTaskSessionId={}", crossTaskSessionId);

        } catch (Exception e) {
            log.error("Cross-task reprocessing failed: crossTaskSessionId={}", crossTaskSessionId, e);
            handleCrossTaskReprocessingFailure(crossTaskSessionId, e.getMessage());
        }
    }

    /**
     * 获取分离式任务状态概览
     *
     * @param sessionId 任务会话ID
     * @return 任务状态概览
     */
    public SeparatedTasksOverview getSeparatedTasksOverview(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return null;
            }

            SeparatedTasksOverview overview = new SeparatedTasksOverview();
            overview.setCurrentTaskSessionId(sessionId);
            overview.setCurrentTaskType(progress.getTaskType());
            overview.setCurrentTaskStatus(progress.getStatus());
            overview.setCurrentTaskProgress(progress.getProgressPercentage());

            // 获取关联任务信息
            String relatedSessionId = progress.getRelatedTaskSessionId();
            if (relatedSessionId != null) {
                OptimizedBitmapProgress relatedProgress = OptimizedBitmapProgress.loadMetadata(relatedSessionId);
                if (relatedProgress != null) {
                    overview.setRelatedTaskSessionId(relatedSessionId);
                    overview.setRelatedTaskType(relatedProgress.getTaskType());
                    overview.setRelatedTaskStatus(relatedProgress.getStatus());
                    overview.setRelatedTaskProgress(relatedProgress.getProgressPercentage());
                }
            }

            // 获取状态变化统计
            overview.setCurrentTaskStatusChanges(progress.getAllStatusChangeRecords().size());
            overview.setCrossTaskChanges(progress.getCrossTaskStatusChanges().size());
            overview.setInTaskChanges(progress.getInTaskStatusChanges().size());

            return overview;

        } catch (Exception e) {
            log.error("Failed to get separated tasks overview: sessionId={}", sessionId, e);
            return null;
        }
    }

    /**
     * 生成跨任务会话ID
     */
    private String generateCrossTaskSessionId(String sourceSessionId, String targetSessionId) {
        return "CROSS_TASK_" + System.currentTimeMillis() + "_" +
               sourceSessionId.substring(sourceSessionId.lastIndexOf("_") + 1) + "_" +
               targetSessionId.substring(targetSessionId.lastIndexOf("_") + 1);
    }

    /**
     * 处理跨任务重新处理失败
     */
    private void handleCrossTaskReprocessingFailure(String crossTaskSessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress crossTaskProgress = OptimizedBitmapProgress.loadMetadata(crossTaskSessionId);
            if (crossTaskProgress != null) {
                crossTaskProgress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                crossTaskProgress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to handle cross-task reprocessing failure: crossTaskSessionId={}", crossTaskSessionId, e);
        }
    }

    /**
     * 分离式任务状态概览
     */
    @Data
    @NoArgsConstructor
    public static class SeparatedTasksOverview {
        private String currentTaskSessionId;
        private OptimizedBitmapProgress.TaskType currentTaskType;
        private OptimizedBitmapProgress.MigrationStatus currentTaskStatus;
        private double currentTaskProgress;

        private String relatedTaskSessionId;
        private OptimizedBitmapProgress.TaskType relatedTaskType;
        private OptimizedBitmapProgress.MigrationStatus relatedTaskStatus;
        private double relatedTaskProgress;

        private int currentTaskStatusChanges;
        private int crossTaskChanges;
        private int inTaskChanges;
    }

    // ==================== 动态扩展功能 ====================

    /**
     * 手动添加动态用户到正在进行的迁移任务
     *
     * @param sessionId 迁移会话ID
     * @param userId 用户ID
     * @param companyId 公司ID
     * @param userStatus 用户状态（FINISHED/NO_FINISHED）
     * @return 是否添加成功
     */
    public boolean addDynamicUser(String sessionId, String userId, String companyId, String userStatus) {
        log.info("Adding dynamic user to migration: sessionId={}, userId={}, companyId={}, status={}",
                sessionId, userId, companyId, userStatus);

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                throw new RuntimeException("迁移会话不存在: " + sessionId);
            }

            if (!progress.isRunning()) {
                throw new RuntimeException("迁移任务未在运行中，无法添加动态用户");
            }

            boolean success = progress.addDynamicUser(userId, companyId, userStatus, "MANUAL");
            if (success) {
                progress.saveMetadata();
                log.info("Successfully added dynamic user: sessionId={}, userId={}", sessionId, userId);
            }

            return success;

        } catch (Exception e) {
            log.error("Failed to add dynamic user: sessionId={}, userId={}", sessionId, userId, e);
            throw new RuntimeException("添加动态用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量添加动态用户
     *
     * @param sessionId 迁移会话ID
     * @param users 用户列表（userId -> {companyId, userStatus}）
     * @return 成功添加的用户数量
     */
    public int addDynamicUsers(String sessionId, Map<String, Map<String, String>> users) {
        log.info("Batch adding dynamic users to migration: sessionId={}, userCount={}", sessionId, users.size());

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                throw new RuntimeException("迁移会话不存在: " + sessionId);
            }

            if (!progress.isRunning()) {
                throw new RuntimeException("迁移任务未在运行中，无法添加动态用户");
            }

            int successCount = progress.addDynamicUsers(users, "BATCH_MANUAL");
            progress.saveMetadata();

            log.info("Batch added dynamic users: sessionId={}, total={}, success={}",
                    sessionId, users.size(), successCount);
            return successCount;

        } catch (Exception e) {
            log.error("Failed to batch add dynamic users: sessionId={}", sessionId, e);
            throw new RuntimeException("批量添加动态用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行动态扩展检查
     * 自动检测新增的需要迁移的用户
     *
     * @param progress 迁移进度
     */
    private void performDynamicExpansionCheck(OptimizedBitmapProgress progress) {
        log.info("Starting dynamic expansion check for session: {}", progress.getSessionId());

        try {
            String migrationType = progress.getMigrationType();

            // 检测新增用户（您需要根据实际业务逻辑实现）
            List<DynamicUserInfo> newUsers = detectNewUsers(migrationType, progress);

            if (!newUsers.isEmpty()) {
                // 添加检测到的新用户
                int addedCount = 0;
                for (DynamicUserInfo userInfo : newUsers) {
                    boolean success = progress.addDynamicUser(
                            userInfo.getUserId(),
                            userInfo.getCompanyId(),
                            userInfo.getUserStatus(),
                            "AUTO_DETECTION"
                    );
                    if (success) {
                        addedCount++;
                    }
                }

                if (addedCount > 0) {
                    progress.saveMetadata();
                    log.info("Auto detected and added dynamic users: sessionId={}, detected={}, added={}",
                            progress.getSessionId(), newUsers.size(), addedCount);
                }
            }

            // 处理待处理的动态用户
            processPendingDynamicUsers(progress);

        } catch (Exception e) {
            log.error("Failed to perform dynamic expansion check for session: {}", progress.getSessionId(), e);
        }
    }

    /**
     * 检测新增用户
     * TODO: 请根据您的实际业务逻辑实现此方法
     *
     * @param migrationType 迁移类型
     * @param progress 迁移进度
     * @return 新增用户列表
     */
    private List<DynamicUserInfo> detectNewUsers(String migrationType, OptimizedBitmapProgress progress) {
        List<DynamicUserInfo> newUsers = new ArrayList<>();

        try {
            // 示例实现：检测最近新增的用户
            // 您可以根据实际业务逻辑替换这部分代码

            // 方案1：查询数据库中最近新增的用户
            // List<User> recentUsers = userService.getRecentUsers(progress.getLastDynamicCheckTime());

            // 方案2：监听业务事件获取新增用户
            // List<UserEvent> userEvents = eventService.getUserEvents(progress.getLastDynamicCheckTime());

            // 方案3：定期扫描特定状态的用户
            // List<User> pendingUsers = userService.getPendingMigrationUsers(migrationType);

            // 临时模拟实现（5%概率检测到新用户）
            if (Math.random() < 0.05) {
                String userId = "DYNAMIC_USER_" + System.currentTimeMillis();
                String companyId = "COMPANY_" + (int)(Math.random() * 100);
                newUsers.add(new DynamicUserInfo(userId, companyId, migrationType));
                log.debug("Simulated new user detection: userId={}, companyId={}", userId, companyId);
            }

        } catch (Exception e) {
            log.error("Failed to detect new users for migration type: {}", migrationType, e);
        }

        return newUsers;
    }
    /**
     * 为动态用户计算全局索引
     * 动态用户使用特殊的索引范围，避免与正常迁移用户冲突
     *
     * @param userRecord 动态用户记录
     * @return 全局索引
     */
    private long calculateDynamicUserGlobalIndex(OptimizedBitmapProgress.DynamicUserRecord userRecord) {
        // 为动态用户分配特殊的索引范围，避免与正常迁移用户冲突
        // 使用一个很大的页码，确保不与正常迁移冲突
        long dynamicUserBaseIndex = 10000000L; // 1000万作为动态用户的起始索引

        // 使用用户ID的哈希值来生成相对唯一的索引
        int userIdHash = Math.abs(userRecord.getUserId().hashCode());
        int companyIdHash = Math.abs(userRecord.getCompanyId().hashCode());

        // 组合哈希值生成索引偏移
        long indexOffset = (long) userIdHash + (long) companyIdHash;

        return dynamicUserBaseIndex + (indexOffset % 1000000L); // 限制在100万范围内
    }
    /**
     * 处理待处理的动态用户
     *
     * @param progress 迁移进度
     */
    private void processPendingDynamicUsers(OptimizedBitmapProgress progress) {
        List<OptimizedBitmapProgress.DynamicUserRecord> pendingUsers = progress.getDynamicUsersNeedingProcessing();

        if (pendingUsers.isEmpty()) {
            return;
        }

        log.info("Processing pending dynamic users: sessionId={}, count={}",
                progress.getSessionId(), pendingUsers.size());

        for (OptimizedBitmapProgress.DynamicUserRecord userRecord : pendingUsers) {
            try {
                // 为动态用户分配全局索引
                long globalIndex = calculateDynamicUserGlobalIndex(userRecord);
                // 处理动态用户
                boolean success = processDynamicUser(userRecord, globalIndex);

                if (success) {
                    progress.markDynamicUserAsProcessed(userRecord.getUserId(), globalIndex);
                    progress.incrementSuccess();

                    log.info("Successfully processed dynamic user: userId={}, globalIndex={}",
                            userRecord.getUserId(), globalIndex);
                } else {
                    progress.incrementFailure();
                    log.warn("Failed to process dynamic user: userId={}", userRecord.getUserId());
                }

            } catch (Exception e) {
                log.error("Error processing dynamic user: {}", userRecord, e);
                progress.incrementFailure();
            }
        }

        // 保存进度
        progress.saveMetadata();
    }

    /**
     * 处理单个动态用户
     *
     * @param userRecord 动态用户记录
     * @param globalIndex 全局索引
     * @return 是否处理成功
     */
    private boolean processDynamicUser(OptimizedBitmapProgress.DynamicUserRecord userRecord, long globalIndex) {
        try {
            String userId = userRecord.getUserId();
            String companyId = userRecord.getCompanyId();
            String userStatus = userRecord.getUserStatus();

            log.info("Processing dynamic user: userId={}, companyId={}, status={}, globalIndex={}",
                    userId, companyId, userStatus, globalIndex);

            // 根据用户状态调用相应的处理方法
            if ("FINISHED".equals(userStatus)) {
                migrateFinishedOne(companyId, userId);
            } else if ("NO_FINISHED".equals(userStatus)) {
                migrateNoFinishOne(companyId, userId);
            } else {
                log.warn("Unknown user status for dynamic user: userId={}, status={}", userId, userStatus);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to process dynamic user: {}", userRecord, e);
            return false;
        }
    }

    /**
     * 获取动态扩展统计信息
     *
     * @param sessionId 会话ID
     * @return 动态扩展统计信息
     */
    public String getDynamicExpansionStatistics(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return "迁移会话不存在";
            }

            return progress.getDynamicExpansionStatistics();

        } catch (Exception e) {
            log.error("Failed to get dynamic expansion statistics: sessionId={}", sessionId, e);
            return "获取动态扩展统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 获取动态用户列表
     *
     * @param sessionId 会话ID
     * @param needsProcessingOnly 是否只返回待处理的用户
     * @return 动态用户列表
     */
    public List<OptimizedBitmapProgress.DynamicUserRecord> getDynamicUsers(String sessionId, boolean needsProcessingOnly) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return new ArrayList<>();
            }

            if (needsProcessingOnly) {
                return progress.getDynamicUsersNeedingProcessing();
            } else {
                return progress.getAllDynamicUsers();
            }

        } catch (Exception e) {
            log.error("Failed to get dynamic users: sessionId={}", sessionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 动态用户信息
     */
    @Getter
    @NoArgsConstructor
    public static class DynamicUserInfo {
        private String userId;
        private String companyId;
        private String userStatus;

        public DynamicUserInfo(String userId, String companyId, String userStatus) {
            this.userId = userId;
            this.companyId = companyId;
            this.userStatus = userStatus;
        }
    }
}
