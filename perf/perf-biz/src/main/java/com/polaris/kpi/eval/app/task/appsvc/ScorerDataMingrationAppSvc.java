package com.polaris.kpi.eval.app.task.appsvc;

import cn.com.polaris.kpi.KpiEmp;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.polaris.kpi.eval.app.TransactionWrap;
import com.polaris.kpi.eval.domain.task.dmsvc.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.entity.EmpEvalScorer;
import com.polaris.kpi.eval.domain.task.entity.TaskBitmapWithLog;
import com.polaris.kpi.eval.domain.task.repo.*;
import com.polaris.kpi.eval.infr.task.repimpl.EmpEvalRuleRepoImpl;
import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.migration.util.ConcurrentBitmapManager;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import com.polaris.kpi.org.domain.dept.entity.CompanyConf;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.kpi.org.infr.emp.dao.KpiEmpDao;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @BelongsProject: perf-server
 * @BelongsPackage: com.polaris.kpi.eval.app.task.appsvc
 * @Author: suxiaoqiu
 * @CreateTime: 2025-03-03  15:25
 * @Description: 评分人数据迁移服务
 * @Version: 2.0
 */
@Service
@Slf4j
public class ScorerDataMingrationAppSvc {

    @Autowired
    private TaskBitmapWithLog bitmapWithLog;
    @Autowired
    private EmpEvalScorerRepo empEvalScorerRepo;
    @Autowired
    private KpiEmpDao kpiEmpDao;
    @Autowired
    private OnScoreEvalRepo onScoreEvalRepo;
    @Autowired
    private TransactionWrap tx;
    @Autowired
    private EmpEvalRuleRepoImpl empRuleRepo;
    @Autowired
    private CompanyDaoImpl companyDao;

    // 新增：优化位图管理组件
    @Autowired(required = false)
    private ConcurrentBitmapManager bitmapManager;
    @Autowired(required = false)
    private RecordIndexCalculator indexCalculator;

    /**
     * 是否启用位图优化
     */
    @Value("${scorer.migration.use.bitmap:false}")
    private boolean useBitmapOptimization;

    private static final String TASK_BITMAP_FILE_PATH = "task_bitmap_with_log.ser";

    @PostConstruct
    public void init() {
        // 从文件中加载任务状态信息
        loadTaskStatusFromFile();
    }
    private void loadTaskStatusFromFile() {
        File file = new File(TASK_BITMAP_FILE_PATH);
        if (file.exists()) {
            try {
                bitmapWithLog = TaskBitmapWithLog.loadFromFile(TASK_BITMAP_FILE_PATH);
                log.info("Loaded task status from file: {}", TASK_BITMAP_FILE_PATH);
            } catch (IOException | ClassNotFoundException e) {
                log.error("Failed to load task status from file: {}", TASK_BITMAP_FILE_PATH, e);
                // 初始化一个新的 TaskBitmapWithLog 对象
                bitmapWithLog = new TaskBitmapWithLog(0, 0);
            }
        } else {
            log.info("Task status file not found: {}. Initializing a new TaskBitmapWithLog object.", TASK_BITMAP_FILE_PATH);
            bitmapWithLog = new TaskBitmapWithLog(0, 0);
        }
    }

    public void migrateFinished(String tid,String companyId, String taskUserId) {
        MDC.put("tid", tid);
        //已完成考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskFinished(); // 处理已完成的，rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())){
                // 更新 TaskBitmapWithLog 的状态为已完成但失败
                saveTaskFinishedFailure(tenantId,taskUserId);
                log.warn("Task finished with failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            // 更新 TaskBitmapWithLog 的状态为已完成且成功
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.info("Task finished successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            // 更新 TaskBitmapWithLog 的状态为已完成但失败
            saveTaskFinishedFailure(tenantId, taskUserId);
            log.error("Task finished with failure for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }

    // 任务ID生成方法（关键：保证全局唯一性）
    private String generateTaskId(String companyId, String taskUserId) {
        return companyId + "_" + taskUserId + "_" + System.currentTimeMillis();
    }
    @Transactional
    public void migrateFinishedOne(String companyId, String taskUserId) {
        //已完成考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        dmSvc.handlerTaskFinished(); // 处理已完成的，rs数据合并+计算
        if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
            // 更新 TaskBitmapWithLog 的状态为已完成但失败
            log.warn("migrateFinishedOne  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }

        List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
        Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId), scorerIds);
        dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
        tx.runTran(() -> {
            empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
            onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
            empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
        });
        log.info("  migrateFinishedOneTask successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
    }

    @Transactional
    public void migrateNoFinishOne(String companyId, String taskUserId) {
        //未完成的考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskNoFinished(); // 处理未完成的，解析+rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
                log.warn("Task no finished  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
            Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId), scorerIds);
            dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            log.info("Task no finished for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            log.error("Task  no finished for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }

    private void saveTaskFinishedFailure( TenantId tenantId, String taskUserId) {
        // 更新 TaskBitmapWithLog 的状态为已完成但失败
        bitmapWithLog.setTaskFinishedFailure(tenantId, taskUserId);
        // 持久化任务状态到文件
        saveTaskStatusToFile();
    }

    public void migrateNoFinish(String tid, String companyId, String taskUserId) {
        MDC.put("tid", tid);
        //未完成的考核
        TenantId tenantId = new TenantId(companyId);
        ScorerDataMingrationDmSvc dmSvc = onScoreEvalRepo.getOnScoreEvalMingration(tenantId, taskUserId);
        if (dmSvc.isNoNeedUp()){
            bitmapWithLog.setTaskFinishedSuccess(tenantId, taskUserId);
            log.warn("[此任务无需更新]Task finished with successfully for companyId: {}, taskUserId: {}", companyId, taskUserId);
            return;
        }
        BigDecimal fullScoreValue = companyDao.findFullScoreValue(new TenantId(companyId));
        CompanyConf conf = companyDao.findCompanyConf(new TenantId(companyId));
        dmSvc.getEvalMerge().setCompanyConf(conf);
        //自定义满分分值 如果为null 则使用系统提供分值
        if (dmSvc.getEvalMerge().getScoreValueConf().getCustomFullScore() == null) {
            dmSvc.getEvalMerge().getScoreValueConf().setCustomFullScore(fullScoreValue);
        }
        try {
            dmSvc.handlerTaskNoFinished(); // 处理未完成的，解析+rs数据合并+计算
            if (CollUtil.isEmpty(dmSvc.getEmpEvalScorers())) {
                // 更新 TaskBitmapWithLog 的状态为已完成但失败
                log.warn("Task no finished  failure for companyId: {}, taskUserId: {}", companyId, taskUserId);
                return;
            }
            List<String> scorerIds = dmSvc.getEmpEvalScorers().stream().map(EmpEvalScorer::getScorerId).collect(Collectors.toList());
            Map<String, KpiEmp> kpiMap = kpiEmpDao.listByEmpAsMap(new TenantId(companyId),scorerIds);
            dmSvc.accScorerInfo(kpiMap);//接收评分人姓名头像。
            tx.runTran(() -> {
                empEvalScorerRepo.batchAddEmpEvalScorer(companyId, taskUserId, dmSvc.getEmpEvalScorers());
                onScoreEvalRepo.saveEvalUserNodeScore(dmSvc.getTaskUser(), dmSvc.getEvalMerge().getKpiTypes(), dmSvc.getTaskUser().getCreatedUser());
                empRuleRepo.updateSuperiorScoreOrder(tenantId, taskUserId,dmSvc.getSuperiorScoreOrder());
            });
            // 记录 TaskBitmapWithLog 的状态为已完成
            bitmapWithLog.setTaskNoFinishedSuccess(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.info("Task no finished for companyId: {}, taskUserId: {}", companyId, taskUserId);
        } catch (Exception e) {
            // 记录 TaskBitmapWithLog 的状态为未执行成功
            bitmapWithLog.setTaskNoFinishedFailure(tenantId, taskUserId);
            // 持久化任务状态到文件
            saveTaskStatusToFile();
            log.error("Task  no finished for companyId: {}, taskUserId: {}", companyId, taskUserId, e);
        }
    }
    private void saveTaskStatusToFile() {
        try {
            bitmapWithLog.saveToFile(TASK_BITMAP_FILE_PATH);
            log.info("Saved task status to file: {}", TASK_BITMAP_FILE_PATH);
        } catch (IOException e) {
            log.error("Failed to save task status to file: {}", TASK_BITMAP_FILE_PATH, e);
        }
    }

    public void logTaskStatus() {
        log.info("Finished success tasks count: {}", bitmapWithLog.getFinishedSuccessTaskCount());
        log.info("Finished failure tasks count: {}", bitmapWithLog.getFinishedFailureTaskCount());
        log.info("No finished tasks count: {}", bitmapWithLog.getNoFinishedTaskCount());
        log.info("No finished failure tasks count: {}", bitmapWithLog.getNoFinishedFailureTaskCount());
        List<String> noFinishedFailurTasks = bitmapWithLog.getNoFinishedFailureTasks();
        if (!noFinishedFailurTasks.isEmpty()) {
            log.info("Not Finished tasks: {}", noFinishedFailurTasks);
        }
        List<String> finishedFailureTasks = bitmapWithLog.getFinishedFailureTasks();
        if (!finishedFailureTasks.isEmpty()) {
            log.info("Finished failure tasks: {}", finishedFailureTasks);
        }
    }

    // ==================== 新增的优化方法 ====================

    /**
     * 启动优化的大规模数据迁移
     * 支持131万条记录的分批处理和断点续传
     *
     * @param migrationType 迁移类型（FINISHED/NO_FINISHED）
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startOptimizedMigration(String migrationType, String operatorId) {
        log.info("Starting optimized migration: type={}, operator={}, useBitmap={}",
                migrationType, operatorId, useBitmapOptimization);

        if (!useBitmapOptimization || bitmapManager == null || indexCalculator == null) {
            log.warn("Bitmap optimization not enabled or components not available, falling back to original method");
            return startOriginalMigration(migrationType, operatorId);
        }

        try {
            // 生成会话ID
            String sessionId = generateOptimizedSessionId();

            // 创建优化的位图进度管理器
            OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);

            // 初始化总数据量
            initializeOptimizedMigrationCounts(progress);

            // 保存初始进度
            progress.saveMetadata();

            // 异步执行迁移
            executeOptimizedMigrationAsync(sessionId);

            log.info("Optimized migration started successfully: sessionId={}", sessionId);
            return sessionId;

        } catch (Exception e) {
            log.error("Failed to start optimized migration: type={}, operator={}", migrationType, operatorId, e);
            throw new RuntimeException("启动优化迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 恢复中断的优化迁移任务
     *
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeOptimizedMigration(String sessionId) {
        log.info("Attempting to resume optimized migration: {}", sessionId);

        if (!useBitmapOptimization || bitmapManager == null) {
            log.warn("Bitmap optimization not enabled, cannot resume optimized migration");
            return false;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return false;
            }

            if (!progress.canResume()) {
                log.warn("Migration cannot be resumed, current status: {}", progress.getStatus());
                return false;
            }

            // 恢复迁移
            progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
            progress.saveMetadata();

            // 异步继续执行
            executeOptimizedMigrationAsync(sessionId);

            log.info("Optimized migration resumed successfully: {}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("Failed to resume optimized migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 暂停优化迁移任务
     *
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseOptimizedMigration(String sessionId) {
        log.info("Pausing optimized migration: {}", sessionId);

        if (!useBitmapOptimization) {
            log.warn("Bitmap optimization not enabled, cannot pause optimized migration");
            return false;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null || !progress.isRunning()) {
                return false;
            }

            progress.pause();
            log.info("Optimized migration paused successfully: {}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("Failed to pause optimized migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取优化迁移状态
     *
     * @param sessionId 会话ID
     * @return 迁移状态
     */
    public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId) {
        if (!useBitmapOptimization) {
            return null;
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                log.debug("Optimized migration status: {}", progress.getStatistics());
            }
            return progress;

        } catch (Exception e) {
            log.error("Failed to get optimized migration status: {}", sessionId, e);
            return null;
        }
    }

    /**
     * 异步执行优化的迁移任务
     *
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public void executeOptimizedMigrationAsync(String sessionId) {
        try {
            log.info("Starting async optimized migration execution for session: {}", sessionId);

            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return;
            }

            // 开始迁移
            if (progress.getStatus() == OptimizedBitmapProgress.MigrationStatus.PENDING) {
                progress.start();
                progress.saveMetadata();
            }

            // 执行优化的迁移逻辑
            executeOptimizedMigrationLogic(progress);

            log.info("Optimized migration completed successfully for session: {}", sessionId);

        } catch (Exception e) {
            log.error("Optimized migration failed for session: {}", sessionId, e);
            handleOptimizedMigrationFailure(sessionId, e.getMessage());
        }
    }

    /**
     * 启动原有的迁移方法（保持向后兼容）
     */
    private String startOriginalMigration(String migrationType, String operatorId) {
        // 这里可以调用您原有的迁移逻辑
        log.info("Starting original migration: type={}, operator={}", migrationType, operatorId);
        return generateOptimizedSessionId();
    }

    /**
     * 生成优化的会话ID
     */
    private String generateOptimizedSessionId() {
        return "OPTIMIZED_SCORER_MIGRATION_" + System.currentTimeMillis() + "_" +
               java.util.UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 初始化优化迁移数据量
     */
    private void initializeOptimizedMigrationCounts(OptimizedBitmapProgress progress) {
        try {
            String migrationType = progress.getMigrationType();

            // 使用OnScoreEvalRepo获取总数据量
            long totalRecords = onScoreEvalRepo.getTotalMigrationRecordCount(migrationType);
            progress.getTotalRecords().set(totalRecords);

            log.info("Initialized optimized migration counts for session: {}, type: {}, total: {}",
                    progress.getSessionId(), migrationType, totalRecords);

        } catch (Exception e) {
            log.error("Failed to initialize optimized migration counts for session: {}", progress.getSessionId(), e);
            throw new RuntimeException("初始化优化迁移数据量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理优化迁移失败
     */
    private void handleOptimizedMigrationFailure(String sessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                progress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to update optimized migration failure status for session: {}", sessionId, e);
        }
    }

    /**
     * 执行优化的迁移逻辑（核心方法）
     */
    private void executeOptimizedMigrationLogic(OptimizedBitmapProgress progress) {
        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();

        // 配置参数
        int companyPageSize = 10;
        int userPageSize = 1000;

        // 从断点位置开始处理
        int startCompanyPage = progress.getCurrentCompanyPage();

        log.info("Resuming optimized migration from company page: {}", startCompanyPage);

        for (int companyPage = startCompanyPage; ; companyPage++) {
            // 检查是否需要暂停
            if (shouldPauseOptimized(progress)) {
                log.info("Optimized migration paused at company page: {}", companyPage);
                break;
            }

            // 使用OnScoreEvalRepo获取公司列表
            List<String> companyIds = onScoreEvalRepo.getCompanyIdsByPage(migrationType, companyPage, companyPageSize);
            if (companyIds.isEmpty()) {
                log.info("No more companies to process, breaking at page: {}", companyPage);
                break;
            }

            // 处理每个公司
            for (String companyId : companyIds) {
                processCompanyWithOptimizedBitmap(progress, companyId, userPageSize);
                progress.updatePosition(companyId, companyPage, 1);
            }

            // 更新当前页码
            progress.updatePosition(null, companyPage + 1, 1);

            // 定期保存进度
            if (companyPage % 5 == 0) {
                progress.saveMetadata();
                bitmapManager.flushAllSegments(sessionId);
                log.info("Optimized progress saved at company page: {}", companyPage);
            }
        }

        // 完成迁移
        if (isOptimizedAllProcessed(progress)) {
            progress.complete();
            log.info("Optimized migration completed successfully for session: {}", progress.getSessionId());
        }
    }

    /**
     * 使用优化位图处理单个公司
     */
    private void processCompanyWithOptimizedBitmap(OptimizedBitmapProgress progress, String companyId, int userPageSize) {
        log.debug("Processing company with optimized bitmap: {}", companyId);

        String migrationType = progress.getMigrationType();
        String sessionId = progress.getSessionId();

        // 确定起始用户页
        int startUserPage = companyId.equals(progress.getCurrentCompanyId()) ?
                progress.getCurrentUserPage() : 1;

        for (int userPage = startUserPage; ; userPage++) {
            // 使用OnScoreEvalRepo获取用户列表
            List<String> userIds = onScoreEvalRepo.getUserIdsByPage(migrationType, companyId, userPage, userPageSize);
            if (userIds.isEmpty()) {
                log.debug("No more users for company: {}, page: {}", companyId, userPage);
                break;
            }

            // 位图检查：过滤已处理的用户
            List<String> usersToProcess = new ArrayList<>();
            List<Long> indexesToProcess = new ArrayList<>();

            for (int i = 0; i < userIds.size(); i++) {
                String userId = userIds.get(i);

                // 计算全局索引
                long globalIndex = indexCalculator.calculateGlobalIndex(
                        companyId, progress.getCurrentCompanyPage(), userPage, i, 10, userPageSize);

                // 位图检查：如果已处理则跳过
                if (!bitmapManager.getBit(sessionId, globalIndex, progress.getSegmentSize())) {
                    usersToProcess.add(userId);
                    indexesToProcess.add(globalIndex);
                } else {
                    log.debug("User already processed, skipping: companyId={}, userId={}, index={}",
                            companyId, userId, globalIndex);
                }
            }

            if (!usersToProcess.isEmpty()) {
                // 批量处理用户
                processOptimizedBatchUsers(progress, companyId, usersToProcess, indexesToProcess, migrationType);
            } else {
                log.debug("All users in this page already processed: companyId={}, page={}", companyId, userPage);
            }

            // 更新用户页位置
            progress.updatePosition(companyId, progress.getCurrentCompanyPage(), userPage + 1);

            // 定期保存进度
            if (userPage % 10 == 0) {
                progress.saveMetadata();
            }
        }
    }

    /**
     * 批量处理用户（优化版本）
     */
    private void processOptimizedBatchUsers(OptimizedBitmapProgress progress, String companyId,
                                          List<String> userIds, List<Long> indexes, String migrationType) {

        String sessionId = progress.getSessionId();

        try {
            log.debug("Processing optimized batch users: companyId={}, count={}, type={}",
                    companyId, userIds.size(), migrationType);

            // 尝试批量处理
            boolean batchSuccess = false;

            if ("FINISHED".equals(migrationType)) {
                batchSuccess = processOptimizedBatchFinishedUsers(companyId, userIds);
            } else if ("NO_FINISHED".equals(migrationType)) {
                batchSuccess = processOptimizedBatchNoFinishedUsers(companyId, userIds);
            }

            if (batchSuccess) {
                // 批量成功：更新所有位图
                for (int i = 0; i < indexes.size(); i++) {
                    Long globalIndex = indexes.get(i);
                    bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                    progress.markProcessed(globalIndex, true);
                }

                log.info("Successfully processed optimized batch users: companyId={}, count={}", companyId, userIds.size());

            } else {
                // 批量失败：逐个处理
                log.warn("Optimized batch processing failed, falling back to individual processing: companyId={}", companyId);
                processOptimizedIndividualUsers(progress, companyId, userIds, indexes, migrationType);
            }

        } catch (Exception e) {
            log.error("Error in optimized batch processing, falling back to individual processing: companyId={}", companyId, e);
            processOptimizedIndividualUsers(progress, companyId, userIds, indexes, migrationType);
        }
    }

    /**
     * 批量处理已完成用户（优化版本）
     */
    private boolean processOptimizedBatchFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Optimized batch processing finished users: companyId={}, count={}", companyId, userIds.size());

            // 批量调用您现有的migrateFinishedOne方法
            for (String userId : userIds) {
                migrateFinishedOne(companyId, userId);
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to process optimized batch finished users: companyId={}, userCount={}",
                    companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 批量处理未完成用户（优化版本）
     */
    private boolean processOptimizedBatchNoFinishedUsers(String companyId, List<String> userIds) {
        try {
            log.debug("Optimized batch processing no-finished users: companyId={}, count={}", companyId, userIds.size());

            // 批量调用您现有的migrateNoFinishOne方法
            for (String userId : userIds) {
                migrateNoFinishOne(companyId, userId);
            }

            return true;

        } catch (Exception e) {
            log.error("Failed to process optimized batch no-finished users: companyId={}, userCount={}",
                    companyId, userIds.size(), e);
            return false;
        }
    }

    /**
     * 逐个处理用户（优化版本）
     */
    private void processOptimizedIndividualUsers(OptimizedBitmapProgress progress, String companyId,
                                               List<String> userIds, List<Long> indexes, String migrationType) {

        String sessionId = progress.getSessionId();

        log.debug("Processing optimized individual users: companyId={}, count={}", companyId, userIds.size());

        for (int i = 0; i < userIds.size(); i++) {
            String userId = userIds.get(i);
            Long globalIndex = indexes.get(i);

            try {
                // 处理单个用户
                boolean success = processOptimizedIndividualUser(companyId, userId, migrationType);

                // 更新位图
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, success);

                if (success) {
                    log.debug("Successfully processed optimized individual user: companyId={}, userId={}", companyId, userId);
                } else {
                    log.warn("Failed to process optimized individual user: companyId={}, userId={}", companyId, userId);
                }

            } catch (Exception e) {
                log.error("Error processing optimized individual user: companyId={}, userId={}", companyId, userId, e);

                // 即使失败也要标记为已处理，避免重复处理
                bitmapManager.setBit(sessionId, globalIndex, progress.getSegmentSize());
                progress.markProcessed(globalIndex, false);
            }
        }
    }

    /**
     * 处理单个用户（优化版本）
     */
    private boolean processOptimizedIndividualUser(String companyId, String userId, String migrationType) {
        try {
            if ("FINISHED".equals(migrationType)) {
                migrateFinishedOne(companyId, userId);
                return true;
            } else if ("NO_FINISHED".equals(migrationType)) {
                migrateNoFinishOne(companyId, userId);
                return true;
            }
            return false;

        } catch (Exception e) {
            log.error("Failed to process optimized individual user: companyId={}, userId={}, type={}",
                    companyId, userId, migrationType, e);
            return false;
        }
    }

    // ==================== 失败记录跟踪和重试功能 ====================

    /**
     * 处理单个用户并记录失败信息
     */
    private boolean processOptimizedIndividualUserWithFailureTracking(OptimizedBitmapProgress progress,
                                                                     String companyId, String userId,
                                                                     long globalIndex, String migrationType) {
        try {
            boolean success = processOptimizedIndividualUser(companyId, userId, migrationType);

            if (!success) {
                // 记录失败信息
                String errorType = "BUSINESS_LOGIC_ERROR";
                String errorMessage = "业务逻辑处理失败";
                progress.recordFailure(userId, companyId, globalIndex, errorMessage, errorType);
            }

            return success;

        } catch (Exception e) {
            // 记录异常失败信息
            String errorType = getErrorType(e);
            String errorMessage = e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName();
            progress.recordFailure(userId, companyId, globalIndex, errorMessage, errorType);

            log.error("Failed to process user with failure tracking: companyId={}, userId={}, globalIndex={}",
                    companyId, userId, globalIndex, e);
            return false;
        }
    }

    /**
     * 获取错误类型
     */
    private String getErrorType(Exception e) {
        if (e instanceof java.sql.SQLException) {
            return "DATABASE_ERROR";
        } else if (e instanceof java.net.SocketTimeoutException) {
            return "TIMEOUT_ERROR";
        } else if (e instanceof java.lang.NullPointerException) {
            return "NULL_POINTER_ERROR";
        } else if (e instanceof java.lang.IllegalArgumentException) {
            return "INVALID_ARGUMENT_ERROR";
        } else {
            return "UNKNOWN_ERROR";
        }
    }

    /**
     * 启动失败记录重试
     *
     * @param sessionId 原始会话ID
     * @param userIds 指定要重试的用户ID列表（可选，为空则重试所有失败记录）
     * @return 重试会话ID
     */
    public String startFailureRetry(String sessionId, List<String> userIds) {
        log.info("Starting failure retry for session: {}, userIds: {}", sessionId, userIds);

        try {
            // 加载原始进度
            OptimizedBitmapProgress originalProgress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (originalProgress == null) {
                throw new RuntimeException("原始迁移会话不存在: " + sessionId);
            }

            // 获取失败记录
            List<OptimizedBitmapProgress.FailureRecord> failureRecords;
            if (userIds != null && !userIds.isEmpty()) {
                // 重试指定用户
                failureRecords = userIds.stream()
                        .map(originalProgress::getFailureRecord)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                // 重试所有失败记录
                failureRecords = originalProgress.getAllFailureRecords();
            }

            if (failureRecords.isEmpty()) {
                throw new RuntimeException("没有找到需要重试的失败记录");
            }

            // 创建重试会话
            String retrySessionId = generateRetrySessionId(sessionId);
            OptimizedBitmapProgress retryProgress = new OptimizedBitmapProgress(retrySessionId, originalProgress.getMigrationType());
            retryProgress.getTotalRecords().set(failureRecords.size());
            retryProgress.saveMetadata();

            // 异步执行重试
            executeFailureRetryAsync(retrySessionId, failureRecords, originalProgress);

            log.info("Failure retry started successfully: retrySessionId={}, retryCount={}", retrySessionId, failureRecords.size());
            return retrySessionId;

        } catch (Exception e) {
            log.error("Failed to start failure retry for session: {}", sessionId, e);
            throw new RuntimeException("启动失败重试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步执行失败记录重试
     */
    @Async("migrationTaskExecutor")
    public void executeFailureRetryAsync(String retrySessionId, List<OptimizedBitmapProgress.FailureRecord> failureRecords,
                                        OptimizedBitmapProgress originalProgress) {
        try {
            log.info("Starting async failure retry execution: retrySessionId={}, retryCount={}", retrySessionId, failureRecords.size());

            OptimizedBitmapProgress retryProgress = OptimizedBitmapProgress.loadMetadata(retrySessionId);
            if (retryProgress == null) {
                log.error("Retry progress not found: {}", retrySessionId);
                return;
            }

            retryProgress.start();
            retryProgress.saveMetadata();

            // 执行重试逻辑
            executeFailureRetryLogic(retryProgress, failureRecords, originalProgress);

            retryProgress.complete();
            log.info("Failure retry completed successfully: retrySessionId={}", retrySessionId);

        } catch (Exception e) {
            log.error("Failure retry failed: retrySessionId={}", retrySessionId, e);
            handleFailureRetryFailure(retrySessionId, e.getMessage());
        }
    }

    /**
     * 执行失败记录重试逻辑
     */
    private void executeFailureRetryLogic(OptimizedBitmapProgress retryProgress,
                                        List<OptimizedBitmapProgress.FailureRecord> failureRecords,
                                        OptimizedBitmapProgress originalProgress) {

        String migrationType = retryProgress.getMigrationType();
        String retrySessionId = retryProgress.getSessionId();

        for (int i = 0; i < failureRecords.size(); i++) {
            OptimizedBitmapProgress.FailureRecord failureRecord = failureRecords.get(i);

            // 检查是否需要暂停
            if (shouldPauseOptimized(retryProgress)) {
                log.info("Failure retry paused at record: {}", i);
                break;
            }

            String userId = failureRecord.getUserId();
            String companyId = failureRecord.getCompanyId();
            long globalIndex = failureRecord.getGlobalIndex();

            try {
                log.info("Retrying failed record: userId={}, companyId={}, globalIndex={}, retryCount={}",
                        userId, companyId, globalIndex, failureRecord.getRetryCount());

                // 更新原始记录的重试次数
                originalProgress.incrementRetryCount(userId);

                // 执行重试
                boolean success = processOptimizedIndividualUser(companyId, userId, migrationType);

                if (success) {
                    // 重试成功：从原始进度中移除失败记录
                    originalProgress.removeFailureRecord(userId);

                    // 更新重试进度
                    retryProgress.markProcessed(i, true);

                    log.info("Retry successful for user: userId={}, companyId={}", userId, companyId);

                } else {
                    // 重试仍然失败
                    retryProgress.recordFailure(userId, companyId, i, "重试仍然失败", "RETRY_FAILED");
                    retryProgress.markProcessed(i, false);

                    log.warn("Retry failed for user: userId={}, companyId={}", userId, companyId);
                }

            } catch (Exception e) {
                // 重试异常
                String errorType = getErrorType(e);
                String errorMessage = "重试异常: " + (e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName());

                retryProgress.recordFailure(userId, companyId, i, errorMessage, errorType);
                retryProgress.markProcessed(i, false);

                log.error("Retry exception for user: userId={}, companyId={}", userId, companyId, e);
            }

            // 定期保存进度
            if (i % 10 == 0) {
                retryProgress.saveMetadata();
                originalProgress.saveMetadata();
            }
        }

        // 最终保存
        retryProgress.saveMetadata();
        originalProgress.saveMetadata();
    }

    /**
     * 获取失败记录列表
     *
     * @param sessionId 会话ID
     * @param companyId 公司ID（可选）
     * @param errorType 错误类型（可选）
     * @return 失败记录列表
     */
    public List<OptimizedBitmapProgress.FailureRecord> getFailureRecords(String sessionId, String companyId, String errorType) {
        log.debug("Getting failure records: sessionId={}, companyId={}, errorType={}", sessionId, companyId, errorType);

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return new ArrayList<>();
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getAllFailureRecords();

            // 按公司ID过滤
            if (companyId != null && !companyId.trim().isEmpty()) {
                failureRecords = failureRecords.stream()
                        .filter(record -> companyId.equals(record.getCompanyId()))
                        .collect(Collectors.toList());
            }

            // 按错误类型过滤
            if (errorType != null && !errorType.trim().isEmpty()) {
                failureRecords = failureRecords.stream()
                        .filter(record -> errorType.equals(record.getErrorType()))
                        .collect(Collectors.toList());
            }

            return failureRecords;

        } catch (Exception e) {
            log.error("Failed to get failure records: sessionId={}", sessionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取失败记录统计信息
     *
     * @param sessionId 会话ID
     * @return 失败统计信息
     */
    public String getFailureStatistics(String sessionId) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                return "迁移会话不存在";
            }

            return progress.getFailureStatistics();

        } catch (Exception e) {
            log.error("Failed to get failure statistics: sessionId={}", sessionId, e);
            return "获取失败统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 生成重试会话ID
     */
    private String generateRetrySessionId(String originalSessionId) {
        return "RETRY_" + originalSessionId + "_" + System.currentTimeMillis();
    }

    /**
     * 处理失败重试失败
     */
    private void handleFailureRetryFailure(String retrySessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress retryProgress = OptimizedBitmapProgress.loadMetadata(retrySessionId);
            if (retryProgress != null) {
                retryProgress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                retryProgress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to handle failure retry failure: retrySessionId={}", retrySessionId, e);
        }
    }

    /**
     * 检查是否应该暂停优化迁移
     */
    private boolean shouldPauseOptimized(OptimizedBitmapProgress progress) {
        OptimizedBitmapProgress latest = OptimizedBitmapProgress.loadMetadata(progress.getSessionId());
        return latest != null && latest.getStatus() == OptimizedBitmapProgress.MigrationStatus.PAUSED;
    }

    /**
     * 检查优化迁移是否全部处理完成
     */
    private boolean isOptimizedAllProcessed(OptimizedBitmapProgress progress) {
        return progress.getProcessedCount().get() >= progress.getTotalRecords().get();
    }

    /**
     * 清理优化迁移文件
     */
    public boolean cleanupOptimizedMigrationFiles(String sessionId) {
        log.info("Cleaning up optimized migration files for session: {}", sessionId);

        if (!useBitmapOptimization || bitmapManager == null) {
            return false;
        }

        try {
            bitmapManager.cleanupSession(sessionId);
            return true;
        } catch (Exception e) {
            log.error("Failed to cleanup optimized migration files for session: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取优化迁移统计信息
     */
    public String getOptimizedMigrationStatistics(String sessionId) {
        if (!useBitmapOptimization) {
            return "Bitmap optimization not enabled";
        }

        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            return progress != null ? progress.getStatistics() : "Migration not found";
        } catch (Exception e) {
            log.error("Failed to get optimized migration statistics for session: {}", sessionId, e);
            return "Error getting statistics: " + e.getMessage();
        }
    }
}
