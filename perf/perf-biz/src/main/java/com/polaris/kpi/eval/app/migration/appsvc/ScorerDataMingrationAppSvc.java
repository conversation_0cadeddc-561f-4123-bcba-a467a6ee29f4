package com.polaris.kpi.eval.app.migration.appsvc;

import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.migration.util.ConcurrentBitmapManager;
import com.polaris.kpi.eval.domain.migration.util.RecordIndexCalculator;
import com.polaris.kpi.eval.domain.migration.dmsvc.ScorerDataMingrationDmSvc;
import com.polaris.kpi.eval.domain.task.repo.OnScoreEvalRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * 评分数据迁移应用服务
 * 基于现有设计优化，支持大规模数据分批处理和断点续传
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScorerDataMingrationAppSvc {

    private final ScorerDataMingrationDmSvc scorerDataMingrationDmSvc;
    private final OnScoreEvalRepo onScoreEvalRepo;
    private final ConcurrentBitmapManager bitmapManager;
    private final RecordIndexCalculator indexCalculator;

    /**
     * 是否启用位图优化
     */
    @Value("${scorer.migration.use.bitmap:false}")
    private boolean useBitmapOptimization;

    /**
     * 启动评分数据迁移
     * 支持原有方式和优化方式的切换
     * 
     * @param migrationType 迁移类型（FINISHED/NO_FINISHED）
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startScorerMigration(String migrationType, String operatorId) {
        log.info("Starting scorer migration: type={}, operator={}, useBitmap={}", 
                migrationType, operatorId, useBitmapOptimization);

        if (useBitmapOptimization) {
            return startOptimizedScorerMigration(migrationType, operatorId);
        } else {
            return startOriginalScorerMigration(migrationType, operatorId);
        }
    }

    /**
     * 启动优化的评分数据迁移
     * 使用位图管理和断点续传
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startOptimizedScorerMigration(String migrationType, String operatorId) {
        log.info("Starting optimized scorer migration: type={}, operator={}", migrationType, operatorId);

        try {
            // 生成会话ID
            String sessionId = generateSessionId();
            
            // 创建优化的位图进度管理器
            OptimizedBitmapProgress progress = new OptimizedBitmapProgress(sessionId, migrationType);
            
            // 初始化总数据量
            initializeMigrationCounts(progress);
            
            // 保存初始进度
            progress.saveMetadata();
            
            // 异步执行迁移
            executeOptimizedMigrationAsync(sessionId);
            
            log.info("Optimized scorer migration started successfully: sessionId={}", sessionId);
            return sessionId;
            
        } catch (Exception e) {
            log.error("Failed to start optimized scorer migration: type={}, operator={}", migrationType, operatorId, e);
            throw new RuntimeException("启动优化迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启动原有的评分数据迁移
     * 保持原有逻辑不变
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 会话ID
     */
    public String startOriginalScorerMigration(String migrationType, String operatorId) {
        log.info("Starting original scorer migration: type={}, operator={}", migrationType, operatorId);
        
        try {
            String sessionId = generateSessionId();
            
            // 调用原有的迁移逻辑
            scorerDataMingrationDmSvc.executeOriginalMigration(sessionId, migrationType, operatorId);
            
            log.info("Original scorer migration started successfully: sessionId={}", sessionId);
            return sessionId;
            
        } catch (Exception e) {
            log.error("Failed to start original scorer migration: type={}, operator={}", migrationType, operatorId, e);
            throw new RuntimeException("启动原有迁移失败: " + e.getMessage(), e);
        }
    }

    /**
     * 恢复中断的评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeScorerMigration(String sessionId) {
        log.info("Attempting to resume scorer migration: {}", sessionId);

        try {
            if (useBitmapOptimization) {
                return resumeOptimizedScorerMigration(sessionId);
            } else {
                return resumeOriginalScorerMigration(sessionId);
            }
        } catch (Exception e) {
            log.error("Failed to resume scorer migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 恢复优化的评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeOptimizedScorerMigration(String sessionId) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null) {
            log.error("Migration progress not found for session: {}", sessionId);
            return false;
        }

        if (!progress.canResume()) {
            log.warn("Migration cannot be resumed, current status: {}", progress.getStatus());
            return false;
        }

        // 恢复迁移
        progress.setStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        progress.saveMetadata();

        // 异步继续执行
        executeOptimizedMigrationAsync(sessionId);

        log.info("Optimized scorer migration resumed successfully: {}", sessionId);
        return true;
    }

    /**
     * 恢复原有的评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功恢复
     */
    public boolean resumeOriginalScorerMigration(String sessionId) {
        // 调用原有的恢复逻辑
        return scorerDataMingrationDmSvc.resumeOriginalMigration(sessionId);
    }

    /**
     * 暂停评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseScorerMigration(String sessionId) {
        log.info("Pausing scorer migration: {}", sessionId);

        try {
            if (useBitmapOptimization) {
                return pauseOptimizedScorerMigration(sessionId);
            } else {
                return pauseOriginalScorerMigration(sessionId);
            }
        } catch (Exception e) {
            log.error("Failed to pause scorer migration: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 暂停优化的评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseOptimizedScorerMigration(String sessionId) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress == null || !progress.isRunning()) {
            return false;
        }

        progress.pause();
        log.info("Optimized scorer migration paused successfully: {}", sessionId);
        return true;
    }

    /**
     * 暂停原有的评分迁移任务
     * 
     * @param sessionId 会话ID
     * @return 是否成功暂停
     */
    public boolean pauseOriginalScorerMigration(String sessionId) {
        // 调用原有的暂停逻辑
        return scorerDataMingrationDmSvc.pauseOriginalMigration(sessionId);
    }

    /**
     * 获取评分迁移状态
     * 
     * @param sessionId 会话ID
     * @return 迁移状态信息
     */
    public Object getScorerMigrationStatus(String sessionId) {
        log.debug("Getting scorer migration status: {}", sessionId);

        try {
            if (useBitmapOptimization) {
                return getOptimizedMigrationStatus(sessionId);
            } else {
                return getOriginalMigrationStatus(sessionId);
            }
        } catch (Exception e) {
            log.error("Failed to get scorer migration status: {}", sessionId, e);
            return null;
        }
    }

    /**
     * 获取优化迁移状态
     * 
     * @param sessionId 会话ID
     * @return 优化迁移状态
     */
    public OptimizedBitmapProgress getOptimizedMigrationStatus(String sessionId) {
        OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
        if (progress != null) {
            log.debug("Optimized migration status: {}", progress.getStatistics());
        }
        return progress;
    }

    /**
     * 获取原有迁移状态
     * 
     * @param sessionId 会话ID
     * @return 原有迁移状态
     */
    public Object getOriginalMigrationStatus(String sessionId) {
        // 调用原有的状态查询逻辑
        return scorerDataMingrationDmSvc.getOriginalMigrationStatus(sessionId);
    }

    /**
     * 异步执行优化的迁移任务
     * 
     * @param sessionId 会话ID
     */
    @Async("migrationTaskExecutor")
    public CompletableFuture<Void> executeOptimizedMigrationAsync(String sessionId) {
        try {
            log.info("Starting async optimized migration execution for session: {}", sessionId);

            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress == null) {
                log.error("Migration progress not found for session: {}", sessionId);
                return CompletableFuture.completedFuture(null);
            }

            // 开始迁移
            if (progress.getStatus() == OptimizedBitmapProgress.MigrationStatus.PENDING) {
                progress.start();
                progress.saveMetadata();
            }

            // 执行优化的迁移逻辑
            scorerDataMingrationDmSvc.executeOptimizedMigration(progress, bitmapManager, indexCalculator);

            log.info("Optimized migration completed successfully for session: {}", sessionId);

        } catch (Exception e) {
            log.error("Optimized migration failed for session: {}", sessionId, e);
            handleOptimizedMigrationFailure(sessionId, e.getMessage());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 处理优化迁移失败
     * 
     * @param sessionId 会话ID
     * @param errorMessage 错误信息
     */
    private void handleOptimizedMigrationFailure(String sessionId, String errorMessage) {
        try {
            OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
            if (progress != null) {
                progress.setStatus(OptimizedBitmapProgress.MigrationStatus.FAILED);
                progress.saveMetadata();
            }
        } catch (Exception e) {
            log.error("Failed to update migration failure status for session: {}", sessionId, e);
        }
    }

    /**
     * 初始化迁移数据量
     * 
     * @param progress 迁移进度
     */
    private void initializeMigrationCounts(OptimizedBitmapProgress progress) {
        try {
            String migrationType = progress.getMigrationType();
            
            // 使用OnScoreEvalRepo获取总数据量
            long totalRecords = onScoreEvalRepo.getTotalMigrationRecordCount(migrationType);
            progress.getTotalRecords().set(totalRecords);
            
            log.info("Initialized migration counts for session: {}, type: {}, total: {}", 
                    progress.getSessionId(), migrationType, totalRecords);
                    
        } catch (Exception e) {
            log.error("Failed to initialize migration counts for session: {}", progress.getSessionId(), e);
            throw new RuntimeException("初始化迁移数据量失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成会话ID
     * 
     * @return 会话ID
     */
    private String generateSessionId() {
        return "SCORER_MIGRATION_" + System.currentTimeMillis() + "_" + 
               UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 清理已完成的迁移文件
     * 
     * @param sessionId 会话ID
     * @return 是否成功清理
     */
    public boolean cleanupMigrationFiles(String sessionId) {
        log.info("Cleaning up migration files for session: {}", sessionId);

        try {
            if (useBitmapOptimization) {
                bitmapManager.cleanupSession(sessionId);
                return true;
            } else {
                return scorerDataMingrationDmSvc.cleanupOriginalMigration(sessionId);
            }
        } catch (Exception e) {
            log.error("Failed to cleanup migration files for session: {}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取迁移统计信息
     * 
     * @param sessionId 会话ID
     * @return 统计信息
     */
    public String getMigrationStatistics(String sessionId) {
        try {
            if (useBitmapOptimization) {
                OptimizedBitmapProgress progress = OptimizedBitmapProgress.loadMetadata(sessionId);
                return progress != null ? progress.getStatistics() : "Migration not found";
            } else {
                return scorerDataMingrationDmSvc.getOriginalMigrationStatistics(sessionId);
            }
        } catch (Exception e) {
            log.error("Failed to get migration statistics for session: {}", sessionId, e);
            return "Error getting statistics: " + e.getMessage();
        }
    }
}
