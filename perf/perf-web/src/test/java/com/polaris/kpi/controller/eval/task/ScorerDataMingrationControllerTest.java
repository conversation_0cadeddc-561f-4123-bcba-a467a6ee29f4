package com.polaris.kpi.controller.eval.task;

import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.sdk.response.SingleResponse;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.time.LocalDateTime;
import java.util.*;

/**
 * ScorerDataMingrationController 单元测试
 */
@RunWith(PowerMockRunner.class)
public class ScorerDataMingrationControllerTest {

    @InjectMocks
    private ScorerDataMingrationController controller;

    @Mock
    private ScorerDataMingrationAppSvc mingrationAppSvc;

    private static final String TEST_SESSION_ID = "OPTIMIZED_SCORER_MIGRATION_1735459200000_a1b2c3d4";
    private static final String TEST_OPERATOR_ID = "test_operator_001";
    private static final String TEST_USER_ID = "test_user_001";
    private static final String TEST_COMPANY_ID = "test_company_001";

    @Before
    public void setUp() {
        // 准备测试数据
    }

    @After
    public void tearDown() {
        // 清理测试数据
    }

    // ==================== 基础迁移功能测试 ====================

    /**
     * 测试启动优化迁移 - 成功场景
     */
    @Test
    public void testStartOptimizedMigration_Success() {
        // Given
        String migrationType = "FINISHED";
        Mockito.when(mingrationAppSvc.startOptimizedMigration(migrationType, TEST_OPERATOR_ID))
                .thenReturn(TEST_SESSION_ID);

        // When
        SingleResponse response = controller.startOptimizedMigration(migrationType);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("会话ID应该匹配", TEST_SESSION_ID, response.getData());
        Mockito.verify(mingrationAppSvc).startOptimizedMigration(migrationType, TEST_OPERATOR_ID);
    }

    /**
     * 测试启动优化迁移 - 失败场景
     */
    @Test
    public void testStartOptimizedMigration_Failure() {
        // Given
        String migrationType = "FINISHED";
        Mockito.when(mingrationAppSvc.startOptimizedMigration(migrationType, TEST_OPERATOR_ID))
                .thenThrow(new RuntimeException("启动迁移失败"));

        // When
        SingleResponse response = controller.startOptimizedMigration(migrationType);

        // Then
        Assert.assertFalse("响应应该失败", response.isSuccess());
        Assert.assertEquals("错误码应该匹配", "START_OPTIMIZED_MIGRATION_ERROR", response.getErrCode());
        Assert.assertTrue("错误信息应该包含异常信息", response.getErrMessage().contains("启动迁移失败"));
    }

    /**
     * 测试获取优化迁移状态 - 成功场景
     */
    @Test
    public void testGetOptimizedMigrationStatus_Success() {
        // Given
        OptimizedBitmapProgress mockProgress = createMockProgress();
        Mockito.when(mingrationAppSvc.getOptimizedMigrationStatus(TEST_SESSION_ID))
                .thenReturn(mockProgress);

        // When
        SingleResponse response = controller.getOptimizedMigrationStatus(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("进度数据不应该为空", response.getData());
        Mockito.verify(mingrationAppSvc).getOptimizedMigrationStatus(TEST_SESSION_ID);
    }

    /**
     * 测试获取优化迁移状态 - 会话不存在
     */
    @Test
    public void testGetOptimizedMigrationStatus_NotFound() {
        // Given
        Mockito.when(mingrationAppSvc.getOptimizedMigrationStatus(TEST_SESSION_ID))
                .thenReturn(null);

        // When
        SingleResponse response = controller.getOptimizedMigrationStatus(TEST_SESSION_ID);

        // Then
        Assert.assertFalse("响应应该失败", response.isSuccess());
        Assert.assertEquals("错误码应该匹配", "MIGRATION_NOT_FOUND", response.getErrCode());
    }

    /**
     * 测试暂停优化迁移 - 成功场景
     */
    @Test
    public void testPauseOptimizedMigration_Success() {
        // Given
        Mockito.when(mingrationAppSvc.pauseOptimizedMigration(TEST_SESSION_ID))
                .thenReturn(true);

        // When
        SingleResponse response = controller.pauseOptimizedMigration(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("返回数据应该为true", true, response.getData());
        Mockito.verify(mingrationAppSvc).pauseOptimizedMigration(TEST_SESSION_ID);
    }

    /**
     * 测试恢复优化迁移 - 成功场景
     */
    @Test
    public void testResumeOptimizedMigration_Success() {
        // Given
        Mockito.when(mingrationAppSvc.resumeOptimizedMigration(TEST_SESSION_ID))
                .thenReturn(true);

        // When
        SingleResponse response = controller.resumeOptimizedMigration(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("返回数据应该为true", true, response.getData());
        Mockito.verify(mingrationAppSvc).resumeOptimizedMigration(TEST_SESSION_ID);
    }

    /**
     * 测试停止优化迁移 - 成功场景
     */
    @Test
    public void testStopOptimizedMigration_Success() {
        // Given
        Mockito.when(mingrationAppSvc.stopOptimizedMigration(TEST_SESSION_ID))
                .thenReturn(true);

        // When
        SingleResponse response = controller.stopOptimizedMigration(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("返回数据应该为true", true, response.getData());
        Mockito.verify(mingrationAppSvc).stopOptimizedMigration(TEST_SESSION_ID);
    }

    // ==================== 失败记录跟踪测试 ====================

    /**
     * 测试获取失败记录 - 成功场景
     */
    @Test
    public void testGetFailureRecords_Success() {
        // Given
        OptimizedBitmapProgress mockProgress = createMockProgress();
        List<OptimizedBitmapProgress.FailureRecord> mockFailureRecords = createMockFailureRecords();
        mockProgress.getFailureRecords().addAll(mockFailureRecords);
        
        Mockito.when(mingrationAppSvc.getOptimizedMigrationStatus(TEST_SESSION_ID))
                .thenReturn(mockProgress);

        // When
        SingleResponse response = controller.getFailureRecords(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("失败记录不应该为空", response.getData());
        @SuppressWarnings("unchecked")
        List<ScorerDataMingrationController.FailureRecordResponse> responseList = 
                (List<ScorerDataMingrationController.FailureRecordResponse>) response.getData();
        Assert.assertEquals("失败记录数量应该匹配", mockFailureRecords.size(), responseList.size());
    }

    /**
     * 测试重试失败记录 - 成功场景
     */
    @Test
    public void testRetryFailureRecords_Success() {
        // Given
        String retrySessionId = "RETRY_" + TEST_SESSION_ID;
        Mockito.when(mingrationAppSvc.retryFailureRecords(TEST_SESSION_ID))
                .thenReturn(retrySessionId);

        // When
        SingleResponse response = controller.retryFailureRecords(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("重试会话ID应该匹配", retrySessionId, response.getData());
        Mockito.verify(mingrationAppSvc).retryFailureRecords(TEST_SESSION_ID);
    }

    /**
     * 测试获取失败统计 - 成功场景
     */
    @Test
    public void testGetFailureStatistics_Success() {
        // Given
        String mockStatistics = "失败统计信息";
        Mockito.when(mingrationAppSvc.getFailureStatistics(TEST_SESSION_ID))
                .thenReturn(mockStatistics);

        // When
        SingleResponse response = controller.getFailureStatistics(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("统计信息应该匹配", mockStatistics, response.getData());
        Mockito.verify(mingrationAppSvc).getFailureStatistics(TEST_SESSION_ID);
    }

    // ==================== 分离式任务测试 ====================

    /**
     * 测试启动分离式任务 - 成功场景
     */
    @Test
    public void testStartSeparatedTasks_Success() {
        // Given
        String migrationType = "FINISHED";
        Mockito.when(mingrationAppSvc.startSeparatedTasks(migrationType, TEST_OPERATOR_ID))
                .thenReturn(TEST_SESSION_ID);

        // When
        SingleResponse response = controller.startSeparatedTasks(migrationType);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("会话ID应该匹配", TEST_SESSION_ID, response.getData());
        Mockito.verify(mingrationAppSvc).startSeparatedTasks(migrationType, TEST_OPERATOR_ID);
    }

    /**
     * 测试获取分离式任务概览 - 成功场景
     */
    @Test
    public void testGetSeparatedTasksOverview_Success() {
        // Given
        ScorerDataMingrationAppSvc.SeparatedTasksOverview mockOverview = createMockSeparatedTasksOverview();
        Mockito.when(mingrationAppSvc.getSeparatedTasksOverview(TEST_SESSION_ID))
                .thenReturn(mockOverview);

        // When
        SingleResponse response = controller.getSeparatedTasksOverview(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("概览数据不应该为空", response.getData());
        Mockito.verify(mingrationAppSvc).getSeparatedTasksOverview(TEST_SESSION_ID);
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建模拟的进度对象
     */
    private OptimizedBitmapProgress createMockProgress() {
        OptimizedBitmapProgress progress = new OptimizedBitmapProgress(TEST_SESSION_ID, "FINISHED");
        progress.getTotalRecords().set(1000);
        progress.getProcessedCount().set(500);
        progress.getSuccessCount().set(480);
        progress.getFailureCount().set(20);
        progress.start();
        return progress;
    }

    /**
     * 创建模拟的失败记录列表
     */
    private List<OptimizedBitmapProgress.FailureRecord> createMockFailureRecords() {
        List<OptimizedBitmapProgress.FailureRecord> failureRecords = new ArrayList<>();
        
        OptimizedBitmapProgress.FailureRecord record1 = new OptimizedBitmapProgress.FailureRecord(
                TEST_USER_ID + "_1", TEST_COMPANY_ID, 1001L, "数据库连接超时", "TIMEOUT_ERROR");
        
        OptimizedBitmapProgress.FailureRecord record2 = new OptimizedBitmapProgress.FailureRecord(
                TEST_USER_ID + "_2", TEST_COMPANY_ID, 1002L, "数据验证失败", "VALIDATION_ERROR");
        
        failureRecords.add(record1);
        failureRecords.add(record2);
        
        return failureRecords;
    }

    /**
     * 创建模拟的分离式任务概览
     */
    private ScorerDataMingrationAppSvc.SeparatedTasksOverview createMockSeparatedTasksOverview() {
        ScorerDataMingrationAppSvc.SeparatedTasksOverview overview = new ScorerDataMingrationAppSvc.SeparatedTasksOverview();
        overview.setCurrentTaskSessionId(TEST_SESSION_ID);
        overview.setCurrentTaskType(OptimizedBitmapProgress.TaskType.FINISHED_ONLY);
        overview.setCurrentTaskStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        overview.setCurrentTaskProgress(65.5);
        overview.setRelatedTaskSessionId("RELATED_" + TEST_SESSION_ID);
        overview.setRelatedTaskType(OptimizedBitmapProgress.TaskType.NO_FINISHED_ONLY);
        overview.setRelatedTaskStatus(OptimizedBitmapProgress.MigrationStatus.RUNNING);
        overview.setRelatedTaskProgress(72.3);
        overview.setCurrentTaskStatusChanges(15);
        overview.setCrossTaskChanges(8);
        overview.setInTaskChanges(7);
        return overview;
    }

    // ==================== 增量检测和状态变化测试 ====================

    /**
     * 测试启动增量重新处理 - 成功场景
     */
    @Test
    public void testStartIncrementalReprocessing_Success() {
        // Given
        String incrementalSessionId = "INCREMENTAL_" + TEST_SESSION_ID;
        Mockito.when(mingrationAppSvc.startIncrementalReprocessing(TEST_SESSION_ID))
                .thenReturn(incrementalSessionId);

        // When
        SingleResponse response = controller.startIncrementalReprocessing(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("增量会话ID应该匹配", incrementalSessionId, response.getData());
        Mockito.verify(mingrationAppSvc).startIncrementalReprocessing(TEST_SESSION_ID);
    }

    /**
     * 测试获取状态变化统计 - 成功场景
     */
    @Test
    public void testGetStatusChangeStatistics_Success() {
        // Given
        String mockStatistics = "状态变化统计:\n总变化数: 25\n待重新处理: 15";
        Mockito.when(mingrationAppSvc.getStatusChangeStatistics(TEST_SESSION_ID))
                .thenReturn(mockStatistics);

        // When
        SingleResponse response = controller.getStatusChangeStatistics(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("统计信息应该匹配", mockStatistics, response.getData());
        Mockito.verify(mingrationAppSvc).getStatusChangeStatistics(TEST_SESSION_ID);
    }

    /**
     * 测试获取状态变化记录 - 成功场景
     */
    @Test
    public void testGetStatusChangeRecords_Success() {
        // Given
        OptimizedBitmapProgress mockProgress = createMockProgress();
        List<OptimizedBitmapProgress.StatusChangeRecord> mockStatusChanges = createMockStatusChangeRecords();

        Mockito.when(mingrationAppSvc.getOptimizedMigrationStatus(TEST_SESSION_ID))
                .thenReturn(mockProgress);

        // When
        SingleResponse response = controller.getStatusChangeRecords(TEST_SESSION_ID, null);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("状态变化记录不应该为空", response.getData());
    }

    /**
     * 测试启动跨任务重新处理 - 成功场景
     */
    @Test
    public void testStartCrossTaskReprocessing_Success() {
        // Given
        String crossTaskSessionId = "CROSS_TASK_" + TEST_SESSION_ID;
        Mockito.when(mingrationAppSvc.startCrossTaskReprocessing(TEST_SESSION_ID))
                .thenReturn(crossTaskSessionId);

        // When
        SingleResponse response = controller.startCrossTaskReprocessing(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("跨任务会话ID应该匹配", crossTaskSessionId, response.getData());
        Mockito.verify(mingrationAppSvc).startCrossTaskReprocessing(TEST_SESSION_ID);
    }

    // ==================== 动态扩展功能测试 ====================

    /**
     * 测试添加动态用户 - 成功场景
     */
    @Test
    public void testAddDynamicUser_Success() {
        // Given
        String userStatus = "FINISHED";
        Mockito.when(mingrationAppSvc.addDynamicUser(TEST_SESSION_ID, TEST_USER_ID, TEST_COMPANY_ID, userStatus))
                .thenReturn(true);

        // When
        SingleResponse response = controller.addDynamicUser(TEST_SESSION_ID, TEST_USER_ID, TEST_COMPANY_ID, userStatus);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("返回信息应该匹配", "添加成功", response.getData());
        Mockito.verify(mingrationAppSvc).addDynamicUser(TEST_SESSION_ID, TEST_USER_ID, TEST_COMPANY_ID, userStatus);
    }

    /**
     * 测试添加动态用户 - 失败场景
     */
    @Test
    public void testAddDynamicUser_Failure() {
        // Given
        String userStatus = "FINISHED";
        Mockito.when(mingrationAppSvc.addDynamicUser(TEST_SESSION_ID, TEST_USER_ID, TEST_COMPANY_ID, userStatus))
                .thenReturn(false);

        // When
        SingleResponse response = controller.addDynamicUser(TEST_SESSION_ID, TEST_USER_ID, TEST_COMPANY_ID, userStatus);

        // Then
        Assert.assertFalse("响应应该失败", response.isSuccess());
        Assert.assertEquals("错误码应该匹配", "ADD_DYNAMIC_USER_FAILED", response.getErrCode());
    }

    /**
     * 测试批量添加动态用户 - 成功场景
     */
    @Test
    public void testAddDynamicUsers_Success() {
        // Given
        Map<String, Map<String, String>> users = createMockDynamicUsers();
        int successCount = 3;
        Mockito.when(mingrationAppSvc.addDynamicUsers(TEST_SESSION_ID, users))
                .thenReturn(successCount);

        // When
        SingleResponse response = controller.addDynamicUsers(TEST_SESSION_ID, users);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("成功数量应该匹配", successCount, response.getData());
        Mockito.verify(mingrationAppSvc).addDynamicUsers(TEST_SESSION_ID, users);
    }

    /**
     * 测试获取动态扩展统计 - 成功场景
     */
    @Test
    public void testGetDynamicExpansionStatistics_Success() {
        // Given
        String mockStatistics = "动态扩展统计:\n总动态用户数: 15\n待处理: 3\n已处理: 12";
        Mockito.when(mingrationAppSvc.getDynamicExpansionStatistics(TEST_SESSION_ID))
                .thenReturn(mockStatistics);

        // When
        SingleResponse response = controller.getDynamicExpansionStatistics(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertEquals("统计信息应该匹配", mockStatistics, response.getData());
        Mockito.verify(mingrationAppSvc).getDynamicExpansionStatistics(TEST_SESSION_ID);
    }

    /**
     * 测试获取动态用户列表 - 成功场景
     */
    @Test
    public void testGetDynamicUsers_Success() {
        // Given
        List<OptimizedBitmapProgress.DynamicUserRecord> mockDynamicUsers = createMockDynamicUserRecords();
        Mockito.when(mingrationAppSvc.getDynamicUsers(TEST_SESSION_ID, false))
                .thenReturn(mockDynamicUsers);

        // When
        SingleResponse response = controller.getDynamicUsers(TEST_SESSION_ID, false);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("动态用户列表不应该为空", response.getData());
        @SuppressWarnings("unchecked")
        List<ScorerDataMingrationController.DynamicUserRecordResponse> responseList =
                (List<ScorerDataMingrationController.DynamicUserRecordResponse>) response.getData();
        Assert.assertEquals("动态用户数量应该匹配", mockDynamicUsers.size(), responseList.size());
    }

    /**
     * 测试获取待处理的动态用户 - 成功场景
     */
    @Test
    public void testGetPendingDynamicUsers_Success() {
        // Given
        List<OptimizedBitmapProgress.DynamicUserRecord> mockPendingUsers = createMockDynamicUserRecords();
        Mockito.when(mingrationAppSvc.getDynamicUsers(TEST_SESSION_ID, true))
                .thenReturn(mockPendingUsers);

        // When
        SingleResponse response = controller.getPendingDynamicUsers(TEST_SESSION_ID);

        // Then
        Assert.assertTrue("响应应该成功", response.isSuccess());
        Assert.assertNotNull("待处理用户列表不应该为空", response.getData());
        @SuppressWarnings("unchecked")
        List<ScorerDataMingrationController.DynamicUserRecordResponse> responseList =
                (List<ScorerDataMingrationController.DynamicUserRecordResponse>) response.getData();
        Assert.assertEquals("待处理用户数量应该匹配", mockPendingUsers.size(), responseList.size());
    }

    // ==================== 更多辅助方法 ====================

    /**
     * 创建模拟的状态变化记录列表
     */
    private List<OptimizedBitmapProgress.StatusChangeRecord> createMockStatusChangeRecords() {
        List<OptimizedBitmapProgress.StatusChangeRecord> statusChanges = new ArrayList<>();

        OptimizedBitmapProgress.StatusChangeRecord record1 = new OptimizedBitmapProgress.StatusChangeRecord(
                TEST_USER_ID + "_1", TEST_COMPANY_ID, "FINISHED", "NO_FINISHED");

        OptimizedBitmapProgress.StatusChangeRecord record2 = new OptimizedBitmapProgress.StatusChangeRecord(
                TEST_USER_ID + "_2", TEST_COMPANY_ID, "NO_FINISHED", "FINISHED");

        statusChanges.add(record1);
        statusChanges.add(record2);

        return statusChanges;
    }

    /**
     * 创建模拟的动态用户映射
     */
    private Map<String, Map<String, String>> createMockDynamicUsers() {
        Map<String, Map<String, String>> users = new HashMap<>();

        Map<String, String> user1 = new HashMap<>();
        user1.put("companyId", TEST_COMPANY_ID);
        user1.put("userStatus", "FINISHED");
        users.put(TEST_USER_ID + "_1", user1);

        Map<String, String> user2 = new HashMap<>();
        user2.put("companyId", TEST_COMPANY_ID);
        user2.put("userStatus", "FINISHED");
        users.put(TEST_USER_ID + "_2", user2);

        Map<String, String> user3 = new HashMap<>();
        user3.put("companyId", TEST_COMPANY_ID);
        user3.put("userStatus", "NO_FINISHED");
        users.put(TEST_USER_ID + "_3", user3);

        return users;
    }

    /**
     * 创建模拟的动态用户记录列表
     */
    private List<OptimizedBitmapProgress.DynamicUserRecord> createMockDynamicUserRecords() {
        List<OptimizedBitmapProgress.DynamicUserRecord> dynamicUsers = new ArrayList<>();

        OptimizedBitmapProgress.DynamicUserRecord record1 = new OptimizedBitmapProgress.DynamicUserRecord(
                TEST_USER_ID + "_1", TEST_COMPANY_ID, "FINISHED", "MANUAL");
        record1.setGlobalIndex(1001L);

        OptimizedBitmapProgress.DynamicUserRecord record2 = new OptimizedBitmapProgress.DynamicUserRecord(
                TEST_USER_ID + "_2", TEST_COMPANY_ID, "NO_FINISHED", "AUTO_DETECTION");
        record2.setGlobalIndex(1002L);

        dynamicUsers.add(record1);
        dynamicUsers.add(record2);

        return dynamicUsers;
    }
}
