package com.polaris.kpi.eval.web.migration;

import com.polaris.kpi.eval.app.migration.appsvc.DataMigrationAppSvc;
import com.polaris.kpi.eval.app.migration.dto.MigrationRequest;
import com.polaris.kpi.eval.app.migration.dto.MigrationStatusResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 数据迁移控制器
 * 提供数据迁移相关的REST API接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@RestController
@RequestMapping("/api/migration")
@RequiredArgsConstructor
@Slf4j
public class MigrationController {

    private final DataMigrationAppSvc dataMigrationAppSvc;

    /**
     * 启动数据迁移任务
     * 
     * @param request 迁移请求
     * @return 迁移会话ID
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startMigration(@Valid @RequestBody MigrationRequest request) {
        log.info("Starting migration request: {}", request.getMigrationType());
        
        try {
            // 验证请求参数
            if (!request.isValid()) {
                return ResponseEntity.badRequest()
                        .body(createErrorResponse("请求参数验证失败", "Invalid request parameters"));
            }
            
            String sessionId = dataMigrationAppSvc.startMigration(request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("sessionId", sessionId);
            response.put("message", "迁移任务已启动");
            response.put("estimatedBatches", request.getEstimatedBatchCount());
            response.put("estimatedTimeMinutes", request.getEstimatedProcessingTimeMinutes());
            
            log.info("Migration started successfully, sessionId: {}", sessionId);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to start migration", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("启动迁移任务失败", e.getMessage()));
        }
    }

    /**
     * 恢复中断的迁移任务
     * 
     * @param sessionId 会话ID
     * @return 恢复结果
     */
    @PostMapping("/resume/{sessionId}")
    public ResponseEntity<Map<String, Object>> resumeMigration(@PathVariable String sessionId) {
        log.info("Resuming migration: {}", sessionId);
        
        try {
            boolean success = dataMigrationAppSvc.resumeMigration(sessionId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("sessionId", sessionId);
            response.put("message", success ? "迁移任务已恢复" : "无法恢复迁移任务");
            
            if (success) {
                log.info("Migration resumed successfully: {}", sessionId);
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to resume migration: {}", sessionId);
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("Error resuming migration: {}", sessionId, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("恢复迁移任务失败", e.getMessage()));
        }
    }

    /**
     * 暂停迁移任务
     * 
     * @param sessionId 会话ID
     * @return 暂停结果
     */
    @PostMapping("/pause/{sessionId}")
    public ResponseEntity<Map<String, Object>> pauseMigration(@PathVariable String sessionId) {
        log.info("Pausing migration: {}", sessionId);
        
        try {
            boolean success = dataMigrationAppSvc.pauseMigration(sessionId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("sessionId", sessionId);
            response.put("message", success ? "迁移任务已暂停" : "无法暂停迁移任务");
            
            if (success) {
                log.info("Migration paused successfully: {}", sessionId);
                return ResponseEntity.ok(response);
            } else {
                log.warn("Failed to pause migration: {}", sessionId);
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            log.error("Error pausing migration: {}", sessionId, e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("暂停迁移任务失败", e.getMessage()));
        }
    }

    /**
     * 获取迁移状态
     * 
     * @param sessionId 会话ID
     * @return 迁移状态
     */
    @GetMapping("/status/{sessionId}")
    public ResponseEntity<MigrationStatusResponse> getMigrationStatus(@PathVariable String sessionId) {
        log.debug("Getting migration status: {}", sessionId);
        
        try {
            MigrationStatusResponse status = dataMigrationAppSvc.getMigrationStatus(sessionId);
            
            // 计算派生字段
            status.calculateDerivedFields();
            
            if (Boolean.TRUE.equals(status.getFound())) {
                return ResponseEntity.ok(status);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error getting migration status: {}", sessionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取所有活跃的迁移任务
     * 
     * @return 活跃任务列表
     */
    @GetMapping("/active")
    public ResponseEntity<List<MigrationStatusResponse>> getActiveMigrations() {
        log.debug("Getting active migrations");
        
        try {
            List<MigrationStatusResponse> activeMigrations = dataMigrationAppSvc.getActiveMigrations();
            
            // 为每个任务计算派生字段
            activeMigrations.forEach(MigrationStatusResponse::calculateDerivedFields);
            
            return ResponseEntity.ok(activeMigrations);
            
        } catch (Exception e) {
            log.error("Error getting active migrations", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 清理已完成的迁移记录
     * 
     * @param daysToKeep 保留天数
     * @return 清理结果
     */
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupCompletedMigrations(
            @RequestParam(defaultValue = "7") int daysToKeep) {
        log.info("Cleaning up completed migrations, keeping {} days", daysToKeep);
        
        try {
            int cleanedCount = dataMigrationAppSvc.cleanupCompletedMigrations(daysToKeep);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("cleanedCount", cleanedCount);
            response.put("message", String.format("已清理 %d 条已完成的迁移记录", cleanedCount));
            
            log.info("Cleaned up {} completed migration records", cleanedCount);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error cleaning up completed migrations", e);
            return ResponseEntity.internalServerError()
                    .body(createErrorResponse("清理迁移记录失败", e.getMessage()));
        }
    }

    /**
     * 创建默认迁移请求
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 默认迁移请求
     */
    @GetMapping("/template/default")
    public ResponseEntity<MigrationRequest> getDefaultMigrationRequest(
            @RequestParam String migrationType,
            @RequestParam String operatorId) {
        
        MigrationRequest request = MigrationRequest.createDefault(migrationType, operatorId);
        return ResponseEntity.ok(request);
    }

    /**
     * 创建高性能迁移请求
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 高性能迁移请求
     */
    @GetMapping("/template/high-performance")
    public ResponseEntity<MigrationRequest> getHighPerformanceMigrationRequest(
            @RequestParam String migrationType,
            @RequestParam String operatorId) {
        
        MigrationRequest request = MigrationRequest.createHighPerformance(migrationType, operatorId);
        return ResponseEntity.ok(request);
    }

    /**
     * 创建保守迁移请求
     * 
     * @param migrationType 迁移类型
     * @param operatorId 操作人员ID
     * @return 保守迁移请求
     */
    @GetMapping("/template/conservative")
    public ResponseEntity<MigrationRequest> getConservativeMigrationRequest(
            @RequestParam String migrationType,
            @RequestParam String operatorId) {
        
        MigrationRequest request = MigrationRequest.createConservative(migrationType, operatorId);
        return ResponseEntity.ok(request);
    }

    /**
     * 获取迁移任务的详细进度信息
     * 
     * @param sessionId 会话ID
     * @return 详细进度信息
     */
    @GetMapping("/progress/{sessionId}")
    public ResponseEntity<Map<String, Object>> getMigrationProgress(@PathVariable String sessionId) {
        log.debug("Getting detailed migration progress: {}", sessionId);
        
        try {
            MigrationStatusResponse status = dataMigrationAppSvc.getMigrationStatus(sessionId);
            
            if (!Boolean.TRUE.equals(status.getFound())) {
                return ResponseEntity.notFound().build();
            }
            
            status.calculateDerivedFields();
            
            Map<String, Object> response = new HashMap<>();
            response.put("sessionId", sessionId);
            response.put("status", status.getStatus());
            response.put("statusDescription", status.getStatusDescription());
            response.put("progressPercentage", status.getProgressPercentage());
            response.put("processedRecords", status.getProcessedRecords());
            response.put("totalRecords", status.getTotalRecords());
            response.put("successCount", status.getSuccessCount());
            response.put("failureCount", status.getFailureCount());
            response.put("skipCount", status.getSkipCount());
            response.put("successRate", status.getSuccessRate());
            response.put("failureRate", status.getFailureRate());
            response.put("skipRate", status.getSkipRate());
            response.put("averageProcessingSpeed", status.getAverageProcessingSpeed());
            response.put("estimatedRemainingMinutes", status.getEstimatedRemainingMinutes());
            response.put("elapsedMinutes", status.getElapsedMinutes());
            response.put("currentPosition", status.getCurrentPosition());
            response.put("detailedProgress", status.getDetailedProgress());
            response.put("summary", status.getSummary());
            response.put("canResume", status.canResume());
            response.put("isRunning", status.isRunning());
            response.put("isCompleted", status.isCompleted());
            response.put("isFailed", status.isFailed());
            response.put("isPaused", status.isPaused());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error getting detailed migration progress: {}", sessionId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建错误响应
     * 
     * @param message 错误消息
     * @param detail 错误详情
     * @return 错误响应Map
     */
    private Map<String, Object> createErrorResponse(String message, String detail) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        response.put("detail", detail);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
