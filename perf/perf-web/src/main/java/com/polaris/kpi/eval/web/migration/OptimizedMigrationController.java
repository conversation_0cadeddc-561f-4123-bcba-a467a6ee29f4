package com.polaris.kpi.eval.web.migration;

import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 优化迁移控制器
 * 提供大规模数据迁移的REST API接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-29
 */
@RestController
@RequestMapping("/api/migration/optimized")
@RequiredArgsConstructor
@Slf4j
public class OptimizedMigrationController {

    private final ScorerDataMingrationAppSvc scorerDataMingrationAppSvc;

    /**
     * 启动优化的大规模数据迁移
     * 
     * @param request 迁移请求
     * @return 迁移响应
     */
    @PostMapping("/start")
    public MigrationResponse startOptimizedMigration(@RequestBody OptimizedMigrationRequest request) {
        log.info("Starting optimized migration: type={}, operator={}", request.getMigrationType(), request.getOperatorId());
        
        try {
            String sessionId = scorerDataMingrationAppSvc.startOptimizedMigration(
                    request.getMigrationType(), 
                    request.getOperatorId()
            );
            
            return MigrationResponse.success(sessionId, "优化迁移任务启动成功");
            
        } catch (Exception e) {
            log.error("Failed to start optimized migration: {}", request, e);
            return MigrationResponse.error("启动优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复中断的优化迁移任务
     * 
     * @param sessionId 会话ID
     * @return 迁移响应
     */
    @PostMapping("/resume/{sessionId}")
    public MigrationResponse resumeOptimizedMigration(@PathVariable String sessionId) {
        log.info("Resuming optimized migration: {}", sessionId);
        
        try {
            boolean success = scorerDataMingrationAppSvc.resumeOptimizedMigration(sessionId);
            
            if (success) {
                return MigrationResponse.success(sessionId, "优化迁移任务恢复成功");
            } else {
                return MigrationResponse.error("优化迁移任务恢复失败，请检查任务状态");
            }
            
        } catch (Exception e) {
            log.error("Failed to resume optimized migration: {}", sessionId, e);
            return MigrationResponse.error("恢复优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停优化迁移任务
     * 
     * @param sessionId 会话ID
     * @return 迁移响应
     */
    @PostMapping("/pause/{sessionId}")
    public MigrationResponse pauseOptimizedMigration(@PathVariable String sessionId) {
        log.info("Pausing optimized migration: {}", sessionId);
        
        try {
            boolean success = scorerDataMingrationAppSvc.pauseOptimizedMigration(sessionId);
            
            if (success) {
                return MigrationResponse.success(sessionId, "优化迁移任务暂停成功");
            } else {
                return MigrationResponse.error("优化迁移任务暂停失败，请检查任务状态");
            }
            
        } catch (Exception e) {
            log.error("Failed to pause optimized migration: {}", sessionId, e);
            return MigrationResponse.error("暂停优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移状态
     * 
     * @param sessionId 会话ID
     * @return 迁移状态响应
     */
    @GetMapping("/status/{sessionId}")
    public MigrationStatusResponse getOptimizedMigrationStatus(@PathVariable String sessionId) {
        log.debug("Getting optimized migration status: {}", sessionId);
        
        try {
            OptimizedBitmapProgress progress = scorerDataMingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            
            if (progress == null) {
                return MigrationStatusResponse.notFound("优化迁移任务不存在");
            }
            
            return MigrationStatusResponse.builder()
                    .sessionId(sessionId)
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .currentCompanyId(progress.getCurrentCompanyId())
                    .currentCompanyPage(progress.getCurrentCompanyPage())
                    .currentUserPage(progress.getCurrentUserPage())
                    .lastProcessedIndex(progress.getLastProcessedIndex())
                    .startTime(progress.getStartTime())
                    .lastUpdateTime(progress.getLastUpdateTime())
                    .message("获取状态成功")
                    .success(true)
                    .build();
            
        } catch (Exception e) {
            log.error("Failed to get optimized migration status: {}", sessionId, e);
            return MigrationStatusResponse.error("获取优化迁移状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移详细进度
     * 
     * @param sessionId 会话ID
     * @return 详细进度响应
     */
    @GetMapping("/progress/{sessionId}")
    public MigrationProgressResponse getOptimizedMigrationProgress(@PathVariable String sessionId) {
        log.debug("Getting optimized migration progress: {}", sessionId);
        
        try {
            OptimizedBitmapProgress progress = scorerDataMingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            
            if (progress == null) {
                return MigrationProgressResponse.notFound("优化迁移任务不存在");
            }
            
            // 计算处理速度
            double processingSpeed = calculateProcessingSpeed(progress);
            
            // 计算预估剩余时间
            long estimatedRemainingTime = calculateEstimatedRemainingTime(progress, processingSpeed);
            
            return MigrationProgressResponse.builder()
                    .sessionId(sessionId)
                    .migrationType(progress.getMigrationType())
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .processingSpeed(processingSpeed)
                    .estimatedRemainingTimeSeconds(estimatedRemainingTime)
                    .currentPosition(buildCurrentPosition(progress))
                    .statistics(progress.getStatistics())
                    .message("获取详细进度成功")
                    .success(true)
                    .build();
            
        } catch (Exception e) {
            log.error("Failed to get optimized migration progress: {}", sessionId, e);
            return MigrationProgressResponse.error("获取优化迁移详细进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移统计信息
     * 
     * @param sessionId 会话ID
     * @return 统计信息
     */
    @GetMapping("/statistics/{sessionId}")
    public String getOptimizedMigrationStatistics(@PathVariable String sessionId) {
        log.debug("Getting optimized migration statistics: {}", sessionId);
        
        try {
            return scorerDataMingrationAppSvc.getOptimizedMigrationStatistics(sessionId);
        } catch (Exception e) {
            log.error("Failed to get optimized migration statistics: {}", sessionId, e);
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 清理优化迁移文件
     * 
     * @param sessionId 会话ID
     * @return 清理响应
     */
    @DeleteMapping("/cleanup/{sessionId}")
    public MigrationResponse cleanupOptimizedMigrationFiles(@PathVariable String sessionId) {
        log.info("Cleaning up optimized migration files: {}", sessionId);
        
        try {
            boolean success = scorerDataMingrationAppSvc.cleanupOptimizedMigrationFiles(sessionId);
            
            if (success) {
                return MigrationResponse.success(sessionId, "优化迁移文件清理成功");
            } else {
                return MigrationResponse.error("优化迁移文件清理失败");
            }
            
        } catch (Exception e) {
            log.error("Failed to cleanup optimized migration files: {}", sessionId, e);
            return MigrationResponse.error("清理优化迁移文件失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 计算处理速度（记录/秒）
     */
    private double calculateProcessingSpeed(OptimizedBitmapProgress progress) {
        if (progress.getStartTime() == null || progress.getLastUpdateTime() == null) {
            return 0.0;
        }
        
        long elapsedSeconds = java.time.Duration.between(progress.getStartTime(), progress.getLastUpdateTime()).getSeconds();
        if (elapsedSeconds <= 0) {
            return 0.0;
        }
        
        return (double) progress.getProcessedCount().get() / elapsedSeconds;
    }

    /**
     * 计算预估剩余时间（秒）
     */
    private long calculateEstimatedRemainingTime(OptimizedBitmapProgress progress, double processingSpeed) {
        if (processingSpeed <= 0) {
            return -1; // 无法估算
        }
        
        long remainingRecords = progress.getTotalRecords().get() - progress.getProcessedCount().get();
        return (long) (remainingRecords / processingSpeed);
    }

    /**
     * 构建当前位置信息
     */
    private String buildCurrentPosition(OptimizedBitmapProgress progress) {
        return String.format("公司[%s] 第%d页 用户第%d页 索引%d", 
                progress.getCurrentCompanyId() != null ? progress.getCurrentCompanyId() : "N/A",
                progress.getCurrentCompanyPage(),
                progress.getCurrentUserPage(),
                progress.getLastProcessedIndex());
    }

    // ==================== 内部类 ====================

    /**
     * 优化迁移请求
     */
    public static class OptimizedMigrationRequest {
        private String migrationType;
        private String operatorId;
        
        // Getters and Setters
        public String getMigrationType() { return migrationType; }
        public void setMigrationType(String migrationType) { this.migrationType = migrationType; }
        
        public String getOperatorId() { return operatorId; }
        public void setOperatorId(String operatorId) { this.operatorId = operatorId; }
        
        @Override
        public String toString() {
            return String.format("OptimizedMigrationRequest{migrationType='%s', operatorId='%s'}", 
                    migrationType, operatorId);
        }
    }

    /**
     * 迁移响应
     */
    public static class MigrationResponse {
        private boolean success;
        private String sessionId;
        private String message;
        
        public static MigrationResponse success(String sessionId, String message) {
            MigrationResponse response = new MigrationResponse();
            response.success = true;
            response.sessionId = sessionId;
            response.message = message;
            return response;
        }
        
        public static MigrationResponse error(String message) {
            MigrationResponse response = new MigrationResponse();
            response.success = false;
            response.message = message;
            return response;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 迁移状态响应
     */
    public static class MigrationStatusResponse {
        private boolean success;
        private String message;
        private String sessionId;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private String currentCompanyId;
        private int currentCompanyPage;
        private int currentUserPage;
        private long lastProcessedIndex;
        private java.time.LocalDateTime startTime;
        private java.time.LocalDateTime lastUpdateTime;
        
        public static MigrationStatusResponse notFound(String message) {
            MigrationStatusResponse response = new MigrationStatusResponse();
            response.success = false;
            response.message = message;
            return response;
        }
        
        public static MigrationStatusResponse error(String message) {
            MigrationStatusResponse response = new MigrationStatusResponse();
            response.success = false;
            response.message = message;
            return response;
        }
        
        public static MigrationStatusResponseBuilder builder() {
            return new MigrationStatusResponseBuilder();
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }
        
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public long getProcessedCount() { return processedCount; }
        public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }
        
        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }
        
        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }
        
        public String getCurrentCompanyId() { return currentCompanyId; }
        public void setCurrentCompanyId(String currentCompanyId) { this.currentCompanyId = currentCompanyId; }
        
        public int getCurrentCompanyPage() { return currentCompanyPage; }
        public void setCurrentCompanyPage(int currentCompanyPage) { this.currentCompanyPage = currentCompanyPage; }
        
        public int getCurrentUserPage() { return currentUserPage; }
        public void setCurrentUserPage(int currentUserPage) { this.currentUserPage = currentUserPage; }
        
        public long getLastProcessedIndex() { return lastProcessedIndex; }
        public void setLastProcessedIndex(long lastProcessedIndex) { this.lastProcessedIndex = lastProcessedIndex; }
        
        public java.time.LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(java.time.LocalDateTime startTime) { this.startTime = startTime; }
        
        public java.time.LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(java.time.LocalDateTime lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
        
        // Builder类
        public static class MigrationStatusResponseBuilder {
            private MigrationStatusResponse response = new MigrationStatusResponse();
            
            public MigrationStatusResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }
            
            public MigrationStatusResponseBuilder status(String status) {
                response.status = status;
                return this;
            }
            
            public MigrationStatusResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }
            
            public MigrationStatusResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }
            
            public MigrationStatusResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }
            
            public MigrationStatusResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }
            
            public MigrationStatusResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }
            
            public MigrationStatusResponseBuilder currentCompanyId(String currentCompanyId) {
                response.currentCompanyId = currentCompanyId;
                return this;
            }
            
            public MigrationStatusResponseBuilder currentCompanyPage(int currentCompanyPage) {
                response.currentCompanyPage = currentCompanyPage;
                return this;
            }
            
            public MigrationStatusResponseBuilder currentUserPage(int currentUserPage) {
                response.currentUserPage = currentUserPage;
                return this;
            }
            
            public MigrationStatusResponseBuilder lastProcessedIndex(long lastProcessedIndex) {
                response.lastProcessedIndex = lastProcessedIndex;
                return this;
            }
            
            public MigrationStatusResponseBuilder startTime(java.time.LocalDateTime startTime) {
                response.startTime = startTime;
                return this;
            }
            
            public MigrationStatusResponseBuilder lastUpdateTime(java.time.LocalDateTime lastUpdateTime) {
                response.lastUpdateTime = lastUpdateTime;
                return this;
            }
            
            public MigrationStatusResponseBuilder message(String message) {
                response.message = message;
                return this;
            }
            
            public MigrationStatusResponseBuilder success(boolean success) {
                response.success = success;
                return this;
            }
            
            public MigrationStatusResponse build() {
                return response;
            }
        }
    }

    /**
     * 迁移详细进度响应
     */
    public static class MigrationProgressResponse {
        private boolean success;
        private String message;
        private String sessionId;
        private String migrationType;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private double processingSpeed;
        private long estimatedRemainingTimeSeconds;
        private String currentPosition;
        private String statistics;
        
        public static MigrationProgressResponse notFound(String message) {
            MigrationProgressResponse response = new MigrationProgressResponse();
            response.success = false;
            response.message = message;
            return response;
        }
        
        public static MigrationProgressResponse error(String message) {
            MigrationProgressResponse response = new MigrationProgressResponse();
            response.success = false;
            response.message = message;
            return response;
        }
        
        public static MigrationProgressResponseBuilder builder() {
            return new MigrationProgressResponseBuilder();
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getMigrationType() { return migrationType; }
        public void setMigrationType(String migrationType) { this.migrationType = migrationType; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }
        
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public long getProcessedCount() { return processedCount; }
        public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }
        
        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }
        
        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }
        
        public double getProcessingSpeed() { return processingSpeed; }
        public void setProcessingSpeed(double processingSpeed) { this.processingSpeed = processingSpeed; }
        
        public long getEstimatedRemainingTimeSeconds() { return estimatedRemainingTimeSeconds; }
        public void setEstimatedRemainingTimeSeconds(long estimatedRemainingTimeSeconds) { this.estimatedRemainingTimeSeconds = estimatedRemainingTimeSeconds; }
        
        public String getCurrentPosition() { return currentPosition; }
        public void setCurrentPosition(String currentPosition) { this.currentPosition = currentPosition; }
        
        public String getStatistics() { return statistics; }
        public void setStatistics(String statistics) { this.statistics = statistics; }
        
        // Builder类
        public static class MigrationProgressResponseBuilder {
            private MigrationProgressResponse response = new MigrationProgressResponse();
            
            public MigrationProgressResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }
            
            public MigrationProgressResponseBuilder migrationType(String migrationType) {
                response.migrationType = migrationType;
                return this;
            }
            
            public MigrationProgressResponseBuilder status(String status) {
                response.status = status;
                return this;
            }
            
            public MigrationProgressResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }
            
            public MigrationProgressResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }
            
            public MigrationProgressResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }
            
            public MigrationProgressResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }
            
            public MigrationProgressResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }
            
            public MigrationProgressResponseBuilder processingSpeed(double processingSpeed) {
                response.processingSpeed = processingSpeed;
                return this;
            }
            
            public MigrationProgressResponseBuilder estimatedRemainingTimeSeconds(long estimatedRemainingTimeSeconds) {
                response.estimatedRemainingTimeSeconds = estimatedRemainingTimeSeconds;
                return this;
            }
            
            public MigrationProgressResponseBuilder currentPosition(String currentPosition) {
                response.currentPosition = currentPosition;
                return this;
            }
            
            public MigrationProgressResponseBuilder statistics(String statistics) {
                response.statistics = statistics;
                return this;
            }
            
            public MigrationProgressResponseBuilder message(String message) {
                response.message = message;
                return this;
            }
            
            public MigrationProgressResponseBuilder success(boolean success) {
                response.success = success;
                return this;
            }
            
            public MigrationProgressResponse build() {
                return response;
            }
        }
    }
}
