package com.polaris.kpi.controller.eval.task;

import cn.com.polaris.kpi.company.MsgSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.polaris.kpi.common.KpiI18NException;
import com.polaris.kpi.eval.app.task.appsvc.*;
import com.polaris.kpi.eval.app.task.dto.sumit.BatchSubmitScoreCmd3;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreCmd;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreCmd3;
import com.polaris.kpi.eval.app.task.dto.sumit.SubmitScoreV3Cmd;
import com.polaris.kpi.eval.domain.task.entity.empeval.vealResult.BatchSubmitScoreResult;
import com.polaris.kpi.eval.domain.task.event.msg.CancelTodoEvent;
import com.polaris.kpi.eval.domain.task.repo.AdminTaskOperationRepo;
import com.polaris.kpi.eval.domain.task.type.TalentStatus;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.eval.infr.task.ppojo.DeadLineJobPo;
import com.polaris.kpi.eval.infr.task.ppojo.PerfEvaluateTaskUserDo;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.dao.DomainDaoImpl;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@RestController
public class AutoJobController {

    @Autowired
    private EmpEvalJobAppSvc jobAppSvc;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private AdminTaskAppSvc adminTaskAppSvc;
    @Autowired
    private SubmitScoreAppSvc submitScoreAppSvc;
    @Autowired
    private ScoreStageAppSvc scoreStageAppSvc;
    @Autowired
    private AdminTaskOperationRepo adminTaskOperationRepo;

    //阶段截止时间自动催办定时任务 每小时扫描一次
    @RequestMapping("eval/auto/urging")
    @Scheduled(cron = "0 0 */1 * * ?")
    public SingleResponse autoUrging() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("阶段截止时间催办任务扫描开始！");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        String urgingTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), now.getHour(), 0, 0).format(formatter);
        log.info("开始时间：" + now);
        List<String> companyIds = taskUserDao.listDeadLineCompanyIds(null, null, 1, urgingTime);
        if (CollUtil.isEmpty(companyIds)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companyIds.size());
        }
        for (String id : companyIds) {
            try {
                jobAppSvc.lockCompanyForHourJob(id, "deadLineAutoUrging");
                log.info("deadLineAutoUrging_companyId={}", id);
                jobAppSvc.autoUrging(id, urgingTime);
            } catch (DataIntegrityViolationException e) {
                log.error("定时阶段截止时间催办任务冲突,公司:{}", id);
            } catch (Exception e) {
                log.error("定时阶段截止时间催办任务出错,公司:{}, error:{}", id, e.getMessage(), e);
            }
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }

    //阶段截止时间自动跳过定时任务 每天0点扫描一次
    @RequestMapping("eval/auto/skip")
    @Scheduled(cron = "0 0 0 * * ?")
    public SingleResponse autoSkip() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("阶段截止时间跳过任务扫描开始！");
        String endDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        log.info("扫描截止时间 =" + endDate);
        List<String> companyIds = taskUserDao.listDeadLineCompanyIds(endDate, Arrays.asList(0, 1, 2), null, null);
        if (CollUtil.isEmpty(companyIds)) {
            log.info("没有使用中的公司");
            return SingleResponse.of(companyIds.size());
        }
        for (String id : companyIds) {
            try {
                String uniKey = jobAppSvc.lockCompanyForDayJob(id, "deadLineAutoSkip");
                MDC.put("tid", uniKey);
                log.info("deadLineAutoSkip_companyId={}", id);
                List<DeadLineJobPo> pos = taskUserDao.listDeadLineSkip(id, endDate);
                TenantId companyId = new TenantId(id);
                for (DeadLineJobPo po : pos) {
                    if (TalentStatus.SCORING.getStatus().equals(po.getTaskStatus())) {
                        log.info("开始执行评分跳过taskUserId ：{},任务状态:{}", po.getTaskUserId() , po.getTaskStatus());
                        //Map<String,List<SubmitScoreCmd>> submitMap = submitScoreAppSvc.skipScoreSubmit(new TenantId(id),po.getTaskUserId(),"-1");
                        List<BatchSubmitScoreCmd3.CmdWrap> cmdWraps = submitScoreAppSvc.skipScoreSubmitV3(companyId, po.getTaskUserId(), "-1");
                        String tid = MDC.get("tid");
                        List<SubmitScoreV3Cmd> batchs = scoreStageAppSvc.buildBatchSubmitScoreCmd(id, "", cmdWraps);//build cmd
                        if (CollUtil.isEmpty(batchs)) {
                            log.info("[deadLineAutoSkip_companyId]无需跳过 taskUserId ：{}", po.getTaskUserId());
                            continue;
                        }
                        BatchSubmitScoreResult result = scoreStageAppSvc.batchSubmitScenesScoreV3(tid, companyId, null, batchs);//v2
                        Thread.sleep(200);
                        log.info("result:{}", JSONUtil.toJsonStr(result));
                        continue;
                    }
                    jobAppSvc.autoSkip(po);
                }
            } catch (DataIntegrityViolationException e) {
                log.error("定时阶段截止时间跳过任务冲突,公司:{}", id);
            } catch (Exception e) {
                log.error("定时阶段截止时间跳过任务出错,公司:{}, error:{}", id, e.getMessage(), e);
            }
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }


    @RequestMapping("fix/auto/fixEmpEvalPath")
    //@Scheduled(cron = "0 0 0 * * ?")
    public SingleResponse fixEmpEvalPath(@RequestBody List<String> companyIds) {
        log.info("fixEmpEvalPath！{}", companyIds);
        companyIds = CollUtil.isNotEmpty(companyIds) ? companyIds : adminTaskAppSvc.listV1NoneOrgTaskUserCompany();
        for (String companyId : companyIds) {
            adminTaskAppSvc.fixEmpEvalPath(MDC.get("tid"), companyId);
        }
        MDC.clear();
        return SingleResponse.buildSuccess();
    }

    //@RequestMapping("eval/auto/testAutoTimerEvent")
    //@Scheduled(cron = "*/1 * * * * ?")
    //public SingleResponse testAutoTimerEvent() {
    //    adminTaskAppSvc.aVoid();
    //    return SingleResponse.buildSuccess();
    //}
}
