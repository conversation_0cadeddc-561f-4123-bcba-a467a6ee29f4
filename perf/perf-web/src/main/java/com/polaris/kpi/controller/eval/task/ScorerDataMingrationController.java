package com.polaris.kpi.controller.eval.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.domain.task.entity.TaskBitmapWithLog;
import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评分人数据迁移服务
 *
 */
@Slf4j
@RestController
public class ScorerDataMingrationController extends AccountBaseController {

    private static final Integer VERSION = 2000000;
    private static final int COMPANY_PAGE_SIZE = 10;
    private static final int USER_PAGE_SIZE = 500;

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private ScorerDataMingrationAppSvc mingrationAppSvc;
    @Autowired
    private TaskBitmapWithLog bitmapWithLog;

    @RequestMapping("perf/task/migrateFinished")
    public SingleResponse migrateFinished() {
        log.info("评分迁移【已完成】任务开始");
        int totalCompaniesProcessed = 0;
        int cPageNo = 1;

        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            if (CollUtil.isEmpty(companies)) {
                log.info("没有更多公司数据");
                break;
            }
            for (Company company : companies) {
                processCompanyFinshed(company.getId(),null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }

        log.info("评分迁移【已完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }


    @RequestMapping("perf/task/migrateFinishedByCompanyId")
    public SingleResponse migrateFinishedByCompanyId(String taskId) {
        log.info("评分迁移【已完成】任务开始");
        String companyId = getCompanyId();
        processCompanyFinshed(companyId,taskId);
        log.info("评分迁移【已完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }
    @RequestMapping("perf/task/migrateNoFinish")
    public SingleResponse migrateNoFinish() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("评分迁移【未完成】任务开始");

        int totalCompaniesProcessed = 0;
        int cPageNo = 1;
        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            for (Company company : companies) {
                processCompanyNoFinish(company.getId(), null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }
        log.info("评分迁移【未完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }

    @RequestMapping("perf/task/migrateNoFinishByCompanyId")
    public SingleResponse migrateNoFinishByCompanyId(String taskId) {
        log.info("评分迁移【未完成】任务开始");
        String companyId = getCompanyId();
        processCompanyNoFinish(companyId,taskId);
        log.info("评分迁移【未完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }

    @RequestMapping("perf/task/migrateFinishedOne")
    public SingleResponse migrateFinishedOne(String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateFinishedOne companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateFinishedOne(companyId, taskUserId);
        log.info("单个评分迁移migrateFinishedOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }


    @RequestMapping("perf/task/migrateNoFinishOne")
    public SingleResponse migrateNoFinishOne( String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateNoFinishOne,companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateNoFinishOne(companyId, taskUserId);
        log.info("单个评分迁移migrateNoFinishOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }

    @RequestMapping("perf/task/retryNoFinishedTasks")
    public SingleResponse retryNoFinishedTasks() {
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务开始");
        List<String> noFinishedTasks = bitmapWithLog.getNoFinishedFailureTasks();
        if (CollUtil.isEmpty(noFinishedTasks)){
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = noFinishedTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageNoFinishedTasks = noFinishedTasks.subList(start, end);
            for (String strId : pageNoFinishedTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateNoFinish(companyId,taskUserId);
            }
        }
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务结束");
        return SingleResponse.of("");
    }


    private void doMigrateNoFinish( String companyId,String taskUserId){
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateNoFinish={}", tid);
            mingrationAppSvc.migrateNoFinish(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【未完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【未完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

    //doMigrateFinished
    @RequestMapping("perf/task/retryFinishedFailureTasks")
    public SingleResponse retryFinishedFailureTasks() {
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务开始");
        List<String> finishedFailureTasks = bitmapWithLog.getFinishedFailureTasks();
        if (CollUtil.isEmpty(finishedFailureTasks)) {
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = finishedFailureTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageFinishedFailureTasks = finishedFailureTasks.subList(start, end);
            for (String strId : pageFinishedFailureTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateFinished(companyId, taskUserId);
            }
        }
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务结束");
        return SingleResponse.of("");
    }
    @RequestMapping("perf/task/logTaskStatus")
    public SingleResponse logTaskStatus() {
        log.info("评分迁移 logTaskStatus任务开始");
        mingrationAppSvc.logTaskStatus();
        log.info("评分迁移 logTaskStatus任务结束");
        return SingleResponse.of("");
    }


    private void processCompanyFinshed(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId, true, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateFinished(companyId, taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }

    private void processCompanyNoFinish(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            //分页查询考核任务未完成的
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId,false, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateNoFinish(companyId,taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }
    private void doMigrateFinished(String companyId, String taskUserId) {
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateFinished={}", tid);
            mingrationAppSvc.migrateFinished(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【已完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【已完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

    // ==================== 新增的优化迁移方法 ====================

    /**
     * 启动优化的大规模数据迁移
     * 支持131万条记录的分批处理和断点续传
     */
    @RequestMapping("perf/task/startOptimizedMigration")
    public SingleResponse startOptimizedMigration(String migrationType) {
        log.info("启动优化迁移任务: type={}", migrationType);

        try {
            String operatorId = getCurrentUserId(); // 获取当前操作人员ID
            String sessionId = mingrationAppSvc.startOptimizedMigration(migrationType, operatorId);

            log.info("优化迁移任务启动成功: sessionId={}", sessionId);
            return SingleResponse.of(sessionId);

        } catch (Exception e) {
            log.error("启动优化迁移任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("MIGRATION_START_FAILED", "启动优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复中断的优化迁移任务
     */
    @RequestMapping("perf/task/resumeOptimizedMigration")
    public SingleResponse resumeOptimizedMigration(String sessionId) {
        log.info("恢复优化迁移任务: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.resumeOptimizedMigration(sessionId);

            if (success) {
                log.info("优化迁移任务恢复成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务恢复成功");
            } else {
                log.warn("优化迁移任务恢复失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_RESUME_FAILED", "优化迁移任务恢复失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("恢复优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_RESUME_ERROR", "恢复优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停优化迁移任务
     */
    @RequestMapping("perf/task/pauseOptimizedMigration")
    public SingleResponse pauseOptimizedMigration(String sessionId) {
        log.info("暂停优化迁移任务: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.pauseOptimizedMigration(sessionId);

            if (success) {
                log.info("优化迁移任务暂停成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务暂停成功");
            } else {
                log.warn("优化迁移任务暂停失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_PAUSE_FAILED", "优化迁移任务暂停失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("暂停优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PAUSE_ERROR", "暂停优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移状态
     */
    @RequestMapping("perf/task/getOptimizedMigrationStatus")
    public SingleResponse getOptimizedMigrationStatus(String sessionId) {
        log.debug("获取优化迁移状态: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);

            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "优化迁移任务不存在");
            }

            // 构建状态响应
            MigrationStatusResponse response = MigrationStatusResponse.builder()
                    .sessionId(sessionId)
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .currentCompanyId(progress.getCurrentCompanyId())
                    .currentCompanyPage(progress.getCurrentCompanyPage())
                    .currentUserPage(progress.getCurrentUserPage())
                    .lastProcessedIndex(progress.getLastProcessedIndex())
                    .startTime(progress.getStartTime())
                    .lastUpdateTime(progress.getLastUpdateTime())
                    .build();

            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("获取优化迁移状态失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_STATUS_ERROR", "获取优化迁移状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移详细进度
     */
    @RequestMapping("perf/task/getOptimizedMigrationProgress")
    public SingleResponse getOptimizedMigrationProgress(String sessionId) {
        log.debug("获取优化迁移详细进度: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);

            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "优化迁移任务不存在");
            }

            // 计算处理速度
            double processingSpeed = calculateProcessingSpeed(progress);

            // 计算预估剩余时间
            long estimatedRemainingTime = calculateEstimatedRemainingTime(progress, processingSpeed);

            // 构建详细进度响应
            MigrationProgressResponse response = MigrationProgressResponse.builder()
                    .sessionId(sessionId)
                    .migrationType(progress.getMigrationType())
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .processingSpeed(processingSpeed)
                    .estimatedRemainingTimeSeconds(estimatedRemainingTime)
                    .currentPosition(buildCurrentPosition(progress))
                    .statistics(progress.getStatistics())
                    .build();

            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("获取优化迁移详细进度失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PROGRESS_ERROR", "获取优化迁移详细进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移统计信息
     */
    @RequestMapping("perf/task/getOptimizedMigrationStatistics")
    public SingleResponse getOptimizedMigrationStatistics(String sessionId) {
        log.debug("获取优化迁移统计信息: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getOptimizedMigrationStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取优化迁移统计信息失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_STATISTICS_ERROR", "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理优化迁移文件
     */
    @RequestMapping("perf/task/cleanupOptimizedMigrationFiles")
    public SingleResponse cleanupOptimizedMigrationFiles(String sessionId) {
        log.info("清理优化迁移文件: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.cleanupOptimizedMigrationFiles(sessionId);

            if (success) {
                log.info("优化迁移文件清理成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移文件清理成功");
            } else {
                log.warn("优化迁移文件清理失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_CLEANUP_FAILED", "优化迁移文件清理失败");
            }

        } catch (Exception e) {
            log.error("清理优化迁移文件失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_CLEANUP_ERROR", "清理优化迁移文件失败: " + e.getMessage());
        }
    }

    // ==================== 失败记录查询和重试API ====================

    /**
     * 获取失败记录列表
     * 支持按公司ID和错误类型过滤
     */
    @RequestMapping("perf/task/getFailureRecords")
    public SingleResponse getFailureRecords(String sessionId, String companyId, String errorType) {
        log.info("获取失败记录: sessionId={}, companyId={}, errorType={}", sessionId, companyId, errorType);

        try {
            List<OptimizedBitmapProgress.FailureRecord> failureRecords =
                    mingrationAppSvc.getFailureRecords(sessionId, companyId, errorType);

            // 构建响应数据
            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取失败记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取失败记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取失败记录统计信息
     */
    @RequestMapping("perf/task/getFailureStatistics")
    public SingleResponse getFailureStatistics(String sessionId) {
        log.info("获取失败记录统计: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getFailureStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取失败记录统计失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_FAILURE_STATISTICS_ERROR", "获取失败统计失败: " + e.getMessage());
        }
    }

    /**
     * 启动失败记录重试
     * 支持重试所有失败记录或指定用户ID列表
     */
    @RequestMapping("perf/task/startFailureRetry")
    public SingleResponse startFailureRetry(String sessionId, String userIds) {
        log.info("启动失败记录重试: sessionId={}, userIds={}", sessionId, userIds);

        try {
            // 解析用户ID列表
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.stream(userIds.split(","))
                        .map(String::trim)
                        .filter(id -> !id.isEmpty())
                        .collect(Collectors.toList());
            }

            String retrySessionId = mingrationAppSvc.startFailureRetry(sessionId, userIdList);

            log.info("失败记录重试启动成功: originalSessionId={}, retrySessionId={}", sessionId, retrySessionId);
            return SingleResponse.of(retrySessionId);

        } catch (Exception e) {
            log.error("启动失败记录重试失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("START_FAILURE_RETRY_ERROR", "启动失败重试失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID快速定位失败记录
     */
    @RequestMapping("perf/task/getFailureRecordByUserId")
    public SingleResponse getFailureRecordByUserId(String sessionId, String userId) {
        log.info("根据用户ID获取失败记录: sessionId={}, userId={}", sessionId, userId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            OptimizedBitmapProgress.FailureRecord failureRecord = progress.getFailureRecord(userId);
            if (failureRecord == null) {
                return SingleResponse.buildFailure("FAILURE_RECORD_NOT_FOUND", "该用户没有失败记录");
            }

            FailureRecordResponse response = buildFailureRecordResponse(failureRecord);
            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("根据用户ID获取失败记录失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORD_BY_USERID_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 重试指定用户的失败记录
     */
    @RequestMapping("perf/task/retrySpecificUser")
    public SingleResponse retrySpecificUser(String sessionId, String userId) {
        log.info("重试指定用户: sessionId={}, userId={}", sessionId, userId);

        try {
            List<String> userIdList = Arrays.asList(userId);
            String retrySessionId = mingrationAppSvc.startFailureRetry(sessionId, userIdList);

            log.info("指定用户重试启动成功: originalSessionId={}, userId={}, retrySessionId={}",
                    sessionId, userId, retrySessionId);
            return SingleResponse.of(retrySessionId);

        } catch (Exception e) {
            log.error("重试指定用户失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("RETRY_SPECIFIC_USER_ERROR", "重试指定用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据公司ID获取失败记录
     */
    @RequestMapping("perf/task/getFailureRecordsByCompany")
    public SingleResponse getFailureRecordsByCompany(String sessionId, String companyId) {
        log.info("根据公司ID获取失败记录: sessionId={}, companyId={}", sessionId, companyId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getFailureRecordsByCompany(companyId);

            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("根据公司ID获取失败记录成功: sessionId={}, companyId={}, count={}",
                    sessionId, companyId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("根据公司ID获取失败记录失败: sessionId={}, companyId={}", sessionId, companyId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_BY_COMPANY_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据错误类型获取失败记录
     */
    @RequestMapping("perf/task/getFailureRecordsByErrorType")
    public SingleResponse getFailureRecordsByErrorType(String sessionId, String errorType) {
        log.info("根据错误类型获取失败记录: sessionId={}, errorType={}", sessionId, errorType);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getFailureRecordsByErrorType(errorType);

            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("根据错误类型获取失败记录成功: sessionId={}, errorType={}, count={}",
                    sessionId, errorType, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("根据错误类型获取失败记录失败: sessionId={}, errorType={}", sessionId, errorType, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_BY_ERROR_TYPE_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    // ==================== 增量检测和处理API ====================

    /**
     * 启动增量重新处理
     * 处理迁移过程中状态发生变化的数据
     */
    @RequestMapping("perf/task/startIncrementalReprocessing")
    public SingleResponse startIncrementalReprocessing(String sessionId) {
        log.info("启动增量重新处理: sessionId={}", sessionId);

        try {
            String incrementalSessionId = mingrationAppSvc.startIncrementalReprocessing(sessionId);

            log.info("增量重新处理启动成功: originalSessionId={}, incrementalSessionId={}",
                    sessionId, incrementalSessionId);
            return SingleResponse.of(incrementalSessionId);

        } catch (Exception e) {
            log.error("启动增量重新处理失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("START_INCREMENTAL_REPROCESSING_ERROR", "启动增量重新处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态变化统计信息
     */
    @RequestMapping("perf/task/getStatusChangeStatistics")
    public SingleResponse getStatusChangeStatistics(String sessionId) {
        log.info("获取状态变化统计: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getStatusChangeStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取状态变化统计失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGE_STATISTICS_ERROR", "获取状态变化统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态变化记录列表
     */
    @RequestMapping("perf/task/getStatusChangeRecords")
    public SingleResponse getStatusChangeRecords(String sessionId, String changeType) {
        log.info("获取状态变化记录: sessionId={}, changeType={}", sessionId, changeType);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> statusChangeRecords;

            if (changeType != null && !changeType.trim().isEmpty()) {
                // 按变化类型过滤
                String[] parts = changeType.split("->");
                if (parts.length == 2) {
                    statusChangeRecords = progress.getStatusChangesByType(parts[0].trim(), parts[1].trim());
                } else {
                    statusChangeRecords = progress.getAllStatusChangeRecords();
                }
            } else {
                // 获取所有状态变化记录
                statusChangeRecords = progress.getAllStatusChangeRecords();
            }

            List<StatusChangeRecordResponse> responseList = statusChangeRecords.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取状态变化记录成功: sessionId={}, changeType={}, count={}",
                    sessionId, changeType, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取状态变化记录失败: sessionId={}, changeType={}", sessionId, changeType, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGE_RECORDS_ERROR", "获取状态变化记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取需要重新处理的状态变化记录
     */
    @RequestMapping("perf/task/getStatusChangesNeedingReprocessing")
    public SingleResponse getStatusChangesNeedingReprocessing(String sessionId) {
        log.info("获取需要重新处理的状态变化记录: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> statusChangeRecords = progress.getStatusChangesNeedingReprocessing();

            List<StatusChangeRecordResponse> responseList = statusChangeRecords.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取需要重新处理的状态变化记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取需要重新处理的状态变化记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGES_NEEDING_REPROCESSING_ERROR", "获取状态变化记录失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        // 根据您的系统获取当前用户ID的方式
        // 这里提供一个示例实现
        try {
            return getAccountId(); // 假设您有这个方法
        } catch (Exception e) {
            return "system"; // 默认系统用户
        }
    }

    /**
     * 计算处理速度（记录/秒）
     */
    private double calculateProcessingSpeed(OptimizedBitmapProgress progress) {
        if (progress.getStartTime() == null || progress.getLastUpdateTime() == null) {
            return 0.0;
        }

        long elapsedSeconds = java.time.Duration.between(progress.getStartTime(), progress.getLastUpdateTime()).getSeconds();
        if (elapsedSeconds <= 0) {
            return 0.0;
        }

        return (double) progress.getProcessedCount().get() / elapsedSeconds;
    }

    /**
     * 计算预估剩余时间（秒）
     */
    private long calculateEstimatedRemainingTime(OptimizedBitmapProgress progress, double processingSpeed) {
        if (processingSpeed <= 0) {
            return -1; // 无法估算
        }

        long remainingRecords = progress.getTotalRecords().get() - progress.getProcessedCount().get();
        return (long) (remainingRecords / processingSpeed);
    }

    /**
     * 构建当前位置信息
     */
    private String buildCurrentPosition(OptimizedBitmapProgress progress) {
        return String.format("公司[%s] 第%d页 用户第%d页 索引%d",
                progress.getCurrentCompanyId() != null ? progress.getCurrentCompanyId() : "N/A",
                progress.getCurrentCompanyPage(),
                progress.getCurrentUserPage(),
                progress.getLastProcessedIndex());
    }

    /**
     * 构建失败记录响应
     */
    private FailureRecordResponse buildFailureRecordResponse(OptimizedBitmapProgress.FailureRecord failureRecord) {
        FailureRecordResponse response = new FailureRecordResponse();
        response.setUserId(failureRecord.getUserId());
        response.setCompanyId(failureRecord.getCompanyId());
        response.setGlobalIndex(failureRecord.getGlobalIndex());
        response.setErrorMessage(failureRecord.getErrorMessage());
        response.setErrorType(failureRecord.getErrorType());
        response.setFailureTime(failureRecord.getFailureTime());
        response.setRetryCount(failureRecord.getRetryCount());
        response.setLastRetryTime(failureRecord.getLastRetryTime());
        return response;
    }

    // ==================== 内部响应类 ====================

    /**
     * 迁移状态响应
     */
    public static class MigrationStatusResponse {
        private String sessionId;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private String currentCompanyId;
        private int currentCompanyPage;
        private int currentUserPage;
        private long lastProcessedIndex;
        private java.time.LocalDateTime startTime;
        private java.time.LocalDateTime lastUpdateTime;

        public static MigrationStatusResponseBuilder builder() {
            return new MigrationStatusResponseBuilder();
        }

        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }

        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }

        public long getProcessedCount() { return processedCount; }
        public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }

        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }

        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }

        public String getCurrentCompanyId() { return currentCompanyId; }
        public void setCurrentCompanyId(String currentCompanyId) { this.currentCompanyId = currentCompanyId; }

        public int getCurrentCompanyPage() { return currentCompanyPage; }
        public void setCurrentCompanyPage(int currentCompanyPage) { this.currentCompanyPage = currentCompanyPage; }

        public int getCurrentUserPage() { return currentUserPage; }
        public void setCurrentUserPage(int currentUserPage) { this.currentUserPage = currentUserPage; }

        public long getLastProcessedIndex() { return lastProcessedIndex; }
        public void setLastProcessedIndex(long lastProcessedIndex) { this.lastProcessedIndex = lastProcessedIndex; }

        public java.time.LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(java.time.LocalDateTime startTime) { this.startTime = startTime; }

        public java.time.LocalDateTime getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(java.time.LocalDateTime lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }

        // Builder类
        public static class MigrationStatusResponseBuilder {
            private MigrationStatusResponse response = new MigrationStatusResponse();

            public MigrationStatusResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }

            public MigrationStatusResponseBuilder status(String status) {
                response.status = status;
                return this;
            }

            public MigrationStatusResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }

            public MigrationStatusResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }

            public MigrationStatusResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }

            public MigrationStatusResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }

            public MigrationStatusResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }

            public MigrationStatusResponseBuilder currentCompanyId(String currentCompanyId) {
                response.currentCompanyId = currentCompanyId;
                return this;
            }

            public MigrationStatusResponseBuilder currentCompanyPage(int currentCompanyPage) {
                response.currentCompanyPage = currentCompanyPage;
                return this;
            }

            public MigrationStatusResponseBuilder currentUserPage(int currentUserPage) {
                response.currentUserPage = currentUserPage;
                return this;
            }

            public MigrationStatusResponseBuilder lastProcessedIndex(long lastProcessedIndex) {
                response.lastProcessedIndex = lastProcessedIndex;
                return this;
            }

            public MigrationStatusResponseBuilder startTime(java.time.LocalDateTime startTime) {
                response.startTime = startTime;
                return this;
            }

            public MigrationStatusResponseBuilder lastUpdateTime(java.time.LocalDateTime lastUpdateTime) {
                response.lastUpdateTime = lastUpdateTime;
                return this;
            }

            public MigrationStatusResponse build() {
                return response;
            }
        }
    }

    /**
     * 迁移详细进度响应
     */
    public static class MigrationProgressResponse {
        private String sessionId;
        private String migrationType;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private double processingSpeed;
        private long estimatedRemainingTimeSeconds;
        private String currentPosition;
        private String statistics;

        public static MigrationProgressResponseBuilder builder() {
            return new MigrationProgressResponseBuilder();
        }

        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }

        public String getMigrationType() { return migrationType; }
        public void setMigrationType(String migrationType) { this.migrationType = migrationType; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(double progressPercentage) { this.progressPercentage = progressPercentage; }

        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }

        public long getProcessedCount() { return processedCount; }
        public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }

        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }

        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }

        public double getProcessingSpeed() { return processingSpeed; }
        public void setProcessingSpeed(double processingSpeed) { this.processingSpeed = processingSpeed; }

        public long getEstimatedRemainingTimeSeconds() { return estimatedRemainingTimeSeconds; }
        public void setEstimatedRemainingTimeSeconds(long estimatedRemainingTimeSeconds) { this.estimatedRemainingTimeSeconds = estimatedRemainingTimeSeconds; }

        public String getCurrentPosition() { return currentPosition; }
        public void setCurrentPosition(String currentPosition) { this.currentPosition = currentPosition; }

        public String getStatistics() { return statistics; }
        public void setStatistics(String statistics) { this.statistics = statistics; }

        // Builder类
        public static class MigrationProgressResponseBuilder {
            private MigrationProgressResponse response = new MigrationProgressResponse();

            public MigrationProgressResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }

            public MigrationProgressResponseBuilder migrationType(String migrationType) {
                response.migrationType = migrationType;
                return this;
            }

            public MigrationProgressResponseBuilder status(String status) {
                response.status = status;
                return this;
            }

            public MigrationProgressResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }

            public MigrationProgressResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }

            public MigrationProgressResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }

            public MigrationProgressResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }

            public MigrationProgressResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }

            public MigrationProgressResponseBuilder processingSpeed(double processingSpeed) {
                response.processingSpeed = processingSpeed;
                return this;
            }

            public MigrationProgressResponseBuilder estimatedRemainingTimeSeconds(long estimatedRemainingTimeSeconds) {
                response.estimatedRemainingTimeSeconds = estimatedRemainingTimeSeconds;
                return this;
            }

            public MigrationProgressResponseBuilder currentPosition(String currentPosition) {
                response.currentPosition = currentPosition;
                return this;
            }

            public MigrationProgressResponseBuilder statistics(String statistics) {
                response.statistics = statistics;
                return this;
            }

            public MigrationProgressResponse build() {
                return response;
            }
        }
    }

    /**
     * 失败记录响应
     */
    public static class FailureRecordResponse {
        private String userId;
        private String companyId;
        private long globalIndex;
        private String errorMessage;
        private String errorType;
        private java.time.LocalDateTime failureTime;
        private int retryCount;
        private java.time.LocalDateTime lastRetryTime;

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getCompanyId() { return companyId; }
        public void setCompanyId(String companyId) { this.companyId = companyId; }

        public long getGlobalIndex() { return globalIndex; }
        public void setGlobalIndex(long globalIndex) { this.globalIndex = globalIndex; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public String getErrorType() { return errorType; }
        public void setErrorType(String errorType) { this.errorType = errorType; }

        public java.time.LocalDateTime getFailureTime() { return failureTime; }
        public void setFailureTime(java.time.LocalDateTime failureTime) { this.failureTime = failureTime; }

        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }

        public java.time.LocalDateTime getLastRetryTime() { return lastRetryTime; }
        public void setLastRetryTime(java.time.LocalDateTime lastRetryTime) { this.lastRetryTime = lastRetryTime; }
    }

}
