package com.polaris.kpi.controller.eval.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.domain.task.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.domain.task.entity.TaskBitmapWithLog;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评分人数据迁移服务
 *
 */
@Slf4j
@RestController
public class ScorerDataMingrationController extends AccountBaseController {

    private static final Integer VERSION = 2000000;
    private static final int COMPANY_PAGE_SIZE = 10;
    private static final int USER_PAGE_SIZE = 500;

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private ScorerDataMingrationAppSvc mingrationAppSvc;
    @Autowired
    private TaskBitmapWithLog bitmapWithLog;

    @RequestMapping("perf/task/migrateFinished")
    public SingleResponse migrateFinished() {
        log.info("评分迁移【已完成】任务开始");
        int totalCompaniesProcessed = 0;
        int cPageNo = 1;

        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            if (CollUtil.isEmpty(companies)) {
                log.info("没有更多公司数据");
                break;
            }
            for (Company company : companies) {
                processCompanyFinshed(company.getId(),null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }

        log.info("评分迁移【已完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }


    @RequestMapping("perf/task/migrateFinishedByCompanyId")
    public SingleResponse migrateFinishedByCompanyId(String taskId) {
        log.info("评分迁移【已完成】任务开始");
        String companyId = getCompanyId();
        processCompanyFinshed(companyId,taskId);
        log.info("评分迁移【已完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }
    @RequestMapping("perf/task/migrateNoFinish")
    public SingleResponse migrateNoFinish() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("评分迁移【未完成】任务开始");

        int totalCompaniesProcessed = 0;
        int cPageNo = 1;
        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            for (Company company : companies) {
                processCompanyNoFinish(company.getId(), null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }
        log.info("评分迁移【未完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }

    @RequestMapping("perf/task/migrateNoFinishByCompanyId")
    public SingleResponse migrateNoFinishByCompanyId(String taskId) {
        log.info("评分迁移【未完成】任务开始");
        String companyId = getCompanyId();
        processCompanyNoFinish(companyId,taskId);
        log.info("评分迁移【未完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }

    @RequestMapping("perf/task/migrateFinishedOne")
    public SingleResponse migrateFinishedOne(String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateFinishedOne companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateFinishedOne(companyId, taskUserId);
        log.info("单个评分迁移migrateFinishedOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }


    @RequestMapping("perf/task/migrateNoFinishOne")
    public SingleResponse migrateNoFinishOne( String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateNoFinishOne,companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateNoFinishOne(companyId, taskUserId);
        log.info("单个评分迁移migrateNoFinishOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }

    @RequestMapping("perf/task/retryNoFinishedTasks")
    public SingleResponse retryNoFinishedTasks() {
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务开始");
        List<String> noFinishedTasks = bitmapWithLog.getNoFinishedFailureTasks();
        if (CollUtil.isEmpty(noFinishedTasks)){
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = noFinishedTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageNoFinishedTasks = noFinishedTasks.subList(start, end);
            for (String strId : pageNoFinishedTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateNoFinish(companyId,taskUserId);
            }
        }
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务结束");
        return SingleResponse.of("");
    }


    private void doMigrateNoFinish( String companyId,String taskUserId){
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateNoFinish={}", tid);
            mingrationAppSvc.migrateNoFinish(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【未完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【未完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

    //doMigrateFinished
    @RequestMapping("perf/task/retryFinishedFailureTasks")
    public SingleResponse retryFinishedFailureTasks() {
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务开始");
        List<String> finishedFailureTasks = bitmapWithLog.getFinishedFailureTasks();
        if (CollUtil.isEmpty(finishedFailureTasks)) {
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = finishedFailureTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageFinishedFailureTasks = finishedFailureTasks.subList(start, end);
            for (String strId : pageFinishedFailureTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateFinished(companyId, taskUserId);
            }
        }
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务结束");
        return SingleResponse.of("");
    }
    @RequestMapping("perf/task/logTaskStatus")
    public SingleResponse logTaskStatus() {
        log.info("评分迁移 logTaskStatus任务开始");
        mingrationAppSvc.logTaskStatus();
        log.info("评分迁移 logTaskStatus任务结束");
        return SingleResponse.of("");
    }


    private void processCompanyFinshed(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId, true, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateFinished(companyId, taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }

    private void processCompanyNoFinish(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            //分页查询考核任务未完成的
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId,false, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateNoFinish(companyId,taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }
    private void doMigrateFinished(String companyId, String taskUserId) {
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateFinished={}", tid);
            mingrationAppSvc.migrateFinished(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【已完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【已完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

    // ==================== 新增的优化迁移方法 ====================

    /**
     * 启动优化的大规模数据迁移
     * 支持131万条记录的分批处理和断点续传
     */
    @RequestMapping("perf/task/startOptimizedMigration")
    public SingleResponse startOptimizedMigration(String migrationType) {
        log.info("启动优化迁移任务: type={}", migrationType);
        String tid = MDC.get("tid");
        try {
            String operatorId = getCurrentUserId(); // 获取当前操作人员ID
            String sessionId = mingrationAppSvc.startOptimizedMigration(tid,migrationType, operatorId);

            log.info("优化迁移任务启动成功: sessionId={}", sessionId);
            return SingleResponse.of(sessionId);

        } catch (Exception e) {
            log.error("启动优化迁移任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("MIGRATION_START_FAILED", "启动优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复中断的优化迁移任务
     */
    @RequestMapping("perf/task/resumeOptimizedMigration")
    public SingleResponse resumeOptimizedMigration(String sessionId) {
        log.info("恢复优化迁移任务: sessionId={}", sessionId);
        String tid = MDC.get("tid");
        try {
            boolean success = mingrationAppSvc.resumeOptimizedMigration(tid,sessionId);

            if (success) {
                log.info("优化迁移任务恢复成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务恢复成功");
            } else {
                log.warn("优化迁移任务恢复失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_RESUME_FAILED", "优化迁移任务恢复失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("恢复优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_RESUME_ERROR", "恢复优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停优化迁移任务
     */
    @RequestMapping("perf/task/pauseOptimizedMigration")
    public SingleResponse pauseOptimizedMigration(String sessionId) {
        log.info("暂停优化迁移任务: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.pauseOptimizedMigration(sessionId);

            if (success) {
                log.info("优化迁移任务暂停成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移任务暂停成功");
            } else {
                log.warn("优化迁移任务暂停失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_PAUSE_FAILED", "优化迁移任务暂停失败，请检查任务状态");
            }

        } catch (Exception e) {
            log.error("暂停优化迁移任务失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PAUSE_ERROR", "暂停优化迁移任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移状态
     */
    @RequestMapping("perf/task/getOptimizedMigrationStatus")
    public SingleResponse getOptimizedMigrationStatus(String sessionId) {
        log.debug("获取优化迁移状态: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);

            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "优化迁移任务不存在");
            }

            // 构建状态响应
            MigrationStatusResponse response = MigrationStatusResponse.builder()
                    .sessionId(sessionId)
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .currentCompanyId(progress.getCurrentCompanyId())
                    .currentCompanyPage(progress.getCurrentCompanyPage())
                    .currentUserPage(progress.getCurrentUserPage())
                    .lastProcessedIndex(progress.getLastProcessedIndex())
                    .startTime(progress.getStartTime())
                    .lastUpdateTime(progress.getLastUpdateTime())
                    .build();

            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("获取优化迁移状态失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_STATUS_ERROR", "获取优化迁移状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移详细进度
     */
    @RequestMapping("perf/task/getOptimizedMigrationProgress")
    public SingleResponse getOptimizedMigrationProgress(String sessionId) {
        log.debug("获取优化迁移详细进度: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);

            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "优化迁移任务不存在");
            }

            // 计算处理速度
            double processingSpeed = calculateProcessingSpeed(progress);

            // 计算预估剩余时间
            long estimatedRemainingTime = calculateEstimatedRemainingTime(progress, processingSpeed);

            // 构建详细进度响应
            MigrationProgressResponse response = MigrationProgressResponse.builder()
                    .sessionId(sessionId)
                    .migrationType(progress.getMigrationType())
                    .status(progress.getStatus().name())
                    .progressPercentage(progress.getProgressPercentage())
                    .totalRecords(progress.getTotalRecords().get())
                    .processedCount(progress.getProcessedCount().get())
                    .successCount(progress.getSuccessCount().get())
                    .failureCount(progress.getFailureCount().get())
                    .processingSpeed(processingSpeed)
                    .estimatedRemainingTimeSeconds(estimatedRemainingTime)
                    .currentPosition(buildCurrentPosition(progress))
                    .statistics(progress.getStatistics())
                    .build();

            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("获取优化迁移详细进度失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_PROGRESS_ERROR", "获取优化迁移详细进度失败: " + e.getMessage());
        }
    }

    /**
     * 获取优化迁移统计信息
     */
    @RequestMapping("perf/task/getOptimizedMigrationStatistics")
    public SingleResponse getOptimizedMigrationStatistics(String sessionId) {
        log.debug("获取优化迁移统计信息: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getOptimizedMigrationStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取优化迁移统计信息失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_STATISTICS_ERROR", "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清理优化迁移文件
     */
    @RequestMapping("perf/task/cleanupOptimizedMigrationFiles")
    public SingleResponse cleanupOptimizedMigrationFiles(String sessionId) {
        log.info("清理优化迁移文件: sessionId={}", sessionId);

        try {
            boolean success = mingrationAppSvc.cleanupOptimizedMigrationFiles(sessionId);

            if (success) {
                log.info("优化迁移文件清理成功: sessionId={}", sessionId);
                return SingleResponse.of("优化迁移文件清理成功");
            } else {
                log.warn("优化迁移文件清理失败: sessionId={}", sessionId);
                return SingleResponse.buildFailure("MIGRATION_CLEANUP_FAILED", "优化迁移文件清理失败");
            }

        } catch (Exception e) {
            log.error("清理优化迁移文件失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("MIGRATION_CLEANUP_ERROR", "清理优化迁移文件失败: " + e.getMessage());
        }
    }

    // ==================== 失败记录查询和重试API ====================

    /**
     * 获取失败记录列表
     * 支持按公司ID和错误类型过滤
     */
    @RequestMapping("perf/task/getFailureRecords")
    public SingleResponse getFailureRecords(String sessionId, String companyId, String errorType) {
        log.info("获取失败记录: sessionId={}, companyId={}, errorType={}", sessionId, companyId, errorType);

        try {
            List<OptimizedBitmapProgress.FailureRecord> failureRecords =
                    mingrationAppSvc.getFailureRecords(sessionId, companyId, errorType);

            // 构建响应数据
            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取失败记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取失败记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取失败记录统计信息
     */
    @RequestMapping("perf/task/getFailureStatistics")
    public SingleResponse getFailureStatistics(String sessionId) {
        log.info("获取失败记录统计: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getFailureStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取失败记录统计失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_FAILURE_STATISTICS_ERROR", "获取失败统计失败: " + e.getMessage());
        }
    }

    /**
     * 启动失败记录重试
     * 支持重试所有失败记录或指定用户ID列表
     */
    @RequestMapping("perf/task/startFailureRetry")
    public SingleResponse startFailureRetry(String sessionId, String userIds) {
        log.info("启动失败记录重试: sessionId={}, userIds={}", sessionId, userIds);

        try {
            // 解析用户ID列表
            List<String> userIdList = null;
            if (userIds != null && !userIds.trim().isEmpty()) {
                userIdList = Arrays.stream(userIds.split(","))
                        .map(String::trim)
                        .filter(id -> !id.isEmpty())
                        .collect(Collectors.toList());
            }

            String retrySessionId = mingrationAppSvc.startFailureRetry(sessionId, userIdList);

            log.info("失败记录重试启动成功: originalSessionId={}, retrySessionId={}", sessionId, retrySessionId);
            return SingleResponse.of(retrySessionId);

        } catch (Exception e) {
            log.error("启动失败记录重试失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("START_FAILURE_RETRY_ERROR", "启动失败重试失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID快速定位失败记录
     */
    @RequestMapping("perf/task/getFailureRecordByUserId")
    public SingleResponse getFailureRecordByUserId(String sessionId, String userId) {
        log.info("根据用户ID获取失败记录: sessionId={}, userId={}", sessionId, userId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            OptimizedBitmapProgress.FailureRecord failureRecord = progress.getFailureRecord(userId);
            if (failureRecord == null) {
                return SingleResponse.buildFailure("FAILURE_RECORD_NOT_FOUND", "该用户没有失败记录");
            }

            FailureRecordResponse response = buildFailureRecordResponse(failureRecord);
            return SingleResponse.of(response);

        } catch (Exception e) {
            log.error("根据用户ID获取失败记录失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORD_BY_USERID_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 重试指定用户的失败记录
     */
    @RequestMapping("perf/task/retrySpecificUser")
    public SingleResponse retrySpecificUser(String sessionId, String userId) {
        log.info("重试指定用户: sessionId={}, userId={}", sessionId, userId);

        try {
            List<String> userIdList = Arrays.asList(userId);
            String retrySessionId = mingrationAppSvc.startFailureRetry(sessionId, userIdList);

            log.info("指定用户重试启动成功: originalSessionId={}, userId={}, retrySessionId={}",
                    sessionId, userId, retrySessionId);
            return SingleResponse.of(retrySessionId);

        } catch (Exception e) {
            log.error("重试指定用户失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("RETRY_SPECIFIC_USER_ERROR", "重试指定用户失败: " + e.getMessage());
        }
    }

    /**
     * 根据公司ID获取失败记录
     */
    @RequestMapping("perf/task/getFailureRecordsByCompany")
    public SingleResponse getFailureRecordsByCompany(String sessionId, String companyId) {
        log.info("根据公司ID获取失败记录: sessionId={}, companyId={}", sessionId, companyId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getFailureRecordsByCompany(companyId);

            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("根据公司ID获取失败记录成功: sessionId={}, companyId={}, count={}",
                    sessionId, companyId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("根据公司ID获取失败记录失败: sessionId={}, companyId={}", sessionId, companyId, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_BY_COMPANY_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据错误类型获取失败记录
     */
    @RequestMapping("perf/task/getFailureRecordsByErrorType")
    public SingleResponse getFailureRecordsByErrorType(String sessionId, String errorType) {
        log.info("根据错误类型获取失败记录: sessionId={}, errorType={}", sessionId, errorType);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.FailureRecord> failureRecords = progress.getFailureRecordsByErrorType(errorType);

            List<FailureRecordResponse> responseList = failureRecords.stream()
                    .map(this::buildFailureRecordResponse)
                    .collect(Collectors.toList());

            log.info("根据错误类型获取失败记录成功: sessionId={}, errorType={}, count={}",
                    sessionId, errorType, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("根据错误类型获取失败记录失败: sessionId={}, errorType={}", sessionId, errorType, e);
            return SingleResponse.buildFailure("GET_FAILURE_RECORDS_BY_ERROR_TYPE_ERROR", "获取失败记录失败: " + e.getMessage());
        }
    }

    // ==================== 增量检测和处理API ====================

    /**
     * 启动增量重新处理
     * 处理迁移过程中状态发生变化的数据
     */
    @RequestMapping("perf/task/startIncrementalReprocessing")
    public SingleResponse startIncrementalReprocessing(String sessionId) {
        log.info("启动增量重新处理: sessionId={}", sessionId);

        try {
            String incrementalSessionId = mingrationAppSvc.startIncrementalReprocessing(sessionId);

            log.info("增量重新处理启动成功: originalSessionId={}, incrementalSessionId={}",
                    sessionId, incrementalSessionId);
            return SingleResponse.of(incrementalSessionId);

        } catch (Exception e) {
            log.error("启动增量重新处理失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("START_INCREMENTAL_REPROCESSING_ERROR", "启动增量重新处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态变化统计信息
     */
    @RequestMapping("perf/task/getStatusChangeStatistics")
    public SingleResponse getStatusChangeStatistics(String sessionId) {
        log.info("获取状态变化统计: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getStatusChangeStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取状态变化统计失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGE_STATISTICS_ERROR", "获取状态变化统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态变化记录列表
     */
    @RequestMapping("perf/task/getStatusChangeRecords")
    public SingleResponse getStatusChangeRecords(String sessionId, String changeType) {
        log.info("获取状态变化记录: sessionId={}, changeType={}", sessionId, changeType);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> statusChangeRecords;

            if (changeType != null && !changeType.trim().isEmpty()) {
                // 按变化类型过滤
                String[] parts = changeType.split("->");
                if (parts.length == 2) {
                    statusChangeRecords = progress.getStatusChangesByType(parts[0].trim(), parts[1].trim());
                } else {
                    statusChangeRecords = progress.getAllStatusChangeRecords();
                }
            } else {
                // 获取所有状态变化记录
                statusChangeRecords = progress.getAllStatusChangeRecords();
            }

            List<StatusChangeRecordResponse> responseList = statusChangeRecords.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取状态变化记录成功: sessionId={}, changeType={}, count={}",
                    sessionId, changeType, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取状态变化记录失败: sessionId={}, changeType={}", sessionId, changeType, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGE_RECORDS_ERROR", "获取状态变化记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取需要重新处理的状态变化记录
     */
    @RequestMapping("perf/task/getStatusChangesNeedingReprocessing")
    public SingleResponse getStatusChangesNeedingReprocessing(String sessionId) {
        log.info("获取需要重新处理的状态变化记录: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> statusChangeRecords = progress.getStatusChangesNeedingReprocessing();

            List<StatusChangeRecordResponse> responseList = statusChangeRecords.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取需要重新处理的状态变化记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取需要重新处理的状态变化记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_STATUS_CHANGES_NEEDING_REPROCESSING_ERROR", "获取状态变化记录失败: " + e.getMessage());
        }
    }

    // ==================== 分离式任务管理API ====================

    /**
     * 启动分离式任务
     * 将FINISHED和NO_FINISHED分为两个独立任务执行
     */
    @RequestMapping("perf/task/startSeparatedTasks")
    public SingleResponse startSeparatedTasks(String migrationType) {
        log.info("启动分离式任务: type={}", migrationType);
        String tid = MDC.get("tid");
        try {
            String operatorId = getCurrentUserId();
            String primarySessionId = mingrationAppSvc.startSeparatedTasks(tid,migrationType, operatorId);

            log.info("分离式任务启动成功: primarySessionId={}", primarySessionId);
            return SingleResponse.of(primarySessionId);

        } catch (Exception e) {
            log.error("启动分离式任务失败: type={}", migrationType, e);
            return SingleResponse.buildFailure("START_SEPARATED_TASKS_ERROR", "启动分离式任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取分离式任务状态概览
     */
    @RequestMapping("perf/task/getSeparatedTasksOverview")
    public SingleResponse getSeparatedTasksOverview(String sessionId) {
        log.info("获取分离式任务概览: sessionId={}", sessionId);

        try {
            ScorerDataMingrationAppSvc.SeparatedTasksOverview overview = mingrationAppSvc.getSeparatedTasksOverview(sessionId);

            if (overview == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            return SingleResponse.of(overview);

        } catch (Exception e) {
            log.error("获取分离式任务概览失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_SEPARATED_TASKS_OVERVIEW_ERROR", "获取分离式任务概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取关联任务会话ID
     */
    @RequestMapping("perf/task/getRelatedTaskSessionId")
    public SingleResponse getRelatedTaskSessionId(String sessionId) {
        log.info("获取关联任务会话ID: sessionId={}", sessionId);

        try {
            String relatedSessionId = mingrationAppSvc.getRelatedTaskSessionId(sessionId);

            if (relatedSessionId == null) {
                return SingleResponse.buildFailure("RELATED_TASK_NOT_FOUND", "未找到关联任务");
            }

            return SingleResponse.of(relatedSessionId);

        } catch (Exception e) {
            log.error("获取关联任务会话ID失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_RELATED_TASK_SESSION_ID_ERROR", "获取关联任务会话ID失败: " + e.getMessage());
        }
    }

    /**
     * 启动跨任务增量处理
     * 处理需要在FINISHED和NO_FINISHED任务间转移的状态变化
     */
    @RequestMapping("perf/task/startCrossTaskReprocessing")
    public SingleResponse startCrossTaskReprocessing(String sessionId) {
        log.info("启动跨任务增量处理: sessionId={}", sessionId);

        try {
            String crossTaskSessionId = mingrationAppSvc.startCrossTaskReprocessing(sessionId);

            log.info("跨任务增量处理启动成功: sourceSessionId={}, crossTaskSessionId={}",
                    sessionId, crossTaskSessionId);
            return SingleResponse.of(crossTaskSessionId);

        } catch (Exception e) {
            log.error("启动跨任务增量处理失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("START_CROSS_TASK_REPROCESSING_ERROR", "启动跨任务增量处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取跨任务状态变化记录
     */
    @RequestMapping("perf/task/getCrossTaskStatusChanges")
    public SingleResponse getCrossTaskStatusChanges(String sessionId) {
        log.info("获取跨任务状态变化记录: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> crossTaskChanges = progress.getCrossTaskStatusChanges();

            List<StatusChangeRecordResponse> responseList = crossTaskChanges.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取跨任务状态变化记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取跨任务状态变化记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_CROSS_TASK_STATUS_CHANGES_ERROR", "获取跨任务状态变化记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务内状态变化记录
     */
    @RequestMapping("perf/task/getInTaskStatusChanges")
    public SingleResponse getInTaskStatusChanges(String sessionId) {
        log.info("获取任务内状态变化记录: sessionId={}", sessionId);

        try {
            OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
            if (progress == null) {
                return SingleResponse.buildFailure("MIGRATION_NOT_FOUND", "迁移任务不存在");
            }

            List<OptimizedBitmapProgress.StatusChangeRecord> inTaskChanges = progress.getInTaskStatusChanges();

            List<StatusChangeRecordResponse> responseList = inTaskChanges.stream()
                    .map(this::buildStatusChangeRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取任务内状态变化记录成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取任务内状态变化记录失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_IN_TASK_STATUS_CHANGES_ERROR", "获取任务内状态变化记录失败: " + e.getMessage());
        }
    }

    // ==================== 动态扩展管理API ====================

    /**
     * 手动添加动态用户到正在进行的迁移任务
     */
    @RequestMapping("perf/task/addDynamicUser")
    public SingleResponse addDynamicUser(String sessionId, String userId, String companyId, String userStatus) {
        log.info("添加动态用户: sessionId={}, userId={}, companyId={}, userStatus={}",
                sessionId, userId, companyId, userStatus);

        try {
            boolean success = mingrationAppSvc.addDynamicUser(sessionId, userId, companyId, userStatus);

            if (success) {
                log.info("添加动态用户成功: sessionId={}, userId={}", sessionId, userId);
                return SingleResponse.of("添加成功");
            } else {
                return SingleResponse.buildFailure("ADD_DYNAMIC_USER_FAILED", "添加动态用户失败");
            }

        } catch (Exception e) {
            log.error("添加动态用户失败: sessionId={}, userId={}", sessionId, userId, e);
            return SingleResponse.buildFailure("ADD_DYNAMIC_USER_ERROR", "添加动态用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加动态用户
     */
    @RequestMapping("perf/task/addDynamicUsers")
    public SingleResponse addDynamicUsers(String sessionId, @RequestBody Map<String, Map<String, String>> users) {
        log.info("批量添加动态用户: sessionId={}, userCount={}", sessionId, users.size());

        try {
            int successCount = mingrationAppSvc.addDynamicUsers(sessionId, users);

            log.info("批量添加动态用户成功: sessionId={}, total={}, success={}",
                    sessionId, users.size(), successCount);
            return SingleResponse.of(successCount);

        } catch (Exception e) {
            log.error("批量添加动态用户失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("ADD_DYNAMIC_USERS_ERROR", "批量添加动态用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取动态扩展统计信息
     */
    @RequestMapping("perf/task/getDynamicExpansionStatistics")
    public SingleResponse getDynamicExpansionStatistics(String sessionId) {
        log.info("获取动态扩展统计: sessionId={}", sessionId);

        try {
            String statistics = mingrationAppSvc.getDynamicExpansionStatistics(sessionId);
            return SingleResponse.of(statistics);

        } catch (Exception e) {
            log.error("获取动态扩展统计失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_DYNAMIC_EXPANSION_STATISTICS_ERROR", "获取动态扩展统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取动态用户列表
     */
    @RequestMapping("perf/task/getDynamicUsers")
    public SingleResponse getDynamicUsers(String sessionId, Boolean needsProcessingOnly) {
        log.info("获取动态用户列表: sessionId={}, needsProcessingOnly={}", sessionId, needsProcessingOnly);

        try {
            boolean onlyPending = needsProcessingOnly != null && needsProcessingOnly;
            List<OptimizedBitmapProgress.DynamicUserRecord> dynamicUsers = mingrationAppSvc.getDynamicUsers(sessionId, onlyPending);

            List<DynamicUserRecordResponse> responseList = dynamicUsers.stream()
                    .map(this::buildDynamicUserRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取动态用户列表成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取动态用户列表失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_DYNAMIC_USERS_ERROR", "获取动态用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取待处理的动态用户
     */
    @RequestMapping("perf/task/getPendingDynamicUsers")
    public SingleResponse getPendingDynamicUsers(String sessionId) {
        log.info("获取待处理的动态用户: sessionId={}", sessionId);

        try {
            List<OptimizedBitmapProgress.DynamicUserRecord> pendingUsers = mingrationAppSvc.getDynamicUsers(sessionId, true);

            List<DynamicUserRecordResponse> responseList = pendingUsers.stream()
                    .map(this::buildDynamicUserRecordResponse)
                    .collect(Collectors.toList());

            log.info("获取待处理的动态用户成功: sessionId={}, count={}", sessionId, responseList.size());
            return SingleResponse.of(responseList);

        } catch (Exception e) {
            log.error("获取待处理的动态用户失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("GET_PENDING_DYNAMIC_USERS_ERROR", "获取待处理的动态用户失败: " + e.getMessage());
        }
    }

    // ==================== 调试和诊断API ====================

    /**
     * 检查元数据文件状态（调试用）
     */
    @RequestMapping("perf/task/checkMetadataFile")
    public SingleResponse checkMetadataFile(String sessionId) {
        log.info("检查元数据文件状态: sessionId={}", sessionId);

        try {
            Map<String, Object> result = new HashMap<>();

            // 检查文件路径 - 使用清理后的sessionId
            String cleanedSessionId = cleanSessionId(sessionId);
            String baseDir = System.getProperty("migration.data.dir", getDefaultDataDir());
            String metadataFile = baseDir + "/" + cleanedSessionId + ".metadata";
            java.nio.file.Path metadataPath = java.nio.file.Paths.get(metadataFile);

            result.put("sessionId", sessionId);
            result.put("cleanedSessionId", cleanedSessionId);
            result.put("baseDir", baseDir);
            result.put("metadataFile", metadataFile);
            result.put("fileExists", java.nio.file.Files.exists(metadataPath));

            if (java.nio.file.Files.exists(metadataPath)) {
                try {
                    result.put("fileSize", java.nio.file.Files.size(metadataPath));
                    result.put("lastModified", java.nio.file.Files.getLastModifiedTime(metadataPath).toString());
                    result.put("readable", java.nio.file.Files.isReadable(metadataPath));
                } catch (Exception e) {
                    result.put("fileError", e.getMessage());
                }
            }

            // 检查目录
            java.nio.file.Path parentDir = metadataPath.getParent();
            if (parentDir != null) {
                result.put("dirExists", java.nio.file.Files.exists(parentDir));
                result.put("dirWritable", java.nio.file.Files.isWritable(parentDir));

                if (java.nio.file.Files.exists(parentDir)) {
                    try {
                        java.util.List<String> files = java.nio.file.Files.list(parentDir)
                                .map(java.nio.file.Path::getFileName)
                                .map(java.nio.file.Path::toString)
                                .filter(name -> name.contains(sessionId))
                                .collect(java.util.stream.Collectors.toList());
                        result.put("relatedFiles", files);
                    } catch (Exception e) {
                        result.put("listError", e.getMessage());
                    }
                }
            }

            // 尝试加载元数据
            try {
                OptimizedBitmapProgress progress = mingrationAppSvc.getOptimizedMigrationStatus(sessionId);
                result.put("loadSuccess", progress != null);
                if (progress != null) {
                    result.put("status", progress.getStatus());
                    result.put("totalRecords", progress.getTotalRecords().get());
                    result.put("processedCount", progress.getProcessedCount().get());
                }
            } catch (Exception e) {
                result.put("loadError", e.getMessage());
            }

            return SingleResponse.of(result);

        } catch (Exception e) {
            log.error("检查元数据文件状态失败: sessionId={}", sessionId, e);
            return SingleResponse.buildFailure("CHECK_METADATA_ERROR", "检查元数据文件状态失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        // 根据您的系统获取当前用户ID的方式
        // 这里提供一个示例实现
        try {
            return getEmpId(); // 假设您有这个方法
        } catch (Exception e) {
            return "system"; // 默认系统用户
        }
    }

    /**
     * 计算处理速度（记录/秒）
     */
    private double calculateProcessingSpeed(OptimizedBitmapProgress progress) {
        if (progress.getStartTime() == null || progress.getLastUpdateTime() == null) {
            return 0.0;
        }

        long elapsedSeconds = java.time.Duration.between(progress.getStartTime(), progress.getLastUpdateTime()).getSeconds();
        if (elapsedSeconds <= 0) {
            return 0.0;
        }

        return (double) progress.getProcessedCount().get() / elapsedSeconds;
    }

    /**
     * 计算预估剩余时间（秒）
     */
    private long calculateEstimatedRemainingTime(OptimizedBitmapProgress progress, double processingSpeed) {
        if (processingSpeed <= 0) {
            return -1; // 无法估算
        }

        long remainingRecords = progress.getTotalRecords().get() - progress.getProcessedCount().get();
        return (long) (remainingRecords / processingSpeed);
    }

    /**
     * 构建当前位置信息
     */
    private String buildCurrentPosition(OptimizedBitmapProgress progress) {
        return String.format("公司[%s] 第%d页 用户第%d页 索引%d",
                progress.getCurrentCompanyId() != null ? progress.getCurrentCompanyId() : "N/A",
                progress.getCurrentCompanyPage(),
                progress.getCurrentUserPage(),
                progress.getLastProcessedIndex());
    }

    /**
     * 构建失败记录响应
     */
    private FailureRecordResponse buildFailureRecordResponse(OptimizedBitmapProgress.FailureRecord failureRecord) {
        FailureRecordResponse response = new FailureRecordResponse();
        response.setUserId(failureRecord.getUserId());
        response.setCompanyId(failureRecord.getCompanyId());
        response.setGlobalIndex(failureRecord.getGlobalIndex());
        response.setErrorMessage(failureRecord.getErrorMessage());
        response.setErrorType(failureRecord.getErrorType());
        response.setFailureTime(failureRecord.getFailureTime());
        response.setRetryCount(failureRecord.getRetryCount());
        response.setLastRetryTime(failureRecord.getLastRetryTime());
        return response;
    }

    /**
     * 构建状态变化记录响应
     */
    private StatusChangeRecordResponse buildStatusChangeRecordResponse(OptimizedBitmapProgress.StatusChangeRecord statusChangeRecord) {
        StatusChangeRecordResponse response = new StatusChangeRecordResponse();
        response.setUserId(statusChangeRecord.getUserId());
        response.setCompanyId(statusChangeRecord.getCompanyId());
        response.setOriginalStatus(statusChangeRecord.getOriginalStatus());
        response.setCurrentStatus(statusChangeRecord.getCurrentStatus());
        response.setChangeDetectedTime(statusChangeRecord.getChangeDetectedTime());
        response.setOriginalProcessTime(statusChangeRecord.getOriginalProcessTime());
        response.setNeedsReprocessing(statusChangeRecord.isNeedsReprocessing());
        response.setReprocessCount(statusChangeRecord.getReprocessCount());
        return response;
    }

    /**
     * 构建动态用户记录响应
     */
    private DynamicUserRecordResponse buildDynamicUserRecordResponse(OptimizedBitmapProgress.DynamicUserRecord dynamicUserRecord) {
        DynamicUserRecordResponse response = new DynamicUserRecordResponse();
        response.setUserId(dynamicUserRecord.getUserId());
        response.setCompanyId(dynamicUserRecord.getCompanyId());
        response.setUserStatus(dynamicUserRecord.getUserStatus());
        response.setAddedTime(dynamicUserRecord.getAddedTime());
        response.setDetectedTime(dynamicUserRecord.getDetectedTime());
        response.setNeedsProcessing(dynamicUserRecord.isNeedsProcessing());
        response.setProcessed(dynamicUserRecord.isProcessed());
        response.setProcessedTime(dynamicUserRecord.getProcessedTime());
        response.setAddedBy(dynamicUserRecord.getAddedBy());
        response.setGlobalIndex(dynamicUserRecord.getGlobalIndex());
        return response;
    }

    // ==================== 内部响应类 ====================

    /**
     * 迁移状态响应
     */
    @Getter
    @Setter
    public static class MigrationStatusResponse {
        private String sessionId;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private String currentCompanyId;
        private int currentCompanyPage;
        private int currentUserPage;
        private long lastProcessedIndex;
        private java.time.LocalDateTime startTime;
        private java.time.LocalDateTime lastUpdateTime;

        public static MigrationStatusResponseBuilder builder() {
            return new MigrationStatusResponseBuilder();
        }


        @Getter
        @Setter
        // Builder类
        public static class MigrationStatusResponseBuilder {
            private MigrationStatusResponse response = new MigrationStatusResponse();

            public MigrationStatusResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }

            public MigrationStatusResponseBuilder status(String status) {
                response.status = status;
                return this;
            }

            public MigrationStatusResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }

            public MigrationStatusResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }

            public MigrationStatusResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }

            public MigrationStatusResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }

            public MigrationStatusResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }

            public MigrationStatusResponseBuilder currentCompanyId(String currentCompanyId) {
                response.currentCompanyId = currentCompanyId;
                return this;
            }

            public MigrationStatusResponseBuilder currentCompanyPage(int currentCompanyPage) {
                response.currentCompanyPage = currentCompanyPage;
                return this;
            }

            public MigrationStatusResponseBuilder currentUserPage(int currentUserPage) {
                response.currentUserPage = currentUserPage;
                return this;
            }

            public MigrationStatusResponseBuilder lastProcessedIndex(long lastProcessedIndex) {
                response.lastProcessedIndex = lastProcessedIndex;
                return this;
            }

            public MigrationStatusResponseBuilder startTime(java.time.LocalDateTime startTime) {
                response.startTime = startTime;
                return this;
            }

            public MigrationStatusResponseBuilder lastUpdateTime(java.time.LocalDateTime lastUpdateTime) {
                response.lastUpdateTime = lastUpdateTime;
                return this;
            }

            public MigrationStatusResponse build() {
                return response;
            }
        }
    }

    /**
     * 迁移详细进度响应
     */

    @Getter
    @Setter
    public static class MigrationProgressResponse {
        private String sessionId;
        private String migrationType;
        private String status;
        private double progressPercentage;
        private long totalRecords;
        private long processedCount;
        private long successCount;
        private long failureCount;
        private double processingSpeed;
        private long estimatedRemainingTimeSeconds;
        private String currentPosition;
        private String statistics;

        public static MigrationProgressResponseBuilder builder() {
            return new MigrationProgressResponseBuilder();
        }

        @Getter
        @Setter
        // Builder类
        public static class MigrationProgressResponseBuilder {
            private MigrationProgressResponse response = new MigrationProgressResponse();

            public MigrationProgressResponseBuilder sessionId(String sessionId) {
                response.sessionId = sessionId;
                return this;
            }

            public MigrationProgressResponseBuilder migrationType(String migrationType) {
                response.migrationType = migrationType;
                return this;
            }

            public MigrationProgressResponseBuilder status(String status) {
                response.status = status;
                return this;
            }

            public MigrationProgressResponseBuilder progressPercentage(double progressPercentage) {
                response.progressPercentage = progressPercentage;
                return this;
            }

            public MigrationProgressResponseBuilder totalRecords(long totalRecords) {
                response.totalRecords = totalRecords;
                return this;
            }

            public MigrationProgressResponseBuilder processedCount(long processedCount) {
                response.processedCount = processedCount;
                return this;
            }

            public MigrationProgressResponseBuilder successCount(long successCount) {
                response.successCount = successCount;
                return this;
            }

            public MigrationProgressResponseBuilder failureCount(long failureCount) {
                response.failureCount = failureCount;
                return this;
            }

            public MigrationProgressResponseBuilder processingSpeed(double processingSpeed) {
                response.processingSpeed = processingSpeed;
                return this;
            }

            public MigrationProgressResponseBuilder estimatedRemainingTimeSeconds(long estimatedRemainingTimeSeconds) {
                response.estimatedRemainingTimeSeconds = estimatedRemainingTimeSeconds;
                return this;
            }

            public MigrationProgressResponseBuilder currentPosition(String currentPosition) {
                response.currentPosition = currentPosition;
                return this;
            }

            public MigrationProgressResponseBuilder statistics(String statistics) {
                response.statistics = statistics;
                return this;
            }

            public MigrationProgressResponse build() {
                return response;
            }
        }
    }

    /**
     * 失败记录响应
     */
    @Getter
    @Setter
    @NoArgsConstructor
    public static class FailureRecordResponse {
        private String userId;
        private String companyId;
        private long globalIndex;
        private String errorMessage;
        private String errorType;
        private java.time.LocalDateTime failureTime;
        private int retryCount;
        private java.time.LocalDateTime lastRetryTime;
    }

    /**
     * 状态变化记录响应
     */
    @Getter
    @Setter
    @NoArgsConstructor
    public static class StatusChangeRecordResponse {
        private String userId;
        private String companyId;
        private String originalStatus;
        private String currentStatus;
        private java.time.LocalDateTime changeDetectedTime;
        private java.time.LocalDateTime originalProcessTime;
        private boolean needsReprocessing;
        private int reprocessCount;
    }

    /**
     * 动态用户记录响应
     */
    @Getter
    @Setter
    @NoArgsConstructor
    public static class DynamicUserRecordResponse {
        private String userId;
        private String companyId;
        private String userStatus;
        private java.time.LocalDateTime addedTime;
        private java.time.LocalDateTime detectedTime;
        private boolean needsProcessing;
        private boolean processed;
        private java.time.LocalDateTime processedTime;
        private String addedBy;
        private long globalIndex;
    }

    // ==================== 辅助方法 ====================

    /**
     * 清理sessionId，移除路径分隔符和特殊字符
     */
    private String cleanSessionId(String sessionId) {
        if (sessionId == null) {
            return null;
        }

        // 移除路径分隔符和特殊字符，只保留字母、数字、下划线和连字符
        String cleaned = sessionId.replaceAll("[/\\\\:*?\"<>|]", "_");

        // 如果sessionId看起来像URL路径，提取最后一部分
        if (cleaned.contains("_") && cleaned.length() > 50) {
            String[] parts = cleaned.split("_");
            // 查找包含时间戳的部分（通常是最后几部分）
            for (int i = parts.length - 1; i >= 0; i--) {
                // 检查是否包含时间戳（13位数字）或十六进制ID（8-12位字母数字）
                if (parts[i].matches(".*\\d{13}.*") || parts[i].matches(".*[a-f0-9]{8,12}.*")) {
                    // 从这部分开始重新组合
                    StringBuilder result = new StringBuilder();
                    for (int j = i; j < parts.length; j++) {
                        if (result.length() > 0) result.append("_");
                        result.append(parts[j]);
                    }
                    cleaned = result.toString();
                    break;
                }
            }
        }

        return cleaned;
    }

    /**
     * 获取默认数据目录，兼容不同操作系统
     */
    private String getDefaultDataDir() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            // Windows系统使用临时目录
            return System.getProperty("java.io.tmpdir") + "migration";
        } else {
            // Unix/Linux系统使用/tmp
            return "/tmp/migration";
        }
    }
}
