package com.polaris.kpi.controller.eval.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.perf.www.common.web.AccountBaseController;
import com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc;
import com.polaris.kpi.eval.domain.task.entity.TaskBitmapWithLog;
import com.polaris.kpi.eval.domain.migration.entity.OptimizedBitmapProgress;
import com.polaris.kpi.eval.infr.task.dao.TaskUserDao;
import com.polaris.kpi.org.domain.dept.entity.Company;
import com.polaris.kpi.org.infr.company.dao.CompanyDaoImpl;
import com.polaris.sdk.type.TenantId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lufei.ibatis.mapper.PagedList;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 评分人数据迁移服务
 *
 */
@Slf4j
@RestController
public class ScorerDataMingrationController extends AccountBaseController {

    private static final Integer VERSION = 2000000;
    private static final int COMPANY_PAGE_SIZE = 10;
    private static final int USER_PAGE_SIZE = 500;

    @Autowired
    private CompanyDaoImpl companyDao;
    @Autowired
    private TaskUserDao taskUserDao;
    @Autowired
    private ScorerDataMingrationAppSvc mingrationAppSvc;
    @Autowired
    private TaskBitmapWithLog bitmapWithLog;

    @RequestMapping("perf/task/migrateFinished")
    public SingleResponse migrateFinished() {
        log.info("评分迁移【已完成】任务开始");
        int totalCompaniesProcessed = 0;
        int cPageNo = 1;

        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            if (CollUtil.isEmpty(companies)) {
                log.info("没有更多公司数据");
                break;
            }
            for (Company company : companies) {
                processCompanyFinshed(company.getId(),null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }

        log.info("评分迁移【已完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }


    @RequestMapping("perf/task/migrateFinishedByCompanyId")
    public SingleResponse migrateFinishedByCompanyId(String taskId) {
        log.info("评分迁移【已完成】任务开始");
        String companyId = getCompanyId();
        processCompanyFinshed(companyId,taskId);
        log.info("评分迁移【已完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }
    @RequestMapping("perf/task/migrateNoFinish")
    public SingleResponse migrateNoFinish() {
        MDC.put("tid", IdUtil.fastSimpleUUID());
        log.info("评分迁移【未完成】任务开始");

        int totalCompaniesProcessed = 0;
        int cPageNo = 1;
        while (true) {
            //分页查询V2所有公司列表
            PagedList<Company> companies = companyDao.pagedAllCompanyV2(VERSION, cPageNo, COMPANY_PAGE_SIZE);
            for (Company company : companies) {
                processCompanyNoFinish(company.getId(), null);
            }
            totalCompaniesProcessed += companies.size();
            if (companies.size() < COMPANY_PAGE_SIZE) {
                break;
            }
            cPageNo++;
        }
        log.info("评分迁移【未完成】任务结束");
        MDC.clear();
        return SingleResponse.of(totalCompaniesProcessed);
    }

    @RequestMapping("perf/task/migrateNoFinishByCompanyId")
    public SingleResponse migrateNoFinishByCompanyId(String taskId) {
        log.info("评分迁移【未完成】任务开始");
        String companyId = getCompanyId();
        processCompanyNoFinish(companyId,taskId);
        log.info("评分迁移【未完成】任务结束companyId:{}", companyId);
        MDC.clear();
        return SingleResponse.of(1);
    }

    @RequestMapping("perf/task/migrateFinishedOne")
    public SingleResponse migrateFinishedOne(String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateFinishedOne companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateFinishedOne(companyId, taskUserId);
        log.info("单个评分迁移migrateFinishedOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }


    @RequestMapping("perf/task/migrateNoFinishOne")
    public SingleResponse migrateNoFinishOne( String taskUserId) {
        String companyId = getCompanyId();
        log.info("单个评分迁移开始 migrateNoFinishOne,companyId:{},taskUserId:{}", companyId, taskUserId);
        mingrationAppSvc.migrateNoFinishOne(companyId, taskUserId);
        log.info("单个评分迁移migrateNoFinishOne【已完成】任务结束！！！");
        return SingleResponse.of("");
    }

    @RequestMapping("perf/task/retryNoFinishedTasks")
    public SingleResponse retryNoFinishedTasks() {
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务开始");
        List<String> noFinishedTasks = bitmapWithLog.getNoFinishedFailureTasks();
        if (CollUtil.isEmpty(noFinishedTasks)){
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = noFinishedTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageNoFinishedTasks = noFinishedTasks.subList(start, end);
            for (String strId : pageNoFinishedTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateNoFinish(companyId,taskUserId);
            }
        }
        log.info("评分迁移【未完成】失败的重试 retryNoFinishedTasks任务结束");
        return SingleResponse.of("");
    }


    private void doMigrateNoFinish( String companyId,String taskUserId){
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateNoFinish={}", tid);
            mingrationAppSvc.migrateNoFinish(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【未完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【未完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

    //doMigrateFinished
    @RequestMapping("perf/task/retryFinishedFailureTasks")
    public SingleResponse retryFinishedFailureTasks() {
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务开始");
        List<String> finishedFailureTasks = bitmapWithLog.getFinishedFailureTasks();
        if (CollUtil.isEmpty(finishedFailureTasks)) {
            return SingleResponse.of("");
        }
        int pageSize = 500;
        int totalSize = finishedFailureTasks.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize;
        for (int i = 0; i < pageCount; i++) {
            int start = i * pageSize;
            int end = Math.min(start + pageSize, totalSize);
            List<String> pageFinishedFailureTasks = finishedFailureTasks.subList(start, end);
            for (String strId : pageFinishedFailureTasks) {
                String[] parts = strId.split("_");
                if (parts.length != 2) {
                    log.warn("Invalid task key format: {}", strId);
                    continue;
                }
                String companyId = parts[0];
                String taskUserId = parts[1];
                doMigrateFinished(companyId, taskUserId);
            }
        }
        log.info("评分迁移【已完成】失败的重试 retryFinishedFailureTasks任务结束");
        return SingleResponse.of("");
    }
    @RequestMapping("perf/task/logTaskStatus")
    public SingleResponse logTaskStatus() {
        log.info("评分迁移 logTaskStatus任务开始");
        mingrationAppSvc.logTaskStatus();
        log.info("评分迁移 logTaskStatus任务结束");
        return SingleResponse.of("");
    }


    private void processCompanyFinshed(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId, true, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateFinished(companyId, taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }

    private void processCompanyNoFinish(String companyId, String taskId) {
        int uPageNo = 1;
        while (true) {
            //分页查询考核任务未完成的
            PagedList<String> taskUserIds = taskUserDao.pagedFinishedTaskUser(companyId, taskId,false, uPageNo, USER_PAGE_SIZE);
            for (String taskUserId : taskUserIds) {
                doMigrateNoFinish(companyId,taskUserId);
            }
            if (taskUserIds.size() < USER_PAGE_SIZE) {
                break;
            }
            uPageNo++;
        }
    }
    private void doMigrateFinished(String companyId, String taskUserId) {
        String tid = IdUtil.simpleUUID() + "_" + companyId + "_" + taskUserId;
        MDC.put("tid", tid);
        try {
            log.debug("migrateFinished={}", tid);
            mingrationAppSvc.migrateFinished(tid, companyId, taskUserId);
        } catch (DataIntegrityViolationException e) {
            log.error("评分迁移【已完成】任务冲突,公司:{}", companyId);
        } catch (Exception e) {
            log.error("评分迁移【已完成】任务出错,公司:{}, error:{}", companyId, e.getMessage(), e);
        } finally {
            MDC.clear();
        }
    }

}
