# 优化迁移配置文件
# 用于配置大规模数据迁移的相关参数

# 评分数据迁移配置
scorer:
  migration:
    # 是否启用位图优化（默认关闭，需要手动开启）
    use-bitmap: false
    
    # 位图配置
    bitmap:
      # 段大小（每段记录数）
      segment-size: 100000
      
      # 最大缓存段数
      max-cached-segments: 5
      
      # 数据目录
      data-dir: /tmp/migration
      
      # 自动清理过期文件（天）
      cleanup-days: 7
    
    # 分页配置
    pagination:
      # 公司页大小
      company-page-size: 10
      
      # 用户页大小
      user-page-size: 1000
      
      # 批量处理大小
      batch-size: 500
    
    # 性能配置
    performance:
      # 内存阈值（MB）
      memory-threshold-mb: 300
      
      # 进度保存间隔（页数）
      progress-save-interval: 5
      
      # 用户页保存间隔
      user-page-save-interval: 10
      
      # 处理超时时间（分钟）
      processing-timeout-minutes: 120
    
    # 重试配置
    retry:
      # 最大重试次数
      max-attempts: 3

      # 重试间隔（秒）
      retry-interval-seconds: 5

      # 指数退避因子
      backoff-multiplier: 2.0

      # 自动重试开关
      auto-retry-enabled: true

      # 自动重试间隔（分钟）
      auto-retry-interval-minutes: 60

      # 自动重试的错误类型
      auto-retry-error-types: TIMEOUT_ERROR,DATABASE_ERROR

    # 失败记录跟踪配置
    failure-tracking:
      # 是否启用失败记录跟踪
      enabled: true

      # 失败记录保留天数
      retention-days: 30

      # 失败率告警阈值（百分比）
      failure-rate-alert-threshold: 5.0

      # 单个用户最大重试次数
      max-retry-per-user: 5

    # 增量检测配置
    incremental:
      # 是否启用增量检测
      enabled: false

      # 增量检测间隔（分钟）
      check-interval-minutes: 30

      # 状态变化检测方式
      detection-mode: POLLING  # POLLING(轮询) / EVENT(事件驱动)

      # 状态变化保留天数
      change-retention-days: 7

      # 自动重新处理开关
      auto-reprocess-enabled: true

      # 自动重新处理的变化类型
      auto-reprocess-change-types: FINISHED->NO_FINISHED,NO_FINISHED->FINISHED

    # 任务分离配置
    task-separation:
      # 是否启用任务分离（推荐开启）
      enabled: true

      # 任务协调模式
      coordination-mode: AUTO  # AUTO(自动协调) / MANUAL(手动协调)

      # 跨任务处理开关
      cross-task-processing-enabled: true

      # 跨任务处理延迟（分钟）
      cross-task-processing-delay-minutes: 5

      # 任务间状态同步间隔（分钟）
      task-sync-interval-minutes: 10

# 线程池配置
spring:
  task:
    execution:
      pool:
        # 迁移任务线程池
        migration:
          core-size: 2
          max-size: 4
          queue-capacity: 10
          keep-alive: 60s
          thread-name-prefix: "Migration-"
          allow-core-thread-timeout: true

# 日志配置
logging:
  level:
    com.polaris.kpi.eval.app.task.appsvc.ScorerDataMingrationAppSvc: INFO
    com.polaris.kpi.eval.domain.migration: DEBUG
    com.polaris.kpi.eval.infr.task.repimpl.OnScoreEvalRepoImpl: INFO
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] - %msg%n"
  
  file:
    name: logs/migration.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,migration
  
  endpoint:
    health:
      show-details: always
    
    # 自定义迁移监控端点
    migration:
      enabled: true

# 数据库配置（如果需要单独配置）
migration:
  datasource:
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
    
    # 查询超时配置
    query-timeout: 30
    
    # 批量操作配置
    batch:
      size: 1000
      timeout: 60

# JVM配置建议（在启动参数中设置）
# -Xms2g -Xmx4g
# -XX:+UseG1GC
# -XX:MaxGCPauseMillis=200
# -XX:+PrintGCDetails
# -XX:+PrintGCTimeStamps
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=/tmp/migration-heapdump.hprof
