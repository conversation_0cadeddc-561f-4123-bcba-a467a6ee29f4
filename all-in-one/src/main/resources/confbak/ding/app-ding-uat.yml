#阿里云oss
aliyun:
  oss:
    access:
      keyId: LTAI5t3iEAY1ZVk3
      keySecret: vo3B2CF7wdRrRYfLYPsKA3LhEBBTnj
    #uat
    #endpoint: http://oss-cn-beijing-internal.aliyuncs.com # 正式环境只能internal内网访问
    #bucketName: zxb-online
    #defaultAvatar: http://zxb-online.oss-cn-beijing.aliyuncs.com/default.jpg
    #imageOssHost: https://zxb-online.topscrm.cn/
    #prod
    endpoint: https://topscrm.oss-cn-hangzhou-internal.aliyuncs.com # 正式环境只能internal内网访问
    bucketName: topscrm
    defaultAvatar: http://zxb-online.oss-cn-beijing.aliyuncs.com/default.jpg
    imageOssHost: https://topscrm.oss-cn-hangzhou.aliyuncs.com/

# 信息安全
security:
  csrf:
    enable: false
    excludes:
plat: ding

spring:
  profiles:
    active: uat
  datasource:
    company:
      url: *************************************************************************************************************************************************
      username: root
      password: Kpi#uat@2024
      hikari:
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 最小空闲连接数量
        minimum-idle: 5
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 180000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 10
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: MyHikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 1800000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 30000
        connection-test-query: SELECT 1

    global:
      url: *************************************************************************************************************************
      username: root
      password: Kpi#uat@2024
      hikari:
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 最小空闲连接数量
        minimum-idle: 5
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 180000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 10
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: MyHikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 1800000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 30000
        connection-test-query: SELECT 1

    ding:
      url: *****************************************************************************************************************************
      username: root
      password: Kpi#uat@2024
      hikari:
        driver-class-name: com.mysql.cj.jdbc.Driver
        # 最小空闲连接数量
        minimum-idle: 5
        # 空闲连接存活最大时间，默认600000（10分钟）
        idle-timeout: 180000
        # 连接池最大连接数，默认是10
        maximum-pool-size: 10
        # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
        auto-commit: true
        # 连接池名称
        pool-name: MyHikariCP
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
        max-lifetime: 1800000
        # 数据库连接超时时间,默认30秒，即30000
        connection-timeout: 30000
        connection-test-query: SELECT 1
  security:
    user:
      name: admin
      password: admin
  redis:
    host: *************
    port: 6379
    password: clota
    timeout: 5000
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 1000
        #连接池最大阻塞等待时间（使用负值表示没有限制））
        max-wait: 10000
        #连接池中的最大空闲连接
        max-idle: 200
        #连接池中的最小空闲连接
        min-idle: 50
        #每ms运行一次空闲连接回收器（独立线程）
        time-between-eviction-runs: 100000
#钉钉配置
ding:
  appId: 58907
  suiteKey: suite2ieot7xfpvqyoqv8
  suiteSecret: i_o9tVDCl1qwTF4MIQILdg12M_CogwqjDUdk_-9YyYUMIz4cwt0kN0u7EN8oQNHi
  SSOSecret:
  goodsCode:
  freeSkuCode: DT_GOODS_881603867765821_836012 #  免费sku 和线上一样
  trySkuCode: DT_GOODS_881603867765821_212006 # 试用sku 和线上一样
  simpleSkuCode: DT_GOODS_881603867765821_212005 #  标准版sku 和线上一样
  enterpriseSkuCode: DT_GOODS_881603867765821_212006 # 企业版本
  service:
    token: 2bc90f577c12bdb3572073e04e3b033a
  inAppCourseCode:
  inAppCallBackPage:
  hrm:
    tenantId: 28
    accessKey: 052c5e0a4202373488dca14996f3f2e2
ext:
  sign:
    url: http://kpiuat.pekhr.com:8084/v1/index.html
host:
  url: http://kpiuat.pekhr.com:8084/
  todoUrlPrefix: '#/login?toRouteName=taskDetail&'
  version: v1
#global数据库表名
database:
  global: global_perf


# 评分历史数据迁移核心配置
scorer:
  migration:
    use-bitmap: true                    # 启用位图优化（核心功能）
    task-separation:
      enabled: true       # 启用任务分离（性能提升）
    incremental:
      enabled: true           # 启用增量检测（状态变化处理）
      check-interval-minutes: 10  #开发环境：更频繁的检测 ; 生产环境：更保守的配置
     # check-interval-minutes: 60 # 生产环境：更保守的配置
    failure-tracking:
      enabled: true      # 启用失败跟踪（问题定位）
      #retention-days: 7  # 生产环境：更保守的配置
    performance:
        company-page-size: 50          # 公司分页大小（减少内存使用）
        user-page-size: 500            # 用户分页大小
        max-concurrent-tasks: 2        # 最大并发任务数

#OKR接口地址
okr:
  host: http://kpiuat.pekhr.com:9000/okrApi/  #  https://demo1.topscrm.cn:8443/
#  host: http://demo1.topscrm.cn:8091/
  dingOkrHost: https://dingokr.eapps.dingtalkcloud.com/
  # host: http://app37766.eapps.dingtalkcloud.com/
  accessKey: 14006174250411491330
  accessSecret: b5d1373a5ae411ebb7d10050568a58a6
  dingAppId: 41774
  domain: https://demo1.topscrm.cn:8443/

down:
  url: https://topscrm.oss-cn-hangzhou.aliyuncs.com
monitoring:
  alarm:
    open: false
    robotToken: f6c8ab3058af62a24f6fa20f62e68d136e490025863f954059775bbd31789a6a
kpi:
  msg:
    tmps: #消息模板配置
      new_emp_eval: fd77a3d5d99843628d19541a8ea8c55b #1-37-请发起新人考核
      create_task: 49e30cc9a3df4a57b3cb9ba7002ba982 #1-1您收到新的考核任务
      task_enact: 1ef0162d0d2f4e74b594ffa835c31cda #1-81请进行指标制定
      task_confirm: 5f8703fd2d0741dfa9cb420f533a512c #1-30请进行指标确认
      task_confirm_audit: 6043ae82bc37403eaddc9091cbea3b8c #1-54请进行指标审核
      task_confirm_reject: a76227b1c2f440a2b04619d008001d5f #1-55指标审核被驳回
      user_enable_apply: 663715f4d39445cea38da40f4b76bfb5 #1-13您收到一条开通应用权限的申请
      urging_user_enable_apply: 9daad6e6fae54b47b5da20788f21d492 #1-14催办_申请使用权限
      user_enable_pass: 4734e682a8d2461f9b3778168354c69e #1-25申请使用结果通知
      user_enable_reject: 4734e682a8d2461f9b3778168354c69e #1-25申请使用结果通知
      item_audit_reject: 0ad5bee6c243460085db6520701fac59 #1-29指标确认考核指标审核结果
      task_submit_progress: b3e4bc2087ed4de5b26c8180289c5781 #1-3请提交完成值
      batch_submit_progress: a7f5496c73a644d485caf79d1d54d5fc #1-32批量录入指标完成值
      change_item_audit: f821aa663c1d439c8d0f4d757580bc47 #1-35请进行指标变更审核
      change_item_reject: 6fd22379b5fe4a8088ea1da751062472 #1-56指标变更被驳回
      item_notice: b3e4bc2087ed4de5b26c8180289c5781 #1-3请提交完成值
      transfer_task: 189dd98250c34e3e86bbb66c863912bf #1-4您收到一条转交的考核任务
      change_item_success: 1d211392e17140d29b20ef3c0401076f #已删除_1-5您有考核任务正在变更
      task_wait_self_score: 320383b76c754bbbbb2682ed6f9eb4e2 #1-31请完成自评
      task_wait_help_score: 8ca0577a2e114d75910f93de66da0abe #1-32请提交协同人评分
      task_all_score: 8ca0577a2e114d75910f93de66da0abe #1-32请提交协同人评分
      task_result_audit: bfb14e7cdb30463cb78e2d0fd01df269 #1-33请进行结果校准
      task_result_collect_audit: 1d01a14febc7482ca3b6e1d30cf5f3e1 #1-79请进行绩效校准
      task_result_affirm: 2ef352ac088d4ad2bdbaecb99803c03c # 1-7考核任务进入结果确认阶段
      task_wait_public: 58a7c7e17bce4f8c9661e9288afbefed #1-8您收到一条待公示的考核任务
      task_result_appeal: ba6ff7ed1b91481aa26012bfa3711bc4 #1-9您收到一条待处理的申诉
      reset_task: 351f3bd82e9c4839972bbc61e2be4e6b #1-10您的考核任务已被重置
      task_stopped: 1b9194a1cdd04b37be688d852f6e2a3d #1-11您的考核任务被终止
      urging: 6c2224ad38704cad9cd83be789d2a665 #1-12您收到一条催办
      urging_task: 825668b035ea43b7a76acde528b01cbf #1-12-1请尽快
      task_scorer_miss: 00195d4981e6488fa474cbe31316bbf3 #1-15责任人空缺
      task_finished: 605ba6d9afdd481e9ba32bc71834a16f #1-16考核任务已完成
      result_appeal_handled: 5c55e5002bd44e7385c7325d293003e8 #1-17 结果申诉已处理完毕
      task_has_publican: bc5dfae2d107445b83378df7b52fb794 #1-26考核结果公示
      item_progress_update: 336eacd838aa4e648f9ff5f236b83dd3 #1-28指标更新
      set_mutual_audit: fa0a6bfe15f649778f8808b2c4839f49 #1-34考核人设定互评人
      simple_open: b8e981f4a6da4d5b86a442aee80f526e #1-18开通北极星绩效试用版
      enterprise_open: dc0bbb414afc4e2bb44a3e115fb29f4a #1-20开通北极星绩效企业版
      enterprise_renewal: c68e3f4830a44d44984c315fcad2be23 #1-21正式版续费
      simple_3d: 7f84b21a746d4df8abd87693f61b1cb6 #1-22使用3天
      enterprise_rest_10d: f2a0b3192a9743e7866e2b1977a56ec8 #1-23正式版剩余10天
      enterprise_expire: b771ebc074ef429789bbb1cb74ac74b3 #1-24企业版到期
      task_coach_created: a6177be2432241dcaa077fd368283501 #1-36面谈辅导通知
      onBardingEmp: fd77a3d5d99843628d19541a8ea8c55b #1-37-请发起新人考核
      notice_person: daef3576b4ac47188b707a704a40185b #通知_请查看_员工
      notice_admin: 7012f15c66b941b5ad4b90658dfb5527 #通知_管理员
      apply_menu_msg: 9afb468301804bd9b4a51f24c72c4bbf #1-50您收到一条新人考核功能的开通申请
      scoring_time_out_to_rater: a84ae1c516ff41c58f8f5c1c85456038 #1-51评分超时提醒评分人-单次
      scoring_time_out_to_admin: 54c788e9cd3042f694c838f46615a36d #1-52评分超时提醒管理员
      eval_ref_result_public: 0366bd6e1d4f464d9d3d69c816e21cdc #1-53考核关联绩效结果公布
      ind_score_item_input: de1e4ff14a814455b2ac930d521b2132 #1-58指标评分通知
      ind_score_item_changed: 855d57642ae6453a800adfa1f596dcc6 #1-59指标评分修改通知
      finish_value_audit: 3a7db8c5cc1a46458437238e4c9d7109 #1-57完成值审核
      eval_review_inform: 70f9df8ec3b54c74b786f5dc24d05a15 #1-60评论通知
      reject_audit_result: f2cf1231bd90463b82d7fd23d0919675 #1-61绩效校准被驳回
      reject_to_score_stage: 01748c7d740e45a5bf0670999c242469 #1-62校准中绩效评分被驳回
      task_interview_excute: 40978441e3bf4d0ebbf2df57078c3f46 #1-82绩效面谈执行
      task_interview_input: 0654982fc4754ee2a37fdc6471bc8019 #1-83绩效面谈录入
      task_interview_confirm: a59926a8392541f4897a7e86891520ce #1-85绩效面谈确认
      task_interview_appointment: ba13bc6dc28041668af5c90802ce8929 #1-84绩效面谈预约被考核人
      task_interview_transfer_input: bb9dca1cfde04956a39a14ffb9bb75e8 #1-86绩效面谈录入转交
      invite_peer_audit: 84025ab44ff04de5be936da46c09af77 #1-93 邀请同级评分人后审核
      invite_sub_audit: b763551619bd4d139b6f9754d7129837 #1-94 邀请下级评分人后审核
      reject_finish_value: 2f7706da041d4fda9a7df5e1486e0e7c # 1-96 完成值被驳回-通知完成值录入人
      task_confirm_finish: a5dde894d0014b03a05492fa368f36ad #1-97 指标确认完成后通知人员
      finish_value_changed: 966a0e3ea00448899cbefc87b9e8a488 #1-98 完成值录入_提交变更后通知人员

ask360:
  msg:
    scorerMsgTempId: 1d7155a745fb4f6794f2097718198302
pip:
  msg:
    tmps: #消息模板配置
      goal_created: 4877bcbb5b7349229a49d005f0b93ef4 #1-88pip目标制定通知
      goal_confirm: 4e2ad103d2f143b697692e604c46ec39 #1-89pip目标确认通知
      finish_input_value: 8be55efef44b418bb8d7c88a55e302e4 #1-90pip完成情况录入通知
      score: a750f4d7d65740a89359c5a95bb86051 #1-91pip改进评价通知
      result_affirm: c149ec43e9a649b2a95dfcd1b7811b62 #1-92pip改进结果确认通知