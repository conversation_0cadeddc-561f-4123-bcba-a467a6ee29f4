# perf-server

绩效考核系统服务端

## 快速启动指南

### 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 5.7+
- Redis 3.0+

### 启动步骤
1. 克隆项目：`git clone <repository-url>`
2. 配置数据库连接
3. 执行：`mvn clean install`
4. 启动：`java -jar target/perf-server.jar`

## 核心功能

### 1. 绩效考核管理
- 360度考核流程
- 自定义考核模板
- 多维度评分体系
- 考核结果统计分析

### 2. 大规模数据迁移 🆕
- **支持131万条记录**的大规模数据迁移
- **分批处理**：二级分页策略（公司->用户）
- **断点续传**：中断后可从上次位置继续
- **内存安全**：实时监控，防止OOM
- **实时监控**：进度跟踪和性能指标

#### 数据迁移特性
- **内存需求**：131万条50字符记录约需300-400MB内存
- **批次配置**：支持默认、高性能、保守三种模式
- **API接口**：完整的REST API支持任务管理
- **异步执行**：专用线程池，不阻塞主业务

#### 使用示例
```bash
# 启动迁移任务
POST /api/migration/start
{
  "migrationType": "FINISHED",
  "operatorId": "admin",
  "userPageSize": 1000
}

# 查询进度
GET /api/migration/status/{sessionId}

# 恢复中断任务
POST /api/migration/resume/{sessionId}
```

#### 配置模板
- **默认配置**：公司页10，用户页1000 - 适合一般环境
- **高性能配置**：公司页20，用户页2000 - 内存充足环境
- **保守配置**：公司页5，用户页200 - 内存受限环境

## 项目结构

```
perf-server/
├── perf/
│   ├── perf-web/          # Web层 - 控制器和API
│   ├── perf-biz/          # 应用层 - 业务服务
│   ├── perf-domain/       # 领域层 - 核心业务逻辑
│   ├── perf-infrastructure/ # 基础设施层 - 数据访问
│   └── perf-api/          # API定义
├── @Docs/                 # 项目文档
│   ├── Feature/           # 功能设计文档
│   ├── DevLog/           # 开发日志
│   └── AskLog/           # 提问记录
└── all-in-one/           # 打包模块
```

## 技术栈

- **框架**：Spring Boot, Spring MVC
- **数据库**：MySQL, MyBatis
- **缓存**：Redis
- **消息队列**：RabbitMQ
- **监控**：Micrometer, Prometheus
- **文档**：Swagger/OpenAPI

## 开发规范

### 代码规范
- 遵循Clean Code原则
- 应用SOLID设计原则
- 使用JSDoc注释
- 遵循DRY原则

### 架构原则
- 分层架构：Web -> Application -> Domain -> Infrastructure
- 依赖倒置：高层不依赖低层实现
- 单一职责：每个类只负责一个职责
- 开闭原则：对扩展开放，对修改关闭

## 性能优化建议

### 数据迁移优化
1. **内存配置**：JVM堆内存建议至少1GB
2. **批次调优**：根据可用内存调整批次大小
3. **并发控制**：合理配置线程池参数
4. **监控告警**：设置内存和性能监控

### 数据库优化
1. **连接池**：合理配置数据库连接池
2. **索引优化**：为查询字段建立合适索引
3. **分页查询**：使用limit分页避免全表扫描
4. **批量操作**：使用批量插入/更新提高效率

## 监控和运维

### 关键指标
- **迁移进度**：处理百分比、剩余时间
- **性能指标**：处理速度、内存使用率
- **错误统计**：失败率、重试次数
- **系统资源**：CPU、内存、磁盘使用情况

### 告警配置
- 内存使用率超过80%
- 迁移任务失败率超过5%
- 长时间运行任务（超过预期时间）
- 系统异常和错误

## 故障处理

### 常见问题
1. **内存不足**：降低批次大小，增加JVM内存
2. **网络中断**：使用断点续传功能恢复任务
3. **数据库连接**：检查连接池配置和数据库状态
4. **任务卡死**：检查线程池状态，必要时重启

### 恢复步骤
1. 查看错误日志确定问题原因
2. 修复问题后使用resume接口恢复任务
3. 监控任务执行状态确保正常运行
4. 记录问题和解决方案供后续参考

## 版本历史

### v2.1.0 (2025-06-29)
- ✨ 新增大规模数据迁移功能
- ✨ 支持131万条记录分批处理
- ✨ 实现断点续传机制
- ✨ 添加内存监控和性能优化
- 📝 完善项目文档和使用指南

### v2.0.0
- 🎉 绩效考核系统核心功能
- 🔧 360度考核流程
- 📊 多维度评分体系

## 贡献指南

1. Fork 项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -m 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者：开发团队
- 技术支持：通过Issue提交问题
- 文档更新：2025-06-29
